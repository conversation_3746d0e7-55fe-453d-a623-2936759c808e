namespace BCP.Core.Email.Translations;

public class FrenchTranslation : IEmailTranslation
{
    // Notifications
    public string Notification_Footer_Cta => "veuillez cliquer ici";
    public string Notification_Footer_Short_Cta => "cliquer ici";
    public string Notification_Footer_Note =>
        "Vous recevez ce courriel parce qu’un compte client de BDO est associé à votre adresse électronique. Si vous souhaitez modifier vos préférences relatives aux avis, ";

    // PortalInvite
    public string PortalInvite_Title =>
        "Bienvenue dans votre espace de travail sur le Portail des clients BDO";
    public string PortalInvite_Body =>
        "Nous vous invitons à accéder à votre espace de travail personnalisé pour vos projets d’affaires et la collaboration avec l’équipe de BDO. Cliquez sur le bouton ci-dessous pour configurer votre compte.";
    public string PortalInvite_Cta => "Commencer la configuration ";

    // ProjectInvite
    public string ProjectInvite_Title =>
        "[Portail des clients BDO] Invitation de {{name}} à participer à votre projet";

    public string ProjectInvite_Title_NULL =>
        "[Portail des clients BDO] Vous avez été invité à participer à un projet";

    public string ProjectInvite_Cta => "Accéder à votre projet";
    public string ProjectInvite_Body =>
        "Vous avez reçu une invitation de {{name}} à participer à un projet. Le portail client BDO est une façon sécurisée et simple de collaborer avec votre équipe BDO. Dans le portail, vous pourrez :";

    public string ProjectInvite_Body_NULL =>
        "Vous avez été invité à participer à un projet. Le portail client BDO est une façon sécurisée et simple de collaborer avec votre équipe BDO. Dans le portail, vous pourrez :";

    public string[] ProjectInvite_Bullets =>
        [
            "visualiser les calendriers et les principales étapes des projets;",
            "rester au fait des risques ou problèmes;",
            "suivre la progression et recevoir des mises à jour en temps opportun;",
            "accéder aux documents et aux communications en un seul endroit;",
            "visualiser et gérer vos tâches.",
        ];

    public string ClientAdminInvite_Subject =>
        "Bienvenue dans votre espace de travail sur le Portail des clients BDO – Commencer la configuration de votre projet";
    public string ClientAdminInvite_Greeting => "Bonjour {{name}},";
    public string ClientAdminInvite_Body =>
        "Vous avez maintenant accès à votre nouveau portail client en tant qu’administrateur client de BDO. Ouvrez une session et commencez à configurer le portail pour votre client.";
    public string ClientAdminInvite_Cta => "Configurer votre portail client BDO";
    public string ClientAdminInvite_StepTitle => "Commencez par configurer votre projet :";
    public (string, string, string)[] ClientAdminInvite_Steps =>
        [
            (
                "Ajouter les membres de l’équipe",
                "Invitez les bonnes personnes à collaborer.",
                "team"
            ),
            (
                "Établir l’échéancier du projet",
                "Planifiez les dates et les jalons importants.",
                "timeline"
            ),
            (
                "Ajouter et attribuer des tâches",
                "Organisez le travail et respectez les délais.",
                "action"
            ),
            (
                "Déterminer les risques associés au projet",
                "Signalez rapidement toute préoccupation.",
                "risk"
            ),
        ];
    public string ClientAdminInvite_HelpTitle => "Besoin d’aide?";
    public string ClientAdminInvite_HelpBody => "Voici quelques ressources utiles :";
    public string ClientAdminInvite_ResourceName => "Centre d’excellence";

    // NotificationReminder
    public string NotificationReminder_Subject =>
        "Votre sommaire hebdomadaire du Portail des clients BDO";
    public string NotificationReminder_Title =>
        "Bonjour {{name}}, vous avez reçu {{notifications}} nouveau(x) avis cette semaine.";
    public string NotificationReminder_DateRange => "Du {{startDate}} au {{endDate}}";
    public string NotificationReminder_Cta => "Afficher les avis";
    public string NotificationReminder_Unsubscribe =>
        "Pour renoncer à recevoir des courriels promotionnels ou vous désinscrire,";

    //OnboardingCompleted
    public string OnboardingCompleted_Title => "Explorez votre Portail Client BDO";
    public string OnboardingCompleted_Heading => "Tout est prêt";

    public string OnboardingCompleted_Body =>
        "Découvrez votre espace de travail personnalisé destiné à la gestion de projets et à la collaboration avec votre équipe BDO.";

    public string OnboardingCompleted_Cta => "Accéder à mon tableau de bord";

    public string OnboardingCompleted_Footer_Note =>
        "Vous recevez ce courriel parce qu'un compte client de BDO est associé à votre adresse électronique.";

    //ActionItem
    public string ActionItem_TaskType(string type) =>
        type.ToLowerInvariant() switch
        {
            "signature" => "Demande de signature",
            "requestitem" => "Demande de document",
            "action" => "Project task",
            _ => "",
        };

    public string ActionItem_TaskTypeIcon(string type) =>
        type.ToLowerInvariant() switch
        {
            "signature" => "task-type-signature-request",
            "requestitem" => "task-type-document-request",
            "action" => "task-type-project-task",
            _ => "",
        };

    public string ActionItem_TaskStatus(string status) =>
        status.ToLowerInvariant() switch
        {
            "todo" => "À faire",
            "inreview" => "En cours de révision",
            "declined" => "Refusé",
            "complete" => "Terminé",
            _ => "",
        };

    public string ActionItem_TaskStatusIcon(string status) =>
        status.ToLowerInvariant() switch
        {
            "todo" => "task-status-to-do",
            "inreview" => "task-status-in-review",
            "declined" => "task-status-declined",
            "complete" => "task-status-complete",
            _ => "",
        };

    public string ActionItem_TaskStatusBadgeWidth(string status) =>
        status.ToLowerInvariant() switch
        {
            "todo" => "100px",
            "inreview" => "200px",
            "declined" => "100px",
            "complete" => "100px",
            _ => "",
        };

    //ActionItemAssigned
    public string ActionItemAssigned_Subject =>
        "[Portail client BDO - {{clientName}}] La tâche {{taskName}} vous a été assignée.";
    public string ActionItemAssigned_PreviewText =>
        """{{name}} vous a assigné la tâche {{taskName}}.""";
    public string ActionItemAssigned_Title =>
        """{{name}} vous a assigné la tâche <span style="font-weight: 600;">{{taskName}}</span>.""";
    public string ActionItemAssigned_Cta => "Voir dans le portail client BDO";
    public string ActionItemAssigned_DueDate => "À terminer avant le {{dueDate}}";

    //ActionItemStatusChanged
    public string ActionItemStatusChanged_Subject =>
        "[Portail client BDO - {{clientName}}] L'état de la tâche {{taskName}} est passé à {{status}}.";
    public string ActionItemStatusChanged_PreviewText =>
        """{{name}} a fait passer l'état de la tâche à {{status}}""";
    public string ActionItemStatusChanged_Title =>
        """{{name}} <span style="font-weight: 600;">a fait passer l'état de la tâche à</span>""";
    public string ActionItemStatusChanged_Cta => "Voir dans la portail client BDO";

    //FinalDeliveryAdded
    public string FinalDeliveryAdded_Subject =>
        "[Portail client BDO - {{clientName}}] {{actorName}} a téléversé un nouveau livrable";
    public string FinalDeliveryAdded_Title =>
        """{{actorName}} <span style="font-weight: 600;">a téléversé un nouveau livrable dans votre projet</span>""";
    public string FinalDeliveryAdded_Cta => "Voir dans le portail client BDO";
}
