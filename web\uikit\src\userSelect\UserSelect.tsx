import React, { useState } from "react";
import styles from "./userSelect.module.css";
import { DropdownUser, UserSelectProps } from "./spec";
import { Combobox, Portal } from "@ark-ui/react";
import classNames from "classnames";
import { CustomAvatar } from "~/avatar";
import { useTranslation } from "react-i18next";
import { getInitials, useFocusedBy } from "~/utils";

// Maximum items to display in the Dropdown
// to maintain performance during render
const MAX_RESULTS = 100;

export const UserSelect: React.FC<UserSelectProps> = ({
  id,
  users,
  selected,
  onChange,
  placeholder,
  label,
  hideLabel,
  ariaDescribedBy,
  ariaLabel,
}) => {
  const [input, setInput] = useState<string | null>(null);
  const [highlight, setHighlight] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation("dropdown");
  const { focusClass } = useFocusedBy();

  const handleChange = (details: Combobox.ValueChangeDetails<DropdownUser>) => {
    const selected = details.items.at(0) ?? null;
    onChange(selected);
    setInput(null);
  };

  const handleChangeInput = (details: Combobox.InputValueChangeDetails) => {
    onChange(null);
    setInput(details.inputValue);
  };

  const handleChangeHighlight = (details: Combobox.HighlightChangeDetails) => {
    setHighlight(details.highlightedValue);
  };

  const handleOpenChange = (details: { open: boolean }) => {
    setIsOpen(details.open);
  };

  const handleFocus = () => {
    setInput("");
    // Improved focus handling for VoiceOver - don't force click on focus
    // as it can interfere with screen reader navigation
  };

  const handleBlur = () => {
    setInput(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Improve VoiceOver navigation by handling specific key combinations
    if (e.key === 'ArrowDown' && !isOpen) {
      e.preventDefault();
      setIsOpen(true);
    }
  };

  const search = (users: DropdownUser[], query: string): DropdownUser[] => {
    query = query.toLowerCase();

    return users.filter(user => {
      const name = user.displayName.toLowerCase();
      const email = user.email.toLowerCase();

      if (name.includes(query)) {
        return true;
      }

      if (email.includes(query)) {
        return true;
      }

      return false;
    });
  };

  const getFilteredUsers = (users: DropdownUser[], input: string | null) => {
    let list = users;

    if (input) {
      list = search(list, input);
    }

    list = list.slice(0, MAX_RESULTS);

    return list;
  };

  // Highlight the first item as the user types
  const getAutoHighlight = (
    dropdownUsers: DropdownUser[],
    input: string | null
  ) => {
    if (!input) {
      return null;
    }

    const topItem = dropdownUsers.at(0);

    if (!topItem) {
      return null;
    }

    return topItem.id;
  };

  const dropdownUsers = getFilteredUsers(users, input);
  const autoHighlight = getAutoHighlight(dropdownUsers, input);
  const noResults = dropdownUsers.length === 0;

  return (
    <div className={styles.container}>
      <Combobox.Root
        id={id}
        items={dropdownUsers}
        placeholder={placeholder}
        openOnClick={true}
        open={isOpen}
        onOpenChange={handleOpenChange}
        itemToValue={user => user.id}
        itemToString={user => user.displayName}
        selectionBehavior="preserve"
        value={selected ? [selected.id] : []}
        onValueChange={handleChange}
        inputValue={input ?? (selected ? selected.displayName : "")}
        onInputValueChange={handleChangeInput}
        highlightedValue={highlight ?? autoHighlight}
        onHighlightChange={handleChangeHighlight}
        onBlur={handleBlur}
        positioning={{ fitViewport: true, strategy: "fixed" }}
        loopFocus={true}
      >
        <Combobox.Label
          className={classNames(hideLabel && styles.visuallyHidden)}
        >
          {label}
        </Combobox.Label>
        <Combobox.Control>
          {selected && (
            <span className={styles.selectedAvatar}>
              <CustomAvatar
                initials={getInitials(selected.displayName)}
                avatarSize="x-small"
                fontSize="x-small"
                type="monogram"
              />
            </span>
          )}
          <Combobox.Input
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            className={focusClass}
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            role="combobox"
            aria-autocomplete="list"
            aria-describedby={ariaDescribedBy}
            aria-label={ariaLabel || label}
          />
        </Combobox.Control>
        <Portal>
          <Combobox.Positioner className={styles.positioner}>
            <Combobox.Content className={styles.content}>
              {dropdownUsers.map(user => (
                <Combobox.Item key={user.id} item={user}>
                  <CustomAvatar
                    initials={getInitials(user.displayName)}
                    avatarSize="x-small"
                    fontSize="x-small"
                    type="monogram"
                  />
                  <Combobox.ItemText>{user.displayName}</Combobox.ItemText>
                </Combobox.Item>
              ))}

              {noResults && (
                <div className={styles.noResults}>{t("no-results")}</div>
              )}
            </Combobox.Content>
          </Combobox.Positioner>
        </Portal>
      </Combobox.Root>
    </div>
  );
};
