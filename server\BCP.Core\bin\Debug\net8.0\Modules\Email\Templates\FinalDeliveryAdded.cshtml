@using BCP.Core.Email.Translations
@model BCP.Core.Email.Templates.FinalDeliveryAdded
@using Microsoft.AspNetCore.Html

@{
    Layout = "Common/Layout.cshtml";
    var T = Model.Translation;
}

<table role="presentation" cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td style="padding: 0 0 24px 0;">
            <h1 style="margin:0; color:#333; font-size:16px; font-style:normal; font-weight:400; line-height:24px;">
                @((HtmlString)new HtmlString(Model.Title))
            </h1>
        </td>
    </tr>
    <tr>
        <td style="padding: 32px; border-width: 1px; border-radius: 2px; border-color: #e7e7e7; border-style: solid;">
            <p style="margin:0; color:#666; font-size:14px; font-style:normal; font-weight:400; line-height:20px;">
                @Model.Project?.Name
            </p>
            <p style="margin:8px 0 0; color:#333; font-size:18px; font-style:normal; font-weight:600; line-height:24px;">
                @Model.DeliverableName
            </p>
            <!--[if mso]>
                    <table cellpadding="0" cellspacing="0" role="presentation" border="0" style="border-collapse:collapse;">
                            <tr>
                                <td style="padding: 24px 0 0;">
                                <v:roundrect
                                    xmlns:v="urn:schemas-microsoft-com:vml"
                                    href="@Model.PortalUrl/client/@Model.Client?.Id/project/@Model.Project?.Id/documents?path=@Uri.EscapeDataString(Model.DirectoryPath ?? "")"
                                    style="height:56px; v-text-anchor:middle; width:290px; padding:0 24px;"
                                    arcsize="2%"
                                    strokecolor="#E81A3B"
                                    fillcolor="#E81A3B"
                                >
                                    <w:anchorlock/>
                                    <center style="color:#ffffff; font-family:Arial, sans-serif; font-size:16px; font-weight:400; mso-line-height-rule: exactly;">
                                      @T.FinalDeliveryAdded_Cta &rarr;
                                    </center>
                                </v:roundrect>
                                </td>
                            </tr>
                    </table>
            <![endif]-->
            <!--[if !mso]><!-- -->
            <table role="presentation" cellpadding="0" cellspacing="0" style="margin: 24px 0 0;">
                <tr>
                    <td style="background:#e81a3b; padding:8px 16px; border-radius:2px;">
                        <a href="@Model.PortalUrl/client/@Model.Client?.Id/project/@Model.Project?.Id/documents?path=@Uri.EscapeDataString(Model.DirectoryPath ?? "")"
                           target="_blank" style="color:#fff; text-decoration:none; font-size:16px; line-height:24px; font-weight:300;">
                            @T.FinalDeliveryAdded_Cta &rarr;
                        </a>
                    </td>
                </tr>
            </table>
            <!--<![endif]-->
        </td>
    </tr>
</table>

@section Footer {
    <p style="font-size:12px; line-height:18px; color:#666; margin:0 0 16px 0; font-family:Arial, sans-serif;">
        @T.Notification_Footer_Note <a href="@Model.PortalUrl/settings/notifications">@T.Notification_Footer_Cta</a>.
    </p>
}
