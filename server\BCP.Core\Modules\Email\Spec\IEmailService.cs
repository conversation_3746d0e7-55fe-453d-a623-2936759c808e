using BCP.Data.Models;
using Microsoft.AspNetCore.Mvc;
using SendGrid.Helpers.Mail;

namespace BCP.Core.Email;

public interface IEmailService
{
    Task<string?> SendEmail<T>(
        T model,
        string[]? ccRecipients = null,
        long? delayTimeInSeconds = null
    )
        where T : class, ITemplateModel;
    Task<FileStreamResult> GetAssetStream(string path);

    Task<string> GetPreview(string name, Language language = Language.English);

    Task SendEmailAsync(
        string[] to,
        string subject,
        string htmlBody,
        string[]? ccRecipients,
        long? delayTimeInSeconds,
        IList<Attachment>? attachments
    );

    Task<MessageStatus> GetDeliveryStatus(string messageId);
}
