namespace BCP.Core.Email.Templates;

public class ActionItemStatusChanged : TemplateModel, ITemplateModel
{
    public static string TemplateName => "ActionItemStatusChanged";
    public override EmailLayout Layout => EmailLayout.Narrow;
    public Guid TaskId { get; set; }
    public string? TaskName { get; set; } = null;
    public string? TaskType { get; set; } = null;
    public string? Status { get; set; } = null;
    public string DisplayedTaskType => Translation.ActionItem_TaskType(TaskType);
    public string TaskTypeIcon => Translation.ActionItem_TaskTypeIcon(TaskType);
    public string DisplayedTaskStatus => Translation.ActionItem_TaskStatus(Status);
    public string TaskStatusIcon => Translation.ActionItem_TaskStatusIcon(Status);
    public string TaskStatusOutlookBadgeWidth =>
        Translation.ActionItem_TaskStatusBadgeWidth(Status);

    public string Title =>
        Translation.ActionItemStatusChanged_Title.Replace("{{name}}", Actor?.Name);

    public override string Subject =>
        Translation
            .ActionItemStatusChanged_Subject.Replace("{{clientName}}", Client?.Name)
            .Replace("{{taskName}}", TaskName)
            .Replace("{{status}}", DisplayedTaskStatus);

    public override string PreviewText =>
        Translation
            .ActionItemStatusChanged_PreviewText.Replace("{{name}}", Actor?.Name)
            .Replace("{{status}}", DisplayedTaskStatus);
}
