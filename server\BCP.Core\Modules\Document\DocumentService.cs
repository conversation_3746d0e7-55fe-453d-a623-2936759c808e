using System.Text.Json;
using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.BGP.ControlAPI.Spec;
using BCP.Core.Client;
using BCP.Core.Common;
using BCP.Core.Document.Models;
using BCP.Core.Events;
using BCP.Core.Modules.BGP.ControlAPI.Models;
using BCP.Core.Project;
using BCP.Core.Template.Models;
using BCP.Core.User;
using BCP.Data;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MimeDetective;
using EventPayloads = BCP.Core.Events.Payloads;

namespace BCP.Core.Document
{
    public class DocumentService : IDocumentService
    {
        private readonly IControlAPIService _bgpService;
        private readonly string deliverablesPath = "Deliverables";
        private readonly IClientService _clientService;
        private readonly IProjectService _projectService;
        private readonly ITeamMemberService _teamMemberService;
        private IEventService _eventService;
        private readonly IUserContext _contextService;
        private readonly DataContext _context;
        private ILogger<IDocumentService> _logger;

        private WebSettings _webSettings;

        public DocumentService(
            IControlAPIService bgpService,
            DataContext context,
            IEventService eventService,
            IClientService clientService,
            IProjectService projectService,
            ITeamMemberService teamMemberService,
            IUserContext contextService,
            ILogger<IDocumentService> logger,
            WebSettings webSettings
        )
        {
            _bgpService = bgpService;
            _context = context;
            _eventService = eventService;
            _clientService = clientService;
            _projectService = projectService;
            _teamMemberService = teamMemberService;
            _contextService = contextService;
            _logger = logger;
            _webSettings = webSettings;
        }

        public async Task<BDOUploadDocument> UploadDocumentAsync(
            IFormFile file,
            int memberFirmId,
            int clientId,
            string path,
            int projectId,
            int? folderListItemId
        )
        {
            var client = await _clientService.GetByBgpId(clientId);
            var project = await _projectService.GetByBgpId(clientId, projectId);
            if (client == null)
                throw new CoreException("Invalid Client", CoreError.BadRequest);
            if (project == null)
                throw new CoreException("Invalid Project", CoreError.BadRequest);

            this.ValidateFile(file);

            var parentPath = Path.GetDirectoryName(path) ?? "/";
            var name = Path.GetFileName(path);
            // Check for existing file in the destination folder, as BGP upload throws 500 in this case
            var existingDocuments = await _bgpService.GetFolderContentAsync(
                parentPath,
                memberFirmId,
                clientId,
                projectId,
                null
            );

            var nameExists = existingDocuments?.Items.Any(document => document.Name == name);

            if (nameExists == true)
            {
                throw new InvalidOperationException("File already exists in this folder");
            }

            // Upload file
            BDOUploadDocument uploadedDocument = await _bgpService.UploadDocumentAsync(
                file,
                memberFirmId,
                clientId,
                path,
                projectId
            );

            bool hasFileBeenUploadedToDeliverables =
                path.Split('/').Length > 1 && path.Split('/')[1] == deliverablesPath;

            if (uploadedDocument != null && hasFileBeenUploadedToDeliverables)
            {
                var folderPermissions = await _bgpService.GetItemPermissions(
                    memberFirmId,
                    clientId,
                    projectId,
                    folderListItemId ?? 0
                );
                // Get the list of client user IDs allowed to access the folder.
                // If the list is empty, it means the folder is not restricted.
                List<Guid> usersWithAccessToFolder = await GetUsersWithAccessToFolder(folderPermissions, true);

                // If the folder is not restricted, we can notify all project client users.
                if (usersWithAccessToFolder.Count == 0)
                {
                    usersWithAccessToFolder = await GetAllProjectClientUsers(clientId, projectId);
                }

                await _eventService.LogEvent(
                    new EventPayloads.DocumentAdded
                    {
                        ClientId = client.Id,
                        ProjectId = project.Id,
                        DocumentId = uploadedDocument.DriveItemId,
                        DocumentName = uploadedDocument.Name,
                        Path = path,
                        NotificationRecipientsOnly = usersWithAccessToFolder,
                    }
                );
            }

            return uploadedDocument;
        }

        public async Task<DocumentsResponse?> GetFolderContentAsync(
            string path,
            int memberFirmId,
            int clientId,
            int? projectId,
            string? filter
        )
        {
            // Check current user authorization
            if (_contextService.CurrentUser == null)
                throw new CoreException(CoreError.Unauthorized);

            var currentUserId = _contextService.CurrentUser.Id;

            // Check if current folder is restricted and validate access
            await ValidateFolderAccess(path, memberFirmId, clientId, projectId, currentUserId);

            var bdoDocuments = await _bgpService.GetFolderContentAsync(
                path,
                memberFirmId,
                clientId,
                projectId,
                filter
            );

            var bdoDocumentItems = bdoDocuments?.Items ?? [];
            List<DocumentResponse> documents = [];
            var template = projectId is int _projectId
                ? await _projectService.GetTemplate(clientId, _projectId)
                : null;

            if (!string.IsNullOrWhiteSpace(filter))
            {
                // Developer Note: This is a workaround for the BGP API not returning the document with driveItemId when searching for documents
                // Should be removed if BGP API  can be changed to return the driveItemId on search
                foreach (var bdoDocument in bdoDocumentItems)
                {
                    try
                    {
                        string? relativePath = ExtractPathAfterDocuments(bdoDocument.Url);
                        var detailedDocument = await GetDocumentDetails(
                            clientId,
                            projectId ?? 0,
                            null,
                            relativePath
                        );

                        documents.Add(
                            detailedDocument
                                ?? await MapBDODocumentToBCPDocument(bdoDocument, template)
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(
                            ex,
                            "Failed to get document details for URL: {Url}, falling back to basic mapping",
                            bdoDocument.Url
                        );
                        documents.Add(await MapBDODocumentToBCPDocument(bdoDocument, template));
                    }
                }
            }
            else
            {
                foreach (var bdoDocument in bdoDocumentItems)
                {
                    documents.Add(await MapBDODocumentToBCPDocument(bdoDocument, template));
                }
            }

            // Filter restricted items based on user access
            var filteredDocuments = new List<DocumentResponse>();
            foreach (var document in documents)
            {
                if (await HasAccessToItem(document, memberFirmId, clientId, projectId, currentUserId))
                {
                    filteredDocuments.Add(document);
                }
            }

            // Update folder modification timestamps based on their contained files
            var folders = filteredDocuments
                .Where(d => d.FileKind.Equals("Folder", StringComparison.OrdinalIgnoreCase))
                .ToList();
            if (folders.Any())
            {
                await UpdateFolderTimestampsBasedOnContents(
                    folders,
                    memberFirmId,
                    clientId,
                    projectId
                );
            }

            // Calculate file and subfolder counts for folders
            if (folders.Any())
            {
                await UpdateFolderCounts(folders, memberFirmId, clientId, projectId);
            }

            return new() { Items = filteredDocuments };
        }

        /// <summary>
        /// Validates if the current user has access to the requested folder
        /// </summary>
        private async Task ValidateFolderAccess(
            string path,
            int memberFirmId,
            int clientId,
            int? projectId,
            Guid currentUserId)
        {
            // For root path, typically no restriction check needed
            if (string.IsNullOrEmpty(path) || path == "/")
                return;

            // Get folder details to check if it's restricted
            var folderDetails = await _bgpService.GetDocumentDetails(
                clientId,
                projectId ?? 0,
                null,
                path
            );

            // If folder doesn't exist, throw NotFound error
            if (folderDetails == null)
                throw new CoreException(CoreError.NotFound, $"Folder {path} not found");

            // If folder is not restricted, allow access
            if (!folderDetails.Restricted)
                return;

            // Get folder permissions
            var folderPermissions = await _bgpService.GetItemPermissions(
                memberFirmId,
                clientId,
                projectId ?? 0,
                folderDetails.ListItemId
            );

            // Check if current user has access, if not, throw Forbidden error
            List<Guid> usersWithAccess = await GetUsersWithAccessToFolder(folderPermissions, false);

            if (usersWithAccess.Count > 0 && !usersWithAccess.Contains(currentUserId))
            {
                throw new CoreException(CoreError.Forbidden, "Access denied to this folder");
            }
        }

        /// <summary>
        /// Checks if the current user has access to a specific document/folder item
        /// </summary>
        private async Task<bool> HasAccessToItem(
            DocumentResponse document,
            int memberFirmId,
            int clientId,
            int? projectId,
            Guid currentUserId)
        {
            try
            {
                // If item is not restricted, allow access
                if (!document.Restricted)
                    return true;

                // Get item permissions
                var itemPermissions = await _bgpService.GetItemPermissions(
                    memberFirmId,
                    clientId,
                    projectId ?? 0,
                    document.ListItemId
                );

                // Check if current user has access
                var usersWithAccess = await GetUsersWithAccessToFolder(itemPermissions, false);

                // If no specific users are set, it might be inherited or unrestricted
                if (usersWithAccess.Count == 0)
                    return true;

                return usersWithAccess.Contains(currentUserId);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex,
                    "Error checking access for item: {ItemName}, User: {UserId}",
                    document.Name, currentUserId);

                // On error, deny access for restricted items (fail-closed approach)
                return false;
            }
        }

        public async Task<DocumentResponse?> GetDocumentDetails(
            int clientId,
            int projectId,
            string? driveItemId,
            string? path
        )
        {
            var template = projectId is int _projectId
                ? await _projectService.GetTemplate(clientId, _projectId)
                : null;

            BDODocumentItem? bgpDocument = await _bgpService.GetDocumentDetails(
                clientId,
                projectId,
                driveItemId,
                path
            );

            if (bgpDocument == null)
            {
                return null;
            }

            var bcpDocument = await MapBDODocumentToBCPDocument(bgpDocument, template);

            return bcpDocument;
        }

        private string? ExtractPathAfterDocuments(string fullUrl)
        {
            const string documentsMarker = "/Documents/";
            var index = fullUrl.IndexOf(documentsMarker, StringComparison.OrdinalIgnoreCase);

            if (index < 0)
                return null;

            return fullUrl.Substring(index + documentsMarker.Length - 1);
        }

        private async Task<DocumentResponse> MapBDODocumentToBCPDocument(
            BDODocumentItem bdoDocument,
            ProjectTemplate? template
        )
        {
            var actionItem = await _context
                .ActionItemDocuments.Where(actionItemDocument =>
                    actionItemDocument.DriveItemId.Equals(bdoDocument.DriveItemId)
                )
                .Select(actionItemDocument => new DocumentActionItem()
                {
                    Id = actionItemDocument.ActionItemId,
                    Name = actionItemDocument.ActionItemName,
                })
                .FirstOrDefaultAsync();

            var isTemplated =
                (template != null) ? this.IsTemplatedNode(template, bdoDocument.Url) : false;

            return new()
            {
                FileKind = bdoDocument.FileKind,
                DriveItemId = bdoDocument.DriveItemId,
                ListItemId = bdoDocument.ListItemId,
                DisplayName = bdoDocument.DisplayName,
                Name = bdoDocument.Name,
                Url = bdoDocument.Url,
                ParentUrl = bdoDocument.ParentUrl,
                Size = bdoDocument.Size,
                CreatedAt = bdoDocument.CreatedAt,
                CreatedByName = bdoDocument.CreatedByName,
                ModifiedAt = bdoDocument.ModifiedAt,
                ModifiedByName = bdoDocument.ModifiedByName,
                Restricted = bdoDocument.Restricted,
                Readonly = bdoDocument.Readonly,
                RequestApproval = bdoDocument.RequestApproval,
                RequestSignature = bdoDocument.RequestSignature,
                DeletionInProgress = bdoDocument.DeletionInProgress,
                ActionItem = actionItem,
                IsTemplated = isTemplated,
            };
        }

        /// <summary>
        /// Updates folder modification timestamps based on their actual contents.
        /// This method checks each folder individually to see if it contains files
        /// with more recent modification times than the folder itself.
        /// Only folders that actually contain newer files get their timestamps updated.
        /// </summary>
        private async Task UpdateFolderTimestampsBasedOnContents(
            List<DocumentResponse> folders,
            int memberFirmId,
            int clientId,
            int? projectId
        )
        {
            foreach (var folder in folders)
            {
                try
                {
                    // Get the contents of this specific folder to check for newer files
                    var folderPath = GetRelativeFolderPath(folder.Url);
                    var folderContents = await _bgpService.GetFolderContentAsync(
                        folderPath,
                        memberFirmId,
                        clientId,
                        projectId,
                        null // no filter
                    );

                    if (folderContents?.Items == null || !folderContents.Items.Any())
                        continue;

                    // Find the most recently modified file within THIS specific folder
                    var mostRecentFileInFolder = folderContents
                        .Items.Where(item =>
                            item.FileKind.Equals("File", StringComparison.OrdinalIgnoreCase)
                        )
                        .OrderByDescending(item => item.ModifiedAt)
                        .FirstOrDefault();

                    // Only update this folder's timestamp if it contains a file newer than the folder itself
                    if (
                        mostRecentFileInFolder != null
                        && mostRecentFileInFolder.ModifiedAt > folder.ModifiedAt
                    )
                    {
                        folder.ModifiedAt = mostRecentFileInFolder.ModifiedAt;
                        //folder.ModifiedByName = mostRecentFileInFolder.ModifiedByName;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail the entire operation
                    // The folder will keep its original modification time
                    Console.WriteLine(
                        $"Error updating folder timestamp for {folder.DisplayName}: {ex.Message}"
                    );
                }
            }
        }

        /// Updates folder file and subfolder counts based on their actual contents.
        /// This method checks each folder individually to count the number of direct files
        /// and direct subfolders within it.
        private async Task UpdateFolderCounts(
            List<DocumentResponse> folders,
            int memberFirmId,
            int clientId,
            int? projectId
        )
        {
            foreach (var folder in folders)
            {
                try
                {
                    // Get the contents of this specific folder to count files and subfolders
                    var folderPath = GetRelativeFolderPath(folder.Url);
                    var folderContents = await _bgpService.GetFolderContentAsync(
                        folderPath,
                        memberFirmId,
                        clientId,
                        projectId,
                        null // no filter
                    );

                    if (folderContents?.Items == null)
                    {
                        folder.FileCount = 0;
                        folder.SubfolderCount = 0;
                        continue;
                    }

                    // Count files and subfolders directly within this folder
                    var fileCount = folderContents.Items.Count(item =>
                        item.FileKind.Equals("File", StringComparison.OrdinalIgnoreCase)
                    );

                    var subfolderCount = folderContents.Items.Count(item =>
                        item.FileKind.Equals("Folder", StringComparison.OrdinalIgnoreCase)
                    );

                    folder.FileCount = fileCount;
                    folder.SubfolderCount = subfolderCount;
                }
                catch (Exception ex)
                {
                    folder.FileCount = null;
                    folder.SubfolderCount = null;
                }
            }
        }

        private string GetRelativeFolderPath(string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                // Extract the path after "/Documents"
                var documentsIndex = url.IndexOf("/Documents", StringComparison.OrdinalIgnoreCase);
                if (documentsIndex >= 0)
                {
                    var relativePath = url.Substring(documentsIndex + "/Documents".Length);
                    return string.IsNullOrEmpty(relativePath) ? "/" : relativePath;
                }
                else
                    return "/";
            }
            else
                return "/";
        }

        public async Task<byte[]> DownloadFileAsync(string filePath)
        {
            return await _bgpService.DownloadFileAsync(filePath);
        }

        public async Task<string> CreateDownloadFilesOrFolderAsync(DocumentDownloadRequest request)
        {
            return await _bgpService.CreateDownloadFilesOrFolderAsync(request);
        }

        public async Task<Stream> DownloadFilesOrFolderAsync(string downloadKey)
        {
            return await _bgpService.DownloadFilesOrFolderAsync(downloadKey);
        }

        public async Task<BDOFolder> CreateNewFolder(FolderCreationRequest request)
        {
            return await _bgpService.CreateNewFolder(request);
        }

        public void ValidateFileForUpload(IFormFile file)
        {
            ValidateFile(file);
        }

        private void ValidateFile(IFormFile file)
        {
            var allowedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                ".pdf",
                ".jpg",
                ".jpeg",
                ".png",
                ".gif",
                ".zip",
                ".zipx",
                ".rar",
                ".7z",
                ".txt",
                ".log",
                ".csv",
                ".doc",
                ".docx",
                ".docm",
                ".rtf",
                ".xls",
                ".xlsx",
                ".xlsm",
                ".xlm",
                ".ppt",
                ".pptx",
                ".pptm",
                ".eml",
                ".msg",
                ".vsd",
                ".vsdx",
                ".html",
                ".htm",
                ".xsl",
                ".xslt",
                ".xml",
                ".xps",
                ".mp4",
                ".preview",
                ".nd",
                ".qbw",
                ".sds",
                ".tlg",
            };

            const long maxFileSize = 2L * 1024 * 1024 * 1024;
            const int maxFileNameLength = 225;

            var allowedMimeTypes = new HashSet<string>(
                FileUploadSettings.AllowedMimeTypes ?? new List<string>(),
                StringComparer.OrdinalIgnoreCase
            );

            var invalidCharacters = new char[]
            {
                '.',
                '*',
                ':',
                '<',
                '>',
                '?',
                '/',
                '~',
                '|',
                '\\',
                '"',
                '\'',
                ';',
                '(',
                ')',
                '\u2022',
            };

            var fileName = Path.GetFileName(file.FileName);
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(file.FileName);
            var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();

            var foundInvalidChars = fileNameWithoutExtension
                .Where(charcter => invalidCharacters.Contains(charcter))
                .ToArray();

            if (fileNameWithoutExtension.Length > maxFileNameLength)
            {
                _logger.LogWarning(
                    "File name exceeds maximum length: {FileName}, Length: {Length}, MaxLength: {MaxLength}",
                    fileName,
                    fileName.Length,
                    maxFileNameLength
                );
                throw new CoreException(
                    CoreError.BadRequest,
                    $"File name is too long. Maximum allowed length is {maxFileNameLength} characters, but the file name is {fileName.Length} characters long."
                );
            }

            if (file.Length > maxFileSize)
            {
                _logger.LogWarning(
                    "Rejected file due to size limit: {FileName}, Extension: {Extension}, Size: {Size} bytes",
                    fileName,
                    fileExtension,
                    file.Length
                );
                throw new CoreException(
                    CoreError.BadRequest,
                    $"File size exceeds the {maxFileSize / (1024 * 1024 * 1024)}GB limit. Uploaded size: {file.Length} bytes"
                );
            }

            if (foundInvalidChars.Length > 0)
            {
                var invalidCharsString = string.Join(", ", foundInvalidChars.Select(c => $"'{c}'"));
                _logger.LogWarning(
                    "File name contains invalid characters: {FileName}, Invalid characters: {InvalidChars}",
                    fileName,
                    invalidCharsString
                );
                throw new CoreException(
                    CoreError.BadRequest,
                    $"File name contains invalid characters: {invalidCharsString}. File names cannot contain: < > \" ' ; ( ) •"
                );
            }

            if (fileNameWithoutExtension.Count(c => c == '.') > 1)
            {
                _logger.LogWarning(
                    "File name cannot contain more than one period (.) character: {FileName}",
                    fileName
                );
                throw new InvalidOperationException(
                    "File name cannot contain more than one period (.) character."
                );
            }

            if (!allowedExtensions.Contains(fileExtension))
            {
                _logger.LogWarning(
                    "Rejected file due to invalid extension: {FileName}, Extension: {Extension}",
                    fileName,
                    fileExtension
                );
                throw new CoreException(
                    CoreError.BadRequest,
                    $"Invalid file type: {fileExtension}. Allowed types: {string.Join(", ", allowedExtensions)}"
                );
            }

            // --- Feature flag for Mime-Detective ---
            if (_webSettings.Features?.MimeTypeCheck == true)
            {
                using var stream = file.OpenReadStream();

                var inspector = new ContentInspectorBuilder()
                {
                    Definitions = MimeDetective.Definitions.DefaultDefinitions.All(),
                }.Build();

                var result = inspector.Inspect(stream);
                var mimeMatches = result.ByMimeType();
                var detectedMime = mimeMatches.Length > 0 ? mimeMatches[0].MimeType : null;

                var safeTextExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
                {
                    ".txt",
                    ".log",
                    ".csv",
                    ".xml",
                    ".html",
                    ".htm",
                    ".xsl",
                    ".xslt",
                    ".rtf",
                    ".eml",
                    ".msg",
                    ".gme",
                    ".preview",
                    ".nd",
                    ".qbw",
                    ".sds",
                    ".tlg",
                };

                // Check browser-provided content type
                if (!allowedMimeTypes.Contains(file.ContentType))
                {
                    _logger.LogWarning(
                        "Rejected file due to invalid content type: {FileName}, Extension: {Extension}, ContentType: {ContentType}",
                        fileName,
                        fileExtension,
                        file.ContentType
                    );
                    throw new CoreException(
                        CoreError.BadRequest,
                        $"Invalid content type: {file.ContentType}. Allowed types: {string.Join(", ", allowedMimeTypes)}"
                    );
                }

                if (!safeTextExtensions.Contains(fileExtension))
                {
                    // Only apply strict Mime-Detective validation for non-text files
                    if (detectedMime == null || !allowedMimeTypes.Contains(detectedMime))
                    {
                        _logger.LogWarning(
                            "Rejected file due to Mime-Detective check: {FileName}, Extension: {Extension}, DetectedMIME: {DetectedMIME}, ContentType: {ContentType}",
                            fileName,
                            fileExtension,
                            detectedMime,
                            file.ContentType
                        );

                        throw new CoreException(
                            CoreError.BadRequest,
                            $"Mime-Detective rejected file. Detected MIME: {detectedMime ?? "unknown"}, Extension: {fileExtension}, ContentType: {file.ContentType}"
                        );
                    }
                }
            }
        }

        public async Task<BDORecycleBinDocument[]?> GetRecycleBinAsync(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var recycleBinItems = await _bgpService.GetRecycleBinAsync(
                memberFirmId,
                clientId,
                projectId
            );

            if (recycleBinItems != null)
            {
                foreach (var document in recycleBinItems)
                {
                    var fileLocationSegments = document.Url.Split('/');

                    // All BGP Document Urls follow pattern of "/sites/{env}-gp-prj-{mfId}-{clientId}-{projectId}/Documents.../"
                    // remove "sites/{env}-gp-prj-{mfId}-{clientId}-{projectId}/" from url to give relative path from Documents Root to return to FE
                    document.OriginalLocation = string.Join("/", fileLocationSegments.Skip(2));
                }
            }
            return recycleBinItems;
        }

        public async Task<bool> DeleteRecycleBinItemsPermanently(
            BDODeleteRecycleBinItemPermanentlyRequest request
        )
        {
            return await _bgpService.DeleteRecycleBinItemsPermanently(request);
        }

        public async Task MoveItemsToRecycleBin(BDOMoveToTrashRequest request)
        {
            await _bgpService.MoveItemsToRecycleBin(request);
        }

        public async Task RestoreDocuments(
            int _clientId,
            int _memberFirmId,
            int _projectId,
            BDORestoreRequest request
        )
        {
            await _bgpService.RestoreDocuments(_clientId, _memberFirmId, _projectId, request);
        }

        public async Task<bool> AssociateActionItemToDocument( // remove from here ...
            string actionItemId,
            int clientId,
            int projectId,
            BDOUploadDocument document
        )
        {
            try
            {
                if (_contextService.CurrentUser == null)
                    throw new CoreException(CoreError.Unauthorized);

                var actionItem = await _bgpService.GetActionItem(
                    new Guid(actionItemId),
                    clientId,
                    projectId
                );

                Data.Models.ActionItemDocument actionItemDocument = new()
                {
                    ActionItemId = actionItemId,
                    ActionItemName = actionItem?.Name ?? "",
                    DriveItemId = document.DriveItemId,
                    Name = document.Name,
                    ServerRelativeUrl = document.ServerRelativeUrl,
                    UploadedAt = DateTime.UtcNow,
                    UploadedBy = _contextService.CurrentUser.Id,
                };
                await _context.ActionItemDocuments.AddAsync(actionItemDocument);
                await _context.SaveChangesAsync();
                _logger.LogInformation(
                    $"Action Item {actionItemId} associated with Action Item Document with driveItemId {document.DriveItemId}."
                );
                return true;
            }
            catch (Exception)
            {
                _logger.LogError(
                    $"Failed to associate Action Item {actionItemId} to Document with driveItemId {document.DriveItemId}."
                );
                return false;
            }
        }

        public Task<BDOPerson[]> GetAvailableUsersToAssignAccess(
            BDOAvailableUsersToAssignAccessRequest request
        )
        {
            return _bgpService.GetAvailableUsersToAssignAccessForDocument(request);
        }

        private async Task<List<Guid>> GetUsersWithAccessToFolder(JsonElement folderPermissions, bool isClientUserOnly)
        {
            var emails = new List<string>();

            if (!folderPermissions.TryGetProperty("users", out JsonElement usersElement))
                return [];

            if (usersElement.ValueKind != JsonValueKind.Array)
                return [];

            foreach (JsonElement user in usersElement.EnumerateArray())
            {
                if (
                    user.ValueKind == JsonValueKind.Object
                    && user.TryGetProperty("email", out JsonElement emailElement)
                    && emailElement.ValueKind == JsonValueKind.String
                )
                {
                    var email = emailElement.GetString();

                    // Here we filter out BDO users based on their email address. Otherwise, we have to call control API for each user to
                    // get their user types (refer to GetProjectTeamMembersAsync method in TeamMembersService), which would slow down the
                    // process significantly.
                    if (!string.IsNullOrEmpty(email)
                        && (isClientUserOnly ? !Validators.BDOEmailRegex().IsMatch(email) : true))
                    {
                        emails.Add(email);
                    }
                }
            }

            var userIds = await _context
                .Users.Where(u => emails.Contains(u.Email))
                .Select(u => u.Id)
                .ToListAsync();

            return userIds;
        }

        private async Task<List<Guid>> GetAllProjectClientUsers(int clientId, int projectId)
        {
            try
            {
                // Get all team members of this project
                var teamMembers = await _teamMemberService.GetProjectTeamMembersAsync(
                    clientId,
                    projectId
                );

                if (teamMembers == null || !teamMembers.Any())
                {
                    return new List<Guid>();
                }

                // Filter client team members based on their user types
                var clientTeamMembers = teamMembers
                    .Where(tm => tm.UserType == UserRole.ClientUser)
                    .ToList();

                var userIds = clientTeamMembers.Select(u => u.Id).ToList();

                return userIds ?? new List<Guid>();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to get all project users for notification. ClientId: {ClientId}, ProjectId: {ProjectId}",
                    clientId,
                    projectId
                );
                return new List<Guid>();
            }
        }

        /// <summary>
        /// Checks whether the given path corresponds to a folder defined in the project template.
        /// </summary>
        private bool IsTemplatedNode(ProjectTemplate template, string path)
        {
            // Skip files, only template folders are supported for now.
            if (string.IsNullOrWhiteSpace(path) || Path.HasExtension(path))
                return false;

            var normalized = NormalizePath(path);
            if (normalized is null)
                return false;

            // Compare normalized path against all template folder paths.
            return GetTemplatePaths(template.Folders)
                .Any(p => p.Equals(normalized, StringComparison.OrdinalIgnoreCase));

            // Extracts the relative, trimming slashes.
            string? NormalizePath(string url)
            {
                const string marker = "/Documents/";
                var i = url.IndexOf(marker, StringComparison.OrdinalIgnoreCase);
                return i < 0 ? null : url[(i + marker.Length)..].Trim('/');
            }

            // Recursively flattens all folder paths from the template, including subfolders.
            IEnumerable<string> GetTemplatePaths(List<FolderTemplate> folders, string prefix = "")
            {
                foreach (var f in folders)
                {
                    var name = f.Name.Trim('/');
                    var full = string.IsNullOrEmpty(prefix) ? name : $"{prefix}/{name}";
                    yield return full;

                    foreach (var sub in GetTemplatePaths(f.Subfolders, full))
                        yield return sub;
                }
            }
        }

        public async Task<AptExportValidationResponse> GetAptExportValidationAsync(
            int memberFirmId,
            int clientId,
            int projectId,
            List<AptExportDocumentInfo> documents
        )
        {
            var response = new AptExportValidationResponse();

            foreach (var document in documents)
            {
                try
                {
                    // Use the complete validation function that includes recursive counts
                    var validation = await ValidateDocumentForAptExport(
                        memberFirmId,
                        clientId,
                        projectId,
                        document.Path,
                        document.IsFolder
                    );

                    response.Documents.Add(validation);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error validating document for APT export: {Path}",
                        document.Path
                    );
                    response.Documents.Add(
                        new AptExportDocumentValidation
                        {
                            Path = document.Path,
                            Name = GetFileNameFromPath(document.Path),
                            Type = "Unknown",
                            IsValid = false,
                            Errors = new List<string>
                            {
                                $"Error processing document: {ex.Message}",
                            },
                        }
                    );
                }
            }

            return response;
        }

        private async Task<AptExportDocumentValidation> ValidateDocumentForAptExport(
            int memberFirmId,
            int clientId,
            int projectId,
            string path,
            bool isFolder
        )
        {
            // Get document details
            var documentDetails = await GetDocumentDetails(clientId, projectId, null, path);

            if (documentDetails == null)
            {
                _logger.LogWarning("Document not found for path: {Path}", path);
                return new AptExportDocumentValidation
                {
                    Path = path,
                    Name = GetFileNameFromPath(path),
                    Type = "Unknown",
                    IsValid = false,
                    Errors = new List<string> { "Document not found" },
                };
            }

            // Use the frontend-provided information about whether this is a folder
            // This is more reliable than trying to detect from BGP data
            bool isActuallyFolder = isFolder || documentDetails.FileKind == "Folder";

            var validation = new AptExportDocumentValidation
            {
                Path = path,
                Name = documentDetails.Name,
                Type = isActuallyFolder ? "Folder" : "File",
                IsValid = true,
                Errors = new List<string>(),
            };

            if (isActuallyFolder)
            {
                // For folders, get recursive counts and validate all nested files
                var recursiveCounts = await GetRecursiveCountsForFolder(
                    memberFirmId,
                    clientId,
                    projectId,
                    path
                );
                validation.RecursiveCounts = recursiveCounts;

                // If there are invalid files, mark the folder as invalid
                if (recursiveCounts.InvalidFiles.Any() || recursiveCounts.OversizedFiles.Any())
                {
                    validation.IsValid = false;

                    if (recursiveCounts.InvalidFiles.Any())
                    {
                        validation.Errors.Add("apt-export-contains-invalid-file-type");
                    }

                    if (recursiveCounts.OversizedFiles.Any())
                    {
                        validation.Errors.Add("apt-export-contains-oversized-file");
                    }
                }
            }
            else
            {
                // For files, validate the file type
                var fileExtension = GetFileExtension(documentDetails.Name);
                if (!IsValidAptFileType(fileExtension))
                {
                    validation.IsValid = false;
                    validation.Errors.Add("apt-export-invalid-file-type");
                }

                // Check file size (APT has 50MB limit)
                if (documentDetails.Size > 50 * 1024 * 1024) // 50MB in bytes
                {
                    validation.IsValid = false;
                    validation.Errors.Add("apt-export-file-exceeds-size");
                }
            }

            return validation;
        }

        private async Task<AptExportRecursiveCounts> GetRecursiveCountsForFolder(
            int memberFirmId,
            int clientId,
            int projectId,
            string folderPath
        )
        {
            var counts = new AptExportRecursiveCounts();
            var processedPaths = new HashSet<string>();

            await ProcessFolderRecursively(
                memberFirmId,
                clientId,
                projectId,
                folderPath,
                counts,
                processedPaths
            );

            return counts;
        }

        private async Task ProcessFolderRecursively(
            int memberFirmId,
            int clientId,
            int projectId,
            string folderPath,
            AptExportRecursiveCounts counts,
            HashSet<string> processedPaths
        )
        {
            if (processedPaths.Contains(folderPath))
                return;

            processedPaths.Add(folderPath);

            try
            {
                var folderContent = await GetFolderContentAsync(
                    folderPath,
                    memberFirmId,
                    clientId,
                    projectId,
                    null
                );

                if (folderContent?.Items == null)
                    return;

                foreach (var item in folderContent.Items)
                {
                    if (item.FileKind == "Folder")
                    {
                        counts.TotalSubfolders++;
                        var subfolderPath = GetRelativeFolderPath(item.Url);
                        await ProcessFolderRecursively(
                            memberFirmId,
                            clientId,
                            projectId,
                            subfolderPath,
                            counts,
                            processedPaths
                        );
                    }
                    else
                    {
                        counts.TotalFiles++;

                        // Validate file
                        var fileExtension = GetFileExtension(item.Name);
                        var isValidType = IsValidAptFileType(fileExtension);
                        var isValidSize = item.Size <= 50 * 1024 * 1024; // 50MB

                        var filePath = GetRelativeFolderPath(item.Url);

                        if (!isValidType)
                        {
                            counts.InvalidFiles.Add(filePath);
                        }

                        if (!isValidSize)
                        {
                            counts.OversizedFiles.Add(filePath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error processing folder recursively: {FolderPath}",
                    folderPath
                );
            }
        }

        private static bool IsValidAptFileType(string? fileExtension)
        {
            if (string.IsNullOrEmpty(fileExtension))
                return true; // Allow files without extension

            // Only block HTML files for APT export - all other file types are allowed
            return !fileExtension.Equals("html", StringComparison.OrdinalIgnoreCase)
                && !fileExtension.Equals("htm", StringComparison.OrdinalIgnoreCase);
        }

        private static string GetFileNameFromPath(string path)
        {
            return Path.GetFileName(path) ?? path;
        }

        private static string GetFileExtension(string fileName)
        {
            return Path.GetExtension(fileName).TrimStart('.');
        }

        public async Task<AptExportResponse> ExportAllDocumentsToAptAsync(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            try
            {
                _logger.LogInformation(
                    "Starting APT export for all documents. MemberFirmId: {MemberFirmId}, ClientId: {ClientId}, ProjectId: {ProjectId}",
                    memberFirmId,
                    clientId,
                    projectId
                );

                // ControlApiClient is already initialized with token in constructor

                // Call BGP API to export all documents
                await _bgpService.ExportAllDocumentsToApt(memberFirmId, clientId, projectId);

                _logger.LogInformation(
                    "Successfully exported all documents to APT. MemberFirmId: {MemberFirmId}, ClientId: {ClientId}, ProjectId: {ProjectId}",
                    memberFirmId,
                    clientId,
                    projectId
                );

                return new AptExportResponse
                {
                    Success = true,
                    Message = "All documents exported to APT successfully",
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error exporting all documents to APT. MemberFirmId: {MemberFirmId}, ClientId: {ClientId}, ProjectId: {ProjectId}",
                    memberFirmId,
                    clientId,
                    projectId
                );

                return new AptExportResponse
                {
                    Success = false,
                    Message = $"Failed to export documents to APT: {ex.Message}",
                };
            }
        }

        public async Task<AptExportResponse> ExportSelectedDocumentsToAptAsync(
            int memberFirmId,
            int clientId,
            int projectId,
            List<int> files,
            List<int> folders
        )
        {
            try
            {
                _logger.LogInformation(
                    "Starting APT export for selected documents. MemberFirmId: {MemberFirmId}, ClientId: {ClientId}, ProjectId: {ProjectId}, Files: {FileCount}, Folders: {FolderCount}",
                    memberFirmId,
                    clientId,
                    projectId,
                    files.Count,
                    folders.Count
                );

                // ControlApiClient is already initialized with token in constructor

                // Call BGP API to export selected documents
                await _bgpService.ExportSelectedDocumentsToApt(
                    memberFirmId,
                    clientId,
                    projectId,
                    files,
                    folders
                );

                _logger.LogInformation(
                    "Successfully exported selected documents to APT. MemberFirmId: {MemberFirmId}, ClientId: {ClientId}, ProjectId: {ProjectId}, Files: {FileCount}, Folders: {FolderCount}",
                    memberFirmId,
                    clientId,
                    projectId,
                    files.Count,
                    folders.Count
                );

                return new AptExportResponse
                {
                    Success = true,
                    Message = $"Successfully exported {files.Count + folders.Count} items to APT",
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error exporting selected documents to APT. MemberFirmId: {MemberFirmId}, ClientId: {ClientId}, ProjectId: {ProjectId}, Files: {FileCount}, Folders: {FolderCount}",
                    memberFirmId,
                    clientId,
                    projectId,
                    files.Count,
                    folders.Count
                );

                return new AptExportResponse
                {
                    Success = false,
                    Message = $"Failed to export documents to APT: {ex.Message}",
                };
            }
        }
    }
}
