"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}Object.defineProperty(exports,"__esModule",{value:!0});var t=require("react"),a=e(t),r=e(require("classnames")),o=require("react-singleton-hook"),n=require("date-fns"),l=require("date-fns/locale"),i=require("i18next"),c=require("react-i18next"),d=require("@ark-ui/react"),s=require("react-router-dom"),_=require("react-dom"),m=require("@lexical/react/LexicalComposer"),u=require("@lexical/react/LexicalRichTextPlugin"),p=require("@lexical/react/LexicalContentEditable"),g=require("@lexical/react/LexicalErrorBoundary"),h=require("@lexical/react/LexicalOnChangePlugin"),f=require("@lexical/react/LexicalHistoryPlugin"),b=require("@lexical/react/LexicalListPlugin"),v=require("@lexical/react/LexicalMarkdownShortcutPlugin"),w=require("@lexical/html"),y=require("lexical"),x=require("@lexical/utils"),E=require("@lexical/list"),C=require("@lexical/react/LexicalHorizontalRuleNode"),k=require("@lexical/rich-text"),T=require("@lexical/code"),I=require("@lexical/link"),z=require("@lexical/react/LexicalComposerContext"),N=require("lexical-beautiful-mentions");function S(e,t){void 0===t&&(t={});var a=t.insertAt;if(e&&"undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css","top"===a&&r.firstChild?r.insertBefore(o,r.firstChild):r.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}var B,L,O,A,D,j,M,P,H,R,V,F,W,U,q,K,G,Y,J,Z,X,Q,$,ee,te,ae,re,oe,ne,le,ie,ce,de,se,_e,me,ue,pe,ge,he,fe,be,ve,we,ye,xe,Ee,Ce,ke,Te,Ie,ze,Ne,Se,Be,Le,Oe,Ae,De,je,Me,Pe,He,Re,Ve,Fe,We,Ue,qe,Ke,Ge,Ye,Je,Ze,Xe,Qe={attachmentHistoryWrapper:"attachmentHistory-module__attachmentHistoryWrapper___K67qx",leftIconWrapper:"attachmentHistory-module__leftIconWrapper___X9gma",historyItems:"attachmentHistory-module__historyItems___apx62",leftHandInfo:"attachmentHistory-module__leftHandInfo___b3mBU",rightHandInfo:"attachmentHistory-module__rightHandInfo___KhzFs",historyItem:"attachmentHistory-module__historyItem___4bGmG",pipe:"attachmentHistory-module__pipe___-F14h",basicInfo:"attachmentHistory-module__basicInfo___ZrMc9","rightHandInfo--primary":"attachmentHistory-module__rightHandInfo--primary___BE-ck","rightHandInfo--secondary":"attachmentHistory-module__rightHandInfo--secondary___O3W7A",documentName:"attachmentHistory-module__documentName___9a7td",nameContainer:"attachmentHistory-module__nameContainer___VXBuy",title:"attachmentHistory-module__title___BZ-Lu",downloadIcon:"attachmentHistory-module__downloadIcon___6wdlD",noDocumentsUploaded:"attachmentHistory-module__noDocumentsUploaded___0fs2x"};function $e(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}function et(e,t,a,r,o,n,l){try{var i=e[n](l),c=i.value}catch(e){return void a(e)}i.done?t(c):Promise.resolve(c).then(r,o)}function tt(e){return function(){var t=this,a=arguments;return new Promise((function(r,o){var n=e.apply(t,a);function l(e){et(n,r,o,l,i,"next",e)}function i(e){et(n,r,o,l,i,"throw",e)}l(void 0)}))}}function at(){return(at=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function rt(e,t){if(null==e)return{};var a={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;a[r]=e[r]}return a}function ot(){ot=function(){return t};var e,t={},a=Object.prototype,r=a.hasOwnProperty,o=Object.defineProperty||function(e,t,a){e[t]=a.value},n="function"==typeof Symbol?Symbol:{},l=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function d(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,a){return e[t]=a}}function s(e,t,a,r){var n=Object.create((t&&t.prototype instanceof h?t:h).prototype),l=new N(r||[]);return o(n,"_invoke",{value:k(e,a,l)}),n}function _(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var m="suspendedStart",u="executing",p="completed",g={};function h(){}function f(){}function b(){}var v={};d(v,l,(function(){return this}));var w=Object.getPrototypeOf,y=w&&w(w(S([])));y&&y!==a&&r.call(y,l)&&(v=y);var x=b.prototype=h.prototype=Object.create(v);function E(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function a(o,n,l,i){var c=_(e[o],e,n);if("throw"!==c.type){var d=c.arg,s=d.value;return s&&"object"==typeof s&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){a("next",e,l,i)}),(function(e){a("throw",e,l,i)})):t.resolve(s).then((function(e){d.value=e,l(d)}),(function(e){return a("throw",e,l,i)}))}i(c.arg)}var n;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){a(e,r,t,o)}))}return n=n?n.then(o,o):o()}})}function k(t,a,r){var o=m;return function(n,l){if(o===u)throw Error("Generator is already running");if(o===p){if("throw"===n)throw l;return{value:e,done:!0}}for(r.method=n,r.arg=l;;){var i=r.delegate;if(i){var c=T(i,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===m)throw o=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=u;var d=_(t,a,r);if("normal"===d.type){if(o=r.done?p:"suspendedYield",d.arg===g)continue;return{value:d.arg,done:r.done}}"throw"===d.type&&(o=p,r.method="throw",r.arg=d.arg)}}}function T(t,a){var r=a.method,o=t.iterator[r];if(o===e)return a.delegate=null,"throw"===r&&t.iterator.return&&(a.method="return",a.arg=e,T(t,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var n=_(o,t.iterator,a.arg);if("throw"===n.type)return a.method="throw",a.arg=n.arg,a.delegate=null,g;var l=n.arg;return l?l.done?(a[t.resultName]=l.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,g):l:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function z(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function S(t){if(t||""===t){var a=t[l];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,n=function a(){for(;++o<t.length;)if(r.call(t,o))return a.value=t[o],a.done=!1,a;return a.value=e,a.done=!0,a};return n.next=n}}throw new TypeError(typeof t+" is not iterable")}return f.prototype=b,o(x,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:f,configurable:!0}),f.displayName=d(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,d(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},E(C.prototype),d(C.prototype,i,(function(){return this})),t.AsyncIterator=C,t.async=function(e,a,r,o,n){void 0===n&&(n=Promise);var l=new C(s(e,a,r,o),n);return t.isGeneratorFunction(a)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},E(x),d(x,c,"Generator"),d(x,l,(function(){return this})),d(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=S,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(z),!t)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function o(r,o){return i.type="throw",i.arg=t,a.next=r,o&&(a.method="next",a.arg=e),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var l=this.tryEntries[n],i=l.completion;if("root"===l.tryLoc)return o("end");if(l.tryLoc<=this.prev){var c=r.call(l,"catchLoc"),d=r.call(l,"finallyLoc");if(c&&d){if(this.prev<l.catchLoc)return o(l.catchLoc,!0);if(this.prev<l.finallyLoc)return o(l.finallyLoc)}else if(c){if(this.prev<l.catchLoc)return o(l.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return o(l.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var l=n?n.completion:{};return l.type=e,l.arg=t,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),z(a),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var o=r.arg;z(a)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,a,r){return this.delegate={iterator:S(t),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function nt(){return(nt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function lt(){return(lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function it(){return(it=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ct(){return(ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function dt(){return(dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function st(){return(st=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function _t(){return(_t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function mt(){return(mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ut(){return(ut=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function pt(){return(pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function gt(){return(gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ht(){return(ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ft(){return(ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function bt(){return(bt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function vt(){return(vt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function wt(){return(wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function yt(){return(yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function xt(){return(xt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Et(){return(Et=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ct(){return(Ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function kt(){return(kt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Tt(){return(Tt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function It(){return(It=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function zt(){return(zt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Nt(){return(Nt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function St(){return(St=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Bt(){return(Bt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Lt(){return(Lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ot(){return(Ot=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function At(){return(At=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Dt(){return(Dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function jt(){return(jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Mt(){return(Mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Pt(){return(Pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ht(){return(Ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Rt(){return(Rt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Vt(){return(Vt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ft(){return(Ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Wt(){return(Wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ut(){return(Ut=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function qt(){return(qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Kt(){return(Kt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Gt(){return(Gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Yt(){return(Yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Jt(){return(Jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Zt(){return(Zt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Xt(){return(Xt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Qt(){return(Qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function $t(){return($t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ea(){return(ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ta(){return(ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function aa(){return(aa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ra(){return(ra=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function oa(){return(oa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function na(){return(na=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function la(){return(la=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ia(){return(ia=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ca(){return(ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function da(){return(da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function sa(){return(sa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function _a(){return(_a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ma(){return(ma=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ua(){return(ua=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function pa(){return(pa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ga(){return(ga=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ha(){return(ha=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function fa(){return(fa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ba(){return(ba=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function va(){return(va=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function wa(){return(wa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}S(".attachmentHistory-module__attachmentHistoryWrapper___K67qx{background-color:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:2px;padding:1rem;strong{color:var(--color-primary-charcoal);line-height:var(--lineheight-body)}.attachmentHistory-module__leftIconWrapper___X9gma{background-color:var(--color-bg-light-grey);border-radius:2px;padding:.5rem;width:min-content;svg{path{fill:var(--color-primary-slate)}}}.attachmentHistory-module__historyItems___apx62{margin:1rem 0 0;padding:0;.attachmentHistory-module__historyItem___4bGmG,.attachmentHistory-module__leftHandInfo___b3mBU,.attachmentHistory-module__rightHandInfo___KhzFs{align-items:center;display:flex;flex-direction:row}>:first-child{border-top:1px solid var(--color-primary-pale-charcoal);border-top-left-radius:2px;border-top-right-radius:2px}>:last-child{border-bottom-left-radius:2px;border-bottom-right-radius:2px}.attachmentHistory-module__historyItem___4bGmG{align-items:center;border-bottom:1px solid var(--color-primary-pale-charcoal);border-left:1px solid var(--color-primary-pale-charcoal);border-right:1px solid var(--color-primary-pale-charcoal);column-gap:.75rem;display:grid;grid-template-columns:1fr auto;padding:.75rem 1rem;.attachmentHistory-module__leftHandInfo___b3mBU{gap:.75rem;min-height:0;min-width:0;.attachmentHistory-module__pipe___-F14h{color:var(--color-border-grey);margin:0 .5rem}.attachmentHistory-module__basicInfo___ZrMc9{color:var(--color-bg-light-charcoal)}}.attachmentHistory-module__rightHandInfo___KhzFs{color:var(--color-bg-inactive-charcoal);gap:.5rem;svg{color:var(--color-primary-charcoal);cursor:pointer}}.attachmentHistory-module__rightHandInfo--primary___BE-ck{svg{color:var(--color-bg-white)}}.attachmentHistory-module__rightHandInfo--secondary___O3W7A{svg{color:var(--color-primary-charcoal)}}.attachmentHistory-module__documentName___9a7td{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);line-height:var(--lineheight-body);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}}}}@media (min-width:769px){.attachmentHistory-module__historyItem___4bGmG{min-width:520px}}.attachmentHistory-module__nameContainer___VXBuy{color:var(--color-bg-inactive-charcoal);font-size:var(--fontsize-body-small);justify-self:end;line-height:var(--fontsize-body-small);white-space:nowrap}.attachmentHistory-module__title___BZ-Lu{font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body)}.attachmentHistory-module__downloadIcon___6wdlD{border-radius:50%;padding:.5rem;&:hover{background-color:var(--color-bg-light-grey)}&:focus-visible{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}}.attachmentHistory-module__noDocumentsUploaded___0fs2x{background-color:#fafafa;border-radius:2px;color:var(--color-primary-inactive-charcoal);font-size:var(--fontsize-body);line-height:var(--lineheight-body);margin-top:1rem;padding:1rem 1.5rem;text-align:center}");var ya,xa,Ea,Ca,ka,Ta,Ia,za,Na,Sa,Ba,La,Oa,Aa,Da,ja,Ma,Pa,Ha,Ra,Va,Fa,Wa,Ua,qa,Ka,Ga,Ya,Ja,Za,Xa,Qa,$a,er,tr,ar,rr,or,nr,lr,ir,cr,dr,sr,_r,mr,ur,pr,gr,hr,fr,br,vr,wr,yr,xr,Er,Cr,kr,Tr,Ir,zr,Nr,Sr,Br,Lr,Or,Ar,Dr,jr,Mr,Pr,Hr,Rr,Vr,Fr,Wr,Ur,qr,Kr,Gr,Yr,Jr,Zr,Xr,Qr,$r,eo,to,ao,ro,oo,no,lo,io,co,so,_o,mo,uo,po,go=function(e){return t.createElement("svg",wa({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Xe||(Xe=t.createElement("path",{fill:"currentColor",d:"M12 16.5a.75.75 0 0 1 .743.648l.007.102v2.188l.72-.718a.75.75 0 0 1 .976-.073l.084.073a.75.75 0 0 1 .073.976l-.073.084-2 2-.038.036-.072.055-.095.055-.086.035-.103.026-.084.011h-.103l-.12-.018-.068-.02-.059-.022-.07-.035-.052-.032-.031-.022a1 1 0 0 1-.08-.069l-2-2a.75.75 0 0 1 .977-1.133l.084.073.72.719v-2.19a.75.75 0 0 1 .648-.742zM12 9a3 3 0 1 1 0 6 3 3 0 0 1 0-6m6.72.47a.75.75 0 0 1 .976-.073l.084.072 2 2 .036.039.056.072.054.095.035.086.027.103.01.084v.103l-.018.12-.019.068-.022.059-.036.07-.032.052-.027.038-.064.072-2 2a.75.75 0 0 1-1.133-.976l.073-.085.718-.72H17.25a.75.75 0 0 1-.743-.647L16.5 12a.75.75 0 0 1 .648-.743l.102-.007h2.19l-.72-.72a.75.75 0 0 1-.073-.976l.073-.085Zm-14.5 0a.75.75 0 0 1 1.133.976l-.073.084-.72.72h2.19a.75.75 0 0 1 .743.648L7.5 12a.75.75 0 0 1-.648.743l-.102.007H4.561l.72.72a.75.75 0 0 1 .072.976l-.073.084a.75.75 0 0 1-.976.073l-.084-.073-2-2-.091-.11-.055-.095-.035-.086-.026-.103-.012-.09v-.093l.019-.125.02-.067.022-.06.035-.07.032-.052.022-.03a1 1 0 0 1 .069-.08zM12 10.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-.136-8.488.067-.009.087-.003.06.004.094.016.067.019.06.022.07.036.051.032.038.027.072.063 2 2a.75.75 0 0 1-.976 1.134l-.084-.073-.72-.72v2.19a.75.75 0 0 1-.648.743L12 7.5a.75.75 0 0 1-.743-.648l-.007-.102V4.56l-.72.72a.75.75 0 0 1-.976.073L9.47 5.28a.75.75 0 0 1-.073-.976l.073-.085 2-2 .11-.09.095-.055.086-.035z"})))};function ho(){return(ho=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function fo(){return(fo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function bo(){return(bo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function vo(){return(vo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function wo(){return(wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function yo(){return(yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function xo(){return(xo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Eo(){return(Eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Co(){return(Co=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ko(){return(ko=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function To(){return(To=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Io(){return(Io=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function zo(){return(zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function No(){return(No=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function So(){return(So=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Bo(){return(Bo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Lo(){return(Lo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Oo(){return(Oo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ao(){return(Ao=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Do(){return(Do=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function jo(){return(jo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Mo(){return(Mo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Po(){return(Po=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ho(){return(Ho=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ro(){return(Ro=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Vo(){return(Vo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Fo(){return(Fo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Wo(){return(Wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Uo(){return(Uo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function qo(){return(qo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ko(){return(Ko=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Go(){return(Go=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Yo(){return(Yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Jo(){return(Jo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Zo(){return(Zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Xo(){return(Xo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Qo(){return(Qo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function $o(){return($o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function en(){return(en=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function tn(){return(tn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function an(){return(an=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function rn(){return(rn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function on(){return(on=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function nn(){return(nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function ln(){return(ln=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function cn(){return(cn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function dn(){return(dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function sn(){return(sn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function _n(){return(_n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function mn(){return(mn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function un(){return(un=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function pn(){return(pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function gn(){return(gn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function hn(){return(hn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function fn(){return(fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function bn(){return(bn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function vn(){return(vn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function wn(){return(wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function yn(){return(yn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function xn(){return(xn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function En(){return(En=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Cn(){return(Cn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function kn(){return(kn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Tn(){return(Tn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function In(){return(In=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function zn(){return(zn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Nn(){return(Nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Sn(){return(Sn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Bn(){return(Bn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Ln(){return(Ln=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function On(){return(On=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function An(){return(An=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Dn(){return(Dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function jn(){return(jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Mn(){return(Mn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Pn(){return(Pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Hn(){return(Hn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Rn(){return(Rn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Vn(){return(Vn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Fn(){return(Fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Wn(){return(Wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Un(){return(Un=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function qn(){return(qn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Kn(){return(Kn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Gn(){return(Gn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Yn(){return(Yn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Jn(){return(Jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Zn(){return(Zn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Xn(){return(Xn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function Qn(){return(Qn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function $n(){return($n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function el(){return(el=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function tl(){return(tl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}var al=at({},{"cash-flow":function(e){return t.createElement("svg",Un({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),to||(to=t.createElement("path",{fill:"#333",d:"M6.25 2A2.25 2.25 0 0 0 4 4.25v15.5A2.25 2.25 0 0 0 6.25 22h7.5A2.25 2.25 0 0 0 16 19.771v-1.52a.75.75 0 0 0-.75-.75c-.454 0-.74-.123-.937-.282-.208-.167-.38-.425-.51-.789-.273-.755-.303-1.75-.303-2.68a.75.75 0 0 0-.202-.512l-.164-.177c-.09-.097-.095-.102-.17-.173-.074-.07-.3-.285-1.184-1.168-.468-.469-.728-.865-.813-1.168a.6.6 0 0 1-.015-.325.7.7 0 0 1 .204-.323.7.7 0 0 1 .322-.204.6.6 0 0 1 .324.016c.303.085.699.346 1.168.815.54.54 1.052 1.047 1.511 1.5.76.752 1.374 1.36 1.72 1.73a.75.75 0 0 0 1.098-1.023 55 55 0 0 0-1.3-1.314V8.06l2.842 2.842c.422.422.659.994.659 1.59v8.758a.75.75 0 0 0 1.5 0v-8.757a3.75 3.75 0 0 0-1.098-2.652L16 5.939v-1.69A2.25 2.25 0 0 0 13.75 2zm6.142 14.94c.096.268.221.534.383.782A2.25 2.25 0 0 0 11.5 19.75v.75h-3v-.75a2.25 2.25 0 0 0-2.25-2.25H5.5v-11h.75A2.25 2.25 0 0 0 8.5 4.25V3.5h3v.75a2.25 2.25 0 0 0 2.25 2.25h.75v3.438l-.47-.468c-.53-.531-1.148-1.008-1.82-1.198a2.1 2.1 0 0 0-1.104-.025 2 2 0 0 0-.633.285 3.5 3.5 0 1 0 1.55 6.324c.038.684.131 1.427.369 2.084M8 12a2 2 0 0 1 1.455-1.925 2.2 2.2 0 0 0 .068.883c.19.673.665 1.291 1.197 1.823l.665.662A2 2 0 0 1 8 12M5.5 4.25a.75.75 0 0 1 .75-.75H7v.75a.75.75 0 0 1-.75.75H5.5zM13 3.5h.75a.75.75 0 0 1 .75.75V5h-.75a.75.75 0 0 1-.75-.75zM14.5 19v.75a.75.75 0 0 1-.75.75H13v-.75a.75.75 0 0 1 .75-.75zM7 20.5h-.75a.75.75 0 0 1-.75-.75V19h.75a.75.75 0 0 1 .75.75z"})))},compliance:function(e){return t.createElement("svg",qn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ao||(ao=t.createElement("path",{fill:"#333",d:"M14.14 2.98a2.25 2.25 0 0 0-3.411-.268L7.264 6.177a2.25 2.25 0 0 0 .343 3.463l2.008 1.339-6.872 6.743a2.51 2.51 0 1 0 3.532 3.565l6.872-6.872 1.217 1.91a2.25 2.25 0 0 0 3.489.384l3.436-3.436a2.25 2.25 0 0 0-.267-3.411l-3.889-2.828a.8.8 0 0 1-.165-.166zm-2.35.792a.75.75 0 0 1 1.136.09l.46.632L9.068 8.81l-.63-.42a.75.75 0 0 1-.114-1.154zM10.34 9.66l3.938-3.938 1.476 2.028q.208.287.496.497l1.958 1.424-3.851 3.851-1.055-1.657a2.25 2.25 0 0 0-.65-.664zm4.842 5.159 4.254-4.255.703.51a.75.75 0 0 1 .089 1.138l-3.437 3.436a.75.75 0 0 1-1.163-.127zm-2.86-1.7-7.109 7.108a1.01 1.01 0 1 1-1.42-1.435l7.097-6.963.93.62a.75.75 0 0 1 .217.222zM14.75 19a.75.75 0 0 0 0 1.5h-2a.75.75 0 0 0 0 1.5h8.5a.75.75 0 1 0 0-1.5h-2a.75.75 0 1 0 0-1.5z"})))},"increasing-brand-awareness":function(e){return t.createElement("svg",Kn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ro||(ro=t.createElement("path",{fill:"#333",d:"M13.025 15.5a5 5 0 0 1 .391-1.5H6.252a2.25 2.25 0 0 0-2.249 2.248v.578c0 .892.319 1.756.899 2.435C6.468 21.095 8.854 22 12 22q1.763 0 3.204-.38l-.294-1.474q-1.275.353-2.91.353c-2.738 0-4.704-.745-5.958-2.213a2.25 2.25 0 0 1-.539-1.461v-.578c0-.414.336-.749.75-.749zM17 7.003a5 5 0 1 0-10 0 5 5 0 0 0 10 0m-8.5 0a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0M22 15.999c0 1.38-.7 2.598-1.763 3.317l-.137.683h-4.2l-.137-.683A4 4 0 1 1 22 15.999m-5.9 5 .159.794A1.5 1.5 0 0 0 17.729 23h.541a1.5 1.5 0 0 0 1.471-1.206L19.9 21z"})))},regulation:function(e){return t.createElement("svg",Gn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),oo||(oo=t.createElement("path",{fill:"#333",d:"M3 3.75A.75.75 0 0 1 3.75 3h16.5a.75.75 0 0 1 0 1.5h-1.042l2.737 6.717A.8.8 0 0 1 22 11.5a3.5 3.5 0 1 1-7 0 .8.8 0 0 1 .055-.283L17.792 4.5H12.75v12h4a2.25 2.25 0 0 1 0 4.5H7.253a2.25 2.25 0 0 1 0-4.5h3.997v-12H6.208l2.737 6.717A.8.8 0 0 1 9 11.5a3.5 3.5 0 1 1-7 0 .8.8 0 0 1 .055-.283L4.792 4.5H3.75A.75.75 0 0 1 3 3.75m3.503 15c0 .414.336.75.75.75h9.497a.75.75 0 0 0 0-1.5H7.253a.75.75 0 0 0-.75.75m.852-6.5h-3.71a2 2 0 0 0 3.71 0m-.22-1.5L5.5 6.738 3.865 10.75zM18.5 13.5a2 2 0 0 0 1.855-1.25h-3.71A2 2 0 0 0 18.5 13.5m-1.635-2.75h3.27L18.5 6.738z"})))},"financial-management":function(e){return t.createElement("svg",Yn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),no||(no=t.createElement("path",{fill:"#333",d:"M14.356 2.595a.25.25 0 0 1 .361-.032l.922.812L12.739 7h1.92l2.106-2.632 1.652 1.457a.25.25 0 0 1 .026.348l-.69.827h1.944a1.75 1.75 0 0 0-.288-2.3l-3.7-3.263a1.75 1.75 0 0 0-2.531.23L8.976 7h1.91zM16.25 14a.75.75 0 0 0 0 1.5h2a.75.75 0 0 0 0-1.5zM4.5 7.25a.75.75 0 0 1 .75-.75h3.128L9.57 5H5.25A2.25 2.25 0 0 0 3 7.25v10.5A3.25 3.25 0 0 0 6.25 21h12a3.25 3.25 0 0 0 3.25-3.25v-6.5A3.25 3.25 0 0 0 18.25 8h-13a.75.75 0 0 1-.75-.75m0 10.5V9.372q.354.126.75.128h13c.966 0 1.75.784 1.75 1.75v6.5a1.75 1.75 0 0 1-1.75 1.75h-12a1.75 1.75 0 0 1-1.75-1.75"})))},"supply-chains":function(e){return t.createElement("svg",Jn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),lo||(lo=t.createElement("path",{fill:"#333",d:"M15.78 3.737a2.25 2.25 0 0 0-1.56 0L9.913 5.33a.25.25 0 0 0-.163.234v2.793a4.8 4.8 0 0 0-1.5 0V5.563a1.75 1.75 0 0 1 1.143-1.641L13.7 2.33a3.75 3.75 0 0 1 2.6 0l4.307 1.592a1.75 1.75 0 0 1 1.143 1.641v10.67a1.75 1.75 0 0 1-1.143 1.642l-4.018 1.484c.105-.292.161-.604.161-.927v-.731l3.337-1.233a.25.25 0 0 0 .163-.235V5.563a.25.25 0 0 0-.163-.234zm-4.01 3.93a.75.75 0 1 1 .49-1.418l2.658.92a.25.25 0 0 0 .163 0l2.65-.92a.75.75 0 1 1 .492 1.418l-2.65.919a1.75 1.75 0 0 1-1.145 0zm-6 7a.75.75 0 0 1 .49-1.418l2.74.948 2.732-.947a.75.75 0 1 1 .49 1.417l-2.472.857v2.7a.75.75 0 0 1-1.5 0v-2.7zM10.3 9.53a3.75 3.75 0 0 0-2.6 0l-4.307 1.592a1.75 1.75 0 0 0-1.143 1.641v5.67a1.75 1.75 0 0 0 1.143 1.642L7.7 21.666c.839.31 1.761.31 2.6 0l4.307-1.592a1.75 1.75 0 0 0 1.143-1.642v-5.67a1.75 1.75 0 0 0-1.143-1.641zm-2.08 1.407a2.25 2.25 0 0 1 1.56 0l4.307 1.592a.25.25 0 0 1 .163.234v5.67a.25.25 0 0 1-.163.235L9.78 20.259a2.25 2.25 0 0 1-1.56 0l-4.307-1.592a.25.25 0 0 1-.163-.235v-5.67a.25.25 0 0 1 .163-.234z"})))},"data-security":function(e){return t.createElement("svg",Zn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),io||(io=t.createElement("path",{fill:"#333",d:"M16.757 9.303a.75.75 0 0 0-1.014-1.106l-5.47 5.015L8.28 11.22a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.037.023zM20.25 5c-2.663 0-5.258-.943-7.8-2.85a.75.75 0 0 0-.9 0C9.008 4.057 6.413 5 3.75 5a.75.75 0 0 0-.75.75V11c0 5.001 2.958 8.676 8.725 10.948a.75.75 0 0 0 .55 0C18.042 19.676 21 16 21 11V5.75a.75.75 0 0 0-.75-.75M4.5 6.478c2.577-.152 5.08-1.09 7.5-2.8 2.42 1.71 4.923 2.648 7.5 2.8V11c0 4.256-2.453 7.379-7.5 9.442C6.953 18.379 4.5 15.256 4.5 11z"})))},"talent-strategy":function(e){return t.createElement("svg",Xn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),co||(co=t.createElement("path",{fill:"#333",d:"M14.754 10c.966 0 1.75.784 1.75 1.75v4.749a4.501 4.501 0 0 1-9.002 0V11.75c0-.966.783-1.75 1.75-1.75zm0 1.5H9.252a.25.25 0 0 0-.25.25v4.749a3.001 3.001 0 0 0 6.002 0V11.75a.25.25 0 0 0-.25-.25M3.75 10h3.381a2.74 2.74 0 0 0-.618 1.5H3.75a.25.25 0 0 0-.25.25v3.249a2.5 2.5 0 0 0 3.082 2.433c.085.504.24.985.453 1.432Q6.539 18.999 6 19A4 4 0 0 1 2 14.999V11.75c0-.966.784-1.75 1.75-1.75m13.125 0h3.375c.966 0 1.75.784 1.75 1.75V15a4 4 0 0 1-5.03 3.866c.214-.448.369-.929.455-1.433q.277.066.575.067a2.5 2.5 0 0 0 2.5-2.5v-3.25a.25.25 0 0 0-.25-.25h-2.757a2.74 2.74 0 0 0-.618-1.5M12 3a3 3 0 1 1 0 6 3 3 0 0 1 0-6m6.5 1a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5m-13 0a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5m6.5.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m6.5 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2m-13 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2"})))},technology:function(e){return t.createElement("svg",Qn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),so||(so=t.createElement("path",{fill:"#333",d:"M19 4.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3M16 6a3 3 0 1 1 2.525 2.963l-2.038 3.358a3 3 0 0 1-4.75 3.65l-3.741 1.87Q8 17.919 8 18a3 3 0 1 1-.465-1.606l3.531-1.765a3 3 0 0 1 4.276-3.313l1.798-2.962A3 3 0 0 1 16 6m-2 6.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m-9 4a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3"})))},"considerations-other":function(e){return t.createElement("svg",$n({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),_o||(_o=t.createElement("path",{fill:"currentColor",d:"M7.75 12a1.75 1.75 0 1 1-3.5 0 1.75 1.75 0 0 1 3.5 0m6 0a1.75 1.75 0 1 1-3.5 0 1.75 1.75 0 0 1 3.5 0M18 13.75a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5"})))},"considerations-triangle":function(e){return t.createElement("svg",el({xmlns:"http://www.w3.org/2000/svg",width:8,height:8,fill:"none",viewBox:"0 0 8 8"},e),mo||(mo=t.createElement("path",{fill:"#fff",d:"M3.476 7.028a.602.602 0 0 0 1.048 0l2.728-4.78a.61.61 0 0 0-.524-.914H1.272a.61.61 0 0 0-.524.915z"})))},"did-you-know":function(e){return t.createElement("svg",tl({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),uo||(uo=t.createElement("g",{clipPath:"url(#a)"},t.createElement("path",{fill:"#333",d:"M5.25 12.251c0 .321.263.584.584.584h2.333c.32 0 .583-.263.583-.584v-.583h-3.5zM7 1.168A4.081 4.081 0 0 0 4.667 8.6v1.318c0 .32.262.583.583.583h3.5c.321 0 .584-.262.584-.583V8.6A4.081 4.081 0 0 0 7 1.168m1.167 6.825v1.342H5.834V7.993c-.887-.624-1.75-1.266-1.75-2.742a2.918 2.918 0 0 1 5.833 0c0 1.453-.88 2.13-1.75 2.742"}))),po||(po=t.createElement("defs",null,t.createElement("clipPath",{id:"a"},t.createElement("path",{fill:"#fff",d:"M0 0h14v14H0z"})))))}},{"action-item-icon":function(e){return t.createElement("svg",nt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),B||(B=t.createElement("path",{fill:"#5B6E7F",d:"M4.5 2A2.5 2.5 0 0 0 2 4.5v7A2.5 2.5 0 0 0 4.5 14h3.672a2.5 2.5 0 0 0 1.767-.732l3.329-3.329A2.5 2.5 0 0 0 14 8.172V4.5A2.5 2.5 0 0 0 11.5 2zM3 4.5A1.5 1.5 0 0 1 4.5 3h7A1.5 1.5 0 0 1 13 4.5V8h-2.5A2.5 2.5 0 0 0 8 10.5V13H4.5A1.5 1.5 0 0 1 3 11.5zm6 8.25V10.5A1.5 1.5 0 0 1 10.5 9h2.25q-.082.124-.19.232l-3.328 3.329q-.107.106-.232.19"})))},"active-clock":function(e){return t.createElement("svg",lt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),L||(L=t.createElement("path",{fill:"#5B6E7F",d:"M8.002.483a.5.5 0 0 1-.466.532 7 7 0 1 0 7.449 7.447.5.5 0 1 1 .998.065A8 8 0 1 1 7.47.017a.5.5 0 0 1 .531.466M9.007.55a.5.5 0 0 1 .59-.391q.841.171 1.608.509a.5.5 0 1 1-.4.916 7 7 0 0 0-1.407-.445.5.5 0 0 1-.39-.59m6.329 4.253a.5.5 0 1 0-.917.4q.293.665.441 1.397a.5.5 0 0 0 .98-.2 8 8 0 0 0-.504-1.597m-2.921-2.888a.5.5 0 0 1 .704-.063q.584.486 1.067 1.074a.5.5 0 1 1-.773.635 7 7 0 0 0-.934-.941.5.5 0 0 1-.064-.705M8 3.5a.5.5 0 0 0-1 0v5a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 0-1H8z"})))},"add-document-icon":function(e){return t.createElement("svg",it({xmlns:"http://www.w3.org/2000/svg",width:17,height:18,fill:"none",viewBox:"0 0 17 18"},e),O||(O=t.createElement("path",{fill:"#404040",d:"M6.5.25A2.5 2.5 0 0 0 4 2.75v2.759q.605-.172 1.25-.231V2.75c0-.69.56-1.25 1.25-1.25h3.75v3.125c0 1.036.84 1.875 1.875 1.875h3.125v8.75c0 .69-.56 1.25-1.25 1.25h-2.822a7 7 0 0 1-1.35 1.25H14a2.5 2.5 0 0 0 2.5-2.5V5.768c0-.498-.198-.974-.55-1.326L12.309.799A1.88 1.88 0 0 0 10.982.25zm8.491 5h-2.866a.625.625 0 0 1-.625-.625V1.759zM11.5 12.125a5.625 5.625 0 1 1-11.25 0 5.625 5.625 0 0 1 11.25 0m-5-2.5a.625.625 0 1 0-1.25 0V11.5H3.375a.625.625 0 1 0 0 1.25H5.25v1.875a.625.625 0 1 0 1.25 0V12.75h1.875a.625.625 0 1 0 0-1.25H6.5z"})))},"add-icon":function(e){return t.createElement("svg",ct({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),A||(A=t.createElement("path",{fill:"#0062B8",d:"M8.5.75a.75.75 0 0 0-1.5 0V7H.75a.75.75 0 0 0 0 1.5H7v6.25a.75.75 0 0 0 1.5 0V8.5h6.25a.75.75 0 0 0 0-1.5H8.5z"})))},"add-icon-small":function(e){return t.createElement("svg",dt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),D||(D=t.createElement("path",{fill:"#333",d:"M8 3.5a.5.5 0 0 0-1 0V7H3.5a.5.5 0 0 0 0 1H7v3.5a.5.5 0 0 0 1 0V8h3.5a.5.5 0 0 0 0-1H8z"})))},"add-square-filled":function(e){return t.createElement("svg",st({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),j||(j=t.createElement("path",{fill:"currentColor",d:"M2 4.5A2.5 2.5 0 0 1 4.5 2h7A2.5 2.5 0 0 1 14 4.5v7a2.5 2.5 0 0 1-2.5 2.5h-7A2.5 2.5 0 0 1 2 11.5zm6 0a.5.5 0 0 0-.5.5v2.5H5a.5.5 0 0 0 0 1h2.5V11a.5.5 0 0 0 1 0V8.5H11a.5.5 0 0 0 0-1H8.5V5a.5.5 0 0 0-.5-.5"})))},"arrow-down":function(e){return t.createElement("svg",mt({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none",viewBox:"0 0 48 48"},e),P||(P=t.createElement("path",{fill:"currentColor",d:"M25.25 5.25a1.25 1.25 0 1 0-2.5 0v32.446L10.141 24.874a1.25 1.25 0 1 0-1.782 1.752l14.75 15a1.25 1.25 0 0 0 1.782 0l14.75-15a1.25 1.25 0 1 0-1.782-1.752L25.25 37.696z"})))},"arrow-download":function(e){return t.createElement("svg",pt({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none",viewBox:"0 0 48 48"},e),R||(R=t.createElement("path",{fill:"currentColor",d:"M25.25 6.25a1.25 1.25 0 1 0-2.5 0v26.482l-8.366-8.366a1.25 1.25 0 0 0-1.768 1.768l10.5 10.5a1.25 1.25 0 0 0 1.768 0l10.5-10.5a1.25 1.25 0 0 0-1.768-1.768l-8.366 8.366zm-14 34.25a1.25 1.25 0 1 0 0 2.5h25.5a1.25 1.25 0 1 0 0-2.5z"})))},"arrow-left":function(e){return t.createElement("svg",ht({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none",viewBox:"0 0 48 48"},e),F||(F=t.createElement("path",{fill:"currentColor",d:"M44 24c0 .69-.56 1.25-1.25 1.25H10.304l12.822 12.609a1.25 1.25 0 1 1-1.752 1.782L6.386 24.903l-.027-.027a1.25 1.25 0 0 1-.352-1.008 1.25 1.25 0 0 1 .393-.785L21.374 8.359a1.25 1.25 0 1 1 1.752 1.782L10.304 22.75H42.75c.69 0 1.25.56 1.25 1.25"})))},"arrow-left-thicker":function(e){return t.createElement("svg",ft({xmlns:"http://www.w3.org/2000/svg",width:16,height:14,fill:"none",viewBox:"0 0 16 14"},e),W||(W=t.createElement("path",{fill:"#333",d:"M15.5 7c0 .518-.42.938-.937.938H3.579l4.109 3.676a.938.938 0 0 1-1.25 1.397L.5 7.7A.94.94 0 0 1 .5 6.3L6.437.99a.937.937 0 1 1 1.25 1.397L3.58 6.063h10.984c.517 0 .937.42.937.937"})))},"arrow-right":function(e){return t.createElement("svg",gt({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none",viewBox:"0 0 48 48"},e),V||(V=t.createElement("path",{fill:"currentColor",d:"M4 24c0-.69.56-1.25 1.25-1.25h32.446L24.874 10.14a1.25 1.25 0 1 1 1.752-1.782l14.988 14.738.027.026a1.25 1.25 0 0 1 .352 1.009 1.25 1.25 0 0 1-.393.785L26.626 39.641a1.25 1.25 0 1 1-1.752-1.782l12.822-12.61H5.25C4.56 25.25 4 24.69 4 24"})))},"arrow-sort-down-line":function(e){return t.createElement("svg",bt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),U||(U=t.createElement("path",{fill:"currentColor",d:"M18 2.75a.75.75 0 0 0-1.5 0v16.69l-2.22-2.22a.75.75 0 1 0-1.06 1.06l3.5 3.5a.75.75 0 0 0 1.06 0l3.5-3.5a.75.75 0 1 0-1.06-1.06L18 19.44zM2.75 4.5a.75.75 0 0 0 0 1.5h10.5a.75.75 0 0 0 0-1.5zM6 9.25a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5A.75.75 0 0 1 6 9.25m4.75 3.25a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5z"})))},"arrow-up":function(e){return t.createElement("svg",ut({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none",viewBox:"0 0 48 48"},e),H||(H=t.createElement("path",{fill:"currentColor",d:"M24 44c-.69 0-1.25-.56-1.25-1.25V10.304L10.14 23.126a1.25 1.25 0 1 1-1.782-1.752L23.097 6.386l.026-.027a1.25 1.25 0 0 1 1.009-.352 1.25 1.25 0 0 1 .785.393l14.724 14.974a1.25 1.25 0 1 1-1.782 1.752l-12.61-12.822V42.75c0 .69-.559 1.25-1.25 1.25"})))},"arrow-upload":function(e){return t.createElement("svg",vt({xmlns:"http://www.w3.org/2000/svg",width:15,height:20,fill:"none",viewBox:"0 0 15 20"},e),q||(q=t.createElement("path",{fill:"currentColor",d:"M14.25 1.509a.75.75 0 1 0 0-1.5l-13-.004a.75.75 0 1 0 0 1.5zM7.648 19.997l.102.007a.75.75 0 0 0 .743-.649l.007-.101-.001-13.685 3.722 3.72a.75.75 0 0 0 .976.073l.085-.073a.75.75 0 0 0 .072-.977l-.073-.084-4.997-4.996a.75.75 0 0 0-.976-.073l-.085.072L2.22 8.228a.75.75 0 0 0 .976 1.134l.084-.073 3.719-3.713L7 19.254c0 .38.282.693.648.743"})))},"attach-icon":function(e){return t.createElement("svg",wt({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none",viewBox:"0 0 32 32"},e),K||(K=t.createElement("path",{fill:"currentColor",d:"M16.223 4.364a8.071 8.071 0 1 1 11.414 11.414l-12.79 12.79a4.89 4.89 0 0 1-6.914-6.915l11.36-11.36a1 1 0 1 1 1.414 1.414l-11.36 11.36a2.89 2.89 0 0 0 4.086 4.086l12.79-12.79a6.071 6.071 0 1 0-8.586-8.585l-13.93 13.93a1 1 0 0 1-1.414-1.414z"})))},"base-filter-arrows":function(e){return t.createElement("svg",yt({xmlns:"http://www.w3.org/2000/svg",width:14,height:12,fill:"none",viewBox:"0 0 14 12"},e),G||(G=t.createElement("path",{fill:"#AAA",d:"M3.854.146a.5.5 0 0 0-.708 0l-3 3a.5.5 0 1 0 .708.708L3 1.707V11.5a.5.5 0 0 0 1 0V1.707l2.146 2.147a.5.5 0 1 0 .708-.708zm6.298 11.714a.5.5 0 0 0 .696 0l3-2.9a.5.5 0 1 0-.696-.72L11 10.321V.501a.5.5 0 0 0-1 0v9.82l-2.152-2.08a.5.5 0 0 0-.696.718z"})))},"bdo-logo":function(e){return t.createElement("svg",xt({xmlns:"http://www.w3.org/2000/svg",width:51,height:21,fill:"none",viewBox:"0 0 51 21"},e),Y||(Y=t.createElement("path",{fill:"#21409A",d:"M9.916 3.222h3.07c.842 0 1.91.202 1.91 1.64 0 1.439-1.526 2.025-2.658 2.025H9.916zm0 5.62h1.749c2.875 0 4.34.632 4.34 2.322 0 1.499-1.175 2.132-3.02 2.132h-2.798l-3.2 2.252h5.977c3.58 0 5.99-1.296 5.99-4.293 0-2.615-2.133-3.642-4.036-3.642 1.315 0 2.87-1.185 2.87-3.194 0-2.007-1.828-3.45-3.913-3.45H6.451l.536 1.195v12.335l2.929-2.061zM23.775 3.222h1.995c.823 0 5.02.21 5.02 5.037 0 5.508-5.02 5.035-5.02 5.035l-1.723.002-3.202 2.252 5.748.001c3.58 0 7.25-2.213 7.25-7.29 0-4.502-3.205-7.29-7.25-7.29H20.31l.534 1.195v12.335l2.93-2.061zM38.248 8.26c0-4.123 2.826-5.282 4.656-5.282s4.657 1.159 4.657 5.281c0 4.123-2.827 5.281-4.657 5.281s-4.657-1.158-4.657-5.28m-3.053 0c0 5.816 4.678 7.45 7.709 7.45s7.709-1.634 7.709-7.45c0-5.818-4.678-7.451-7.709-7.451s-7.709 1.633-7.709 7.45"})),J||(J=t.createElement("path",{fill:"#ED1A3B",d:"M.389.969v18.174L3.25 17.13V.97zM3.785 17.805.389 20.193H50.45v-2.388z"})))},"bdo-white-logo":function(e){return t.createElement("svg",Et({xmlns:"http://www.w3.org/2000/svg",width:56,height:28,fill:"none",viewBox:"0 0 56 28"},e),Z||(Z=t.createElement("path",{fill:"#fff",d:"M10.887 6.034h3.371c.925 0 2.097.22 2.097 1.792s-1.675 2.212-2.918 2.212h-2.55zm0 6.14h1.92c3.158 0 4.766.69 4.766 2.537 0 1.637-1.29 2.33-3.315 2.33h-3.073L7.671 19.5l6.564-.001c3.93 0 6.576-1.416 6.576-4.69 0-2.857-2.341-3.979-4.432-3.979 1.445 0 3.151-1.294 3.151-3.49 0-2.193-2.006-3.769-4.295-3.769H7.083l.588 1.305v13.478l3.216-2.253zM26.104 6.034h2.19c.904 0 5.514.228 5.514 5.503 0 6.018-5.513 5.501-5.513 5.501l-1.892.002-3.516 2.46 6.311.002c3.931 0 7.96-2.418 7.96-7.965 0-4.919-3.518-7.965-7.96-7.965H22.3l.587 1.305v13.478l3.217-2.253zM41.996 11.536c0-4.504 3.103-5.77 5.113-5.77s5.114 1.266 5.114 5.77c0 4.505-3.104 5.77-5.114 5.77s-5.113-1.265-5.113-5.77m-3.352 0c0 6.355 5.137 8.141 8.465 8.141s8.464-1.786 8.464-8.14c0-6.356-5.136-8.14-8.464-8.14s-8.465 1.784-8.465 8.14M.427 3.572V23.43l3.142-2.2V3.571zM4.156 21.963l-3.73 2.61h54.971v-2.61z"})))},"briefcase-icon":function(e){return t.createElement("svg",Ct({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),X||(X=t.createElement("path",{fill:"#242424",d:"M5 5V3.5A1.5 1.5 0 0 1 6.5 2h3A1.5 1.5 0 0 1 11 3.5V5h1a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2zm1-1.5V5h4V3.5a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5m-3 6V12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V9.5c-.418.314-.937.5-1.5.5H9v.5a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5V10H4.5A2.5 2.5 0 0 1 3 9.5M7 9v-.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5V9h2.5A1.5 1.5 0 0 0 13 7.5V7a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v.5A1.5 1.5 0 0 0 4.5 9z"})))},"calendar-ltr":function(e){return t.createElement("svg",kt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Q||(Q=t.createElement("path",{fill:"currentColor",d:"M17.75 3A3.25 3.25 0 0 1 21 6.25v11.5A3.25 3.25 0 0 1 17.75 21H6.25A3.25 3.25 0 0 1 3 17.75V6.25A3.25 3.25 0 0 1 6.25 3zm1.75 5.5h-15v9.25c0 .966.784 1.75 1.75 1.75h11.5a1.75 1.75 0 0 0 1.75-1.75zm-11.75 6a1.25 1.25 0 1 1 0 2.5 1.25 1.25 0 0 1 0-2.5m4.25 0a1.25 1.25 0 1 1 0 2.5 1.25 1.25 0 0 1 0-2.5m-4.25-4a1.25 1.25 0 1 1 0 2.5 1.25 1.25 0 0 1 0-2.5m4.25 0a1.25 1.25 0 1 1 0 2.5 1.25 1.25 0 0 1 0-2.5m4.25 0a1.25 1.25 0 1 1 0 2.5 1.25 1.25 0 0 1 0-2.5m1.5-6H6.25A1.75 1.75 0 0 0 4.5 6.25V7h15v-.75a1.75 1.75 0 0 0-1.75-1.75"})))},"calendar-placeholder":function(e){return t.createElement("svg",Tt({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),$||($=t.createElement("path",{fill:"#333",d:"M4 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2m1 2a1 1 0 1 1-2 0 1 1 0 0 1 2 0m2-2a1 1 0 1 0 0-2 1 1 0 0 0 0 2m1 2a1 1 0 1 1-2 0 1 1 0 0 1 2 0m2-2a1 1 0 1 0 0-2 1 1 0 0 0 0 2m4-5.5A2.5 2.5 0 0 0 11.5 0h-9A2.5 2.5 0 0 0 0 2.5v9A2.5 2.5 0 0 0 2.5 14h9a2.5 2.5 0 0 0 2.5-2.5zM1 4h12v7.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 1 11.5zm1.5-3h9A1.5 1.5 0 0 1 13 2.5V3H1v-.5A1.5 1.5 0 0 1 2.5 1"})))},"checkbox-checkmark-icon":function(e){return t.createElement("svg",zt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),te||(te=t.createElement("path",{fill:"#fff",d:"M14.046 3.486a.75.75 0 0 1-.032 1.06l-7.93 7.474a.85.85 0 0 1-1.188-.022l-2.68-2.72a.75.75 0 1 1 1.068-1.053l2.234 2.267 7.468-7.038a.75.75 0 0 1 1.06.032"})))},checkmark:function(e){return t.createElement("svg",It({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ee||(ee=t.createElement("path",{fill:"currentColor",d:"M4.53 12.97a.75.75 0 0 0-1.06 1.06l4.5 4.5a.75.75 0 0 0 1.06 0l11-11a.75.75 0 0 0-1.06-1.06L8.5 16.94z"})))},"checkmark-circle-filled":function(e){return t.createElement("svg",Nt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),ae||(ae=t.createElement("path",{fill:"currentColor",d:"M8 2a6 6 0 1 1 0 12A6 6 0 0 1 8 2m2.12 4.164L7.25 9.042 5.854 7.646a.5.5 0 1 0-.708.708l1.75 1.75a.5.5 0 0 0 .708 0l3.224-3.234a.5.5 0 0 0-.708-.706"})))},"chevron-double":function(e){return t.createElement("svg",Bt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),oe||(oe=t.createElement("path",{fill:"#5B6E7F",d:"M3.854 8.354a.5.5 0 1 1-.708-.708l4.5-4.5a.5.5 0 0 1 .708 0l4.5 4.5a.5.5 0 0 1-.708.708L8 4.207zm0 4a.5.5 0 0 1-.708-.708l4.5-4.5a.5.5 0 0 1 .708 0l4.5 4.5a.5.5 0 0 1-.708.708L8 8.207z"})))},"chevron-down":function(e){return t.createElement("svg",Lt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ne||(ne=t.createElement("path",{fill:"currentColor",d:"M4.22 8.47a.75.75 0 0 1 1.06 0L12 15.19l6.72-6.72a.75.75 0 1 1 1.06 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L4.22 9.53a.75.75 0 0 1 0-1.06"})))},"chevron-left":function(e){return t.createElement("svg",Dt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ce||(ce=t.createElement("path",{fill:"currentColor",d:"M15.53 4.22a.75.75 0 0 1 0 1.06L8.81 12l6.72 6.72a.75.75 0 1 1-1.06 1.06l-7.25-7.25a.75.75 0 0 1 0-1.06l7.25-7.25a.75.75 0 0 1 1.06 0"})))},"chevron-right":function(e){return t.createElement("svg",jt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),de||(de=t.createElement("path",{fill:"currentColor",d:"M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0"})))},"chevron-up":function(e){return t.createElement("svg",Ot({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),le||(le=t.createElement("path",{fill:"currentColor",d:"M4.22 15.53a.75.75 0 0 0 1.06 0L12 8.81l6.72 6.72a.75.75 0 1 0 1.06-1.06l-7.25-7.25a.75.75 0 0 0-1.06 0l-7.25 7.25a.75.75 0 0 0 0 1.06"})))},"chevron-up-down":function(e){return t.createElement("svg",At({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ie||(ie=t.createElement("path",{fill:"currentColor",d:"M12.54 2.23a.75.75 0 0 0-1.08 0l-6.25 6.5a.75.75 0 1 0 1.08 1.04L12 3.832l5.71 5.938a.75.75 0 1 0 1.08-1.04zm0 19.54a.75.75 0 0 1-1.08 0l-6.25-6.5a.75.75 0 1 1 1.08-1.04L12 20.168l5.71-5.938a.75.75 0 1 1 1.08 1.04z"})))},"circle-filled":function(e){return t.createElement("svg",Yt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),xe||(xe=t.createElement("path",{fill:"currentColor",d:"M8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12"})))},"circle-half-fill":function(e){return t.createElement("svg",Gt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ye||(ye=t.createElement("path",{fill:"currentColor",d:"M2.028 11.25A10 10 0 0 1 12 2c5.27 0 9.589 4.077 9.972 9.25H22V12c0 5.523-4.477 10-10 10S2 17.523 2 12v-.75zM12 3.5a8.5 8.5 0 0 0-8.467 7.75h16.934A8.5 8.5 0 0 0 12 3.5"})))},"circle-line":function(e){return t.createElement("svg",Jt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ee||(Ee=t.createElement("path",{fill:"currentColor",d:"M3.533 11.25h16.934a8.5 8.5 0 0 0-16.934 0m16.934 1.5H3.533a8.5 8.5 0 0 0 16.934 0M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12"})))},"client-logo":function(e){return t.createElement("svg",Ht({xmlns:"http://www.w3.org/2000/svg",width:28,height:28,fill:"none",viewBox:"0 0 28 28"},e),me||(me=t.createElement("rect",{width:28,height:28,fill:"#666",rx:4})),ue||(ue=t.createElement("path",{fill:"#fff",d:"M13.452 5.623c.175-.469.925-.47 1.1-.001.573 1.535 1.554 3.778 2.75 4.975 1.197 1.196 3.44 2.177 4.976 2.75.468.176.467.926-.002 1.1-1.53.569-3.762 1.544-4.973 2.755-1.208 1.208-2.182 3.433-2.751 4.963-.175.47-.93.47-1.104 0-.57-1.53-1.543-3.755-2.75-4.963-1.209-1.208-3.434-2.181-4.963-2.75-.47-.175-.47-.93 0-1.104 1.53-.57 3.754-1.543 4.962-2.751 1.211-1.211 2.187-3.444 2.755-4.974"})))},"comment-icon":function(e){return t.createElement("svg",Rt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),pe||(pe=t.createElement("path",{fill:"currentColor",d:"M5.25 18A3.25 3.25 0 0 1 2 14.75v-8.5A3.25 3.25 0 0 1 5.25 3h13.5A3.25 3.25 0 0 1 22 6.25v8.5A3.25 3.25 0 0 1 18.75 18h-5.738L8 21.75a1.25 1.25 0 0 1-1.999-1V18zm7.264-1.5h6.236a1.75 1.75 0 0 0 1.75-1.75v-8.5a1.75 1.75 0 0 0-1.75-1.75H5.25A1.75 1.75 0 0 0 3.5 6.25v8.5c0 .966.784 1.75 1.75 1.75h2.249v3.75z"})))},"close-icon":function(e){return t.createElement("svg",Mt({xmlns:"http://www.w3.org/2000/svg",width:13,height:12,fill:"none",viewBox:"0 0 13 12"},e),se||(se=t.createElement("path",{fill:"#333",d:"M.897.554.97.47a.75.75 0 0 1 .976-.073L2.03.47 6.5 4.939l4.47-4.47a.75.75 0 1 1 1.06 1.061L7.561 6l4.47 4.47a.75.75 0 0 1 .072.976l-.073.084a.75.75 0 0 1-.976.073l-.084-.073L6.5 7.061l-4.47 4.47A.75.75 0 0 1 .97 10.47L5.439 6 .969 1.53A.75.75 0 0 1 .898.554L.97.47z"})))},"close-icon-white":function(e){return t.createElement("svg",Pt({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),_e||(_e=t.createElement("path",{fill:"#fff",d:"M.397.554.47.47a.75.75 0 0 1 .976-.073L1.53.47 8 6.939l6.47-6.47a.75.75 0 1 1 1.06 1.061L9.061 8l6.47 6.47a.75.75 0 0 1 .072.976l-.073.084a.75.75 0 0 1-.976.073l-.084-.073L8 9.061l-6.47 6.47A.75.75 0 0 1 .47 14.47L6.939 8 .469 1.53A.75.75 0 0 1 .398.554L.47.47z"})))},"color-line":function(e){return t.createElement("svg",Zt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ce||(Ce=t.createElement("path",{fill:"currentColor",d:"M4.38 14.003A2.5 2.5 0 0 0 2 16.5v3A2.5 2.5 0 0 0 4.5 22h15a2.5 2.5 0 0 0 2.5-2.5v-3a2.5 2.5 0 0 0-2.5-2.5h-5.954l-1.316 1.314a3 3 0 0 1-.203.186H19.5a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-15a1 1 0 0 1-1-1v-3a1 1 0 0 1 .504-.869 2 2 0 0 1 .092-.744z"})),ke||(ke=t.createElement("path",{fill:"currentColor",d:"M18.648 2.944a3.22 3.22 0 0 1-.002 4.551l-7.123 7.111a2.25 2.25 0 0 1-.942.563l-4.294 1.289a1 1 0 0 1-1.239-1.265l1.362-4.228c.11-.34.298-.65.552-.902l7.132-7.122a3.22 3.22 0 0 1 4.554.003m-3.494 1.059-7.133 7.121a.75.75 0 0 0-.184.301l-1.07 3.322 3.382-1.015a.75.75 0 0 0 .315-.187l7.121-7.11a1.718 1.718 0 1 0-2.43-2.432Z"})))},"copy-document-icon":function(e){return t.createElement("svg",Ut({xmlns:"http://www.w3.org/2000/svg",width:13,height:16,fill:"none",viewBox:"0 0 13 16"},e),be||(be=t.createElement("path",{fill:"#242424",d:"M2 2a2 2 0 0 1 2-2h3.586a1.5 1.5 0 0 1 1.06.44l3.915 3.914A1.5 1.5 0 0 1 13 5.414V12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V6H8.5A1.5 1.5 0 0 1 7 4.5V1zm4 .207V4.5a.5.5 0 0 0 .5.5h3.293zM0 3a1 1 0 0 1 1-1v10a3 3 0 0 0 3 3h7a1 1 0 0 1-1 1H3.94A3.94 3.94 0 0 1 0 12.06z"})))},"complete-icon":function(e){return t.createElement("svg",Rn({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Gr||(Gr=t.createElement("g",{clipPath:"url(#a)"},t.createElement("rect",{width:16,height:16,fill:"#fff",rx:8}),t.createElement("path",{fill:"#218F8B",d:"M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0m3.358 5.646a.5.5 0 0 0-.637-.057l-.07.057L7 9.298 5.354 7.651l-.07-.058a.5.5 0 0 0-.695.696l.057.07 2 2 .07.057a.5.5 0 0 0 .568 0l.07-.058 4.004-4.004.058-.07a.5.5 0 0 0-.058-.638"}))),Yr||(Yr=t.createElement("defs",null,t.createElement("clipPath",{id:"a"},t.createElement("rect",{width:16,height:16,fill:"#fff",rx:8})))))},"copy-elipses":function(e){return t.createElement("svg",qt({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),ve||(ve=t.createElement("path",{fill:"#333",d:"M7.25 5a.75.75 0 0 1 .11 1.492l-.11.008H5a3.5 3.5 0 0 0-.206 6.994L5 13.5h2.25a.75.75 0 0 1 .11 1.492L7.25 15H5a5 5 0 0 1-.25-9.994L5 5zM15 5a5 5 0 0 1 .25 9.994L15 15h-2.25a.75.75 0 0 1-.11-1.492l.11-.008H15a3.5 3.5 0 0 0 .206-6.994L15 6.5h-2.25a.75.75 0 0 1-.11-1.492L12.75 5zM5 9.25h10a.75.75 0 0 1 .102 1.493L15 10.75H5a.75.75 0 0 1-.102-1.493zh10z"})))},"copy-icon":function(e){return t.createElement("svg",Vt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ge||(ge=t.createElement("path",{fill:"currentColor",d:"M5.503 4.627 5.5 6.75v10.504a3.25 3.25 0 0 0 3.25 3.25h8.616a2.25 2.25 0 0 1-2.122 1.5H8.75A4.75 4.75 0 0 1 4 17.254V6.75c0-.98.627-1.815 1.503-2.123M17.75 2A2.25 2.25 0 0 1 20 4.25v13a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-13A2.25 2.25 0 0 1 8.75 2zm0 1.5h-9a.75.75 0 0 0-.75.75v13c0 .414.336.75.75.75h9a.75.75 0 0 0 .75-.75v-13a.75.75 0 0 0-.75-.75"})))},"copy-select":function(e){return t.createElement("svg",Ft({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),he||(he=t.createElement("path",{fill:"currentColor",d:"M9.25 3.5A1.75 1.75 0 0 0 7.5 5.25v1a.75.75 0 0 1-1.5 0v-1A3.25 3.25 0 0 1 9.25 2h1a.75.75 0 0 1 0 1.5zM12 2.75a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75m.75 13.75a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5zm-6-8.5a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-1.5 0v-2.5A.75.75 0 0 1 6.75 8M22 8.75a.75.75 0 0 0-1.5 0v2.5a.75.75 0 0 0 1.5 0zm-3.25 7.75a1.75 1.75 0 0 0 1.75-1.75v-1a.75.75 0 0 1 1.5 0v1A3.25 3.25 0 0 1 18.75 18h-1a.75.75 0 0 1 0-1.5zM20.5 5.25a1.75 1.75 0 0 0-1.75-1.75h-1a.75.75 0 0 1 0-1.5h1A3.25 3.25 0 0 1 22 5.25v1a.75.75 0 0 1-1.5 0zM9.25 16.5a1.75 1.75 0 0 1-1.75-1.75v-1a.75.75 0 0 0-1.5 0v1A3.25 3.25 0 0 0 9.25 18h1a.75.75 0 0 0 0-1.5zM2 9.25a3.25 3.25 0 0 1 3-3.24v1.508A1.75 1.75 0 0 0 3.5 9.25v7a4.25 4.25 0 0 0 4.25 4.25h7a1.75 1.75 0 0 0 1.732-1.5h1.509a3.25 3.25 0 0 1-3.241 3h-7A5.75 5.75 0 0 1 2 16.25z"})))},"copytoclipboard-icon":function(e){return t.createElement("svg",Wt({xmlns:"http://www.w3.org/2000/svg",width:12,height:16,fill:"none",viewBox:"0 0 12 16"},e),fe||(fe=t.createElement("path",{fill:"#5B6E7F",d:"M4 0a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zM3 2a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zM0 4a2 2 0 0 1 1-1.732V12.5A2.5 2.5 0 0 0 3.5 15h6.232A2 2 0 0 1 8 16H3.5A3.5 3.5 0 0 1 0 12.5z"})))},"default-profile-icon":function(e){return t.createElement("svg",Xt({xmlns:"http://www.w3.org/2000/svg",width:20,height:24,fill:"none",viewBox:"0 0 20 24"},e),Te||(Te=t.createElement("path",{fill:"#fff",d:"M17 14a3 3 0 0 1 3 3v.715C20 21.292 15.79 24 10 24S0 21.433 0 17.715V17a3 3 0 0 1 3-3zm0 1.5H3a1.5 1.5 0 0 0-1.493 1.355L1.5 17v.715c0 2.674 3.389 4.785 8.5 4.785 4.926 0 8.355-2.105 8.496-4.624l.004-.161V17a1.5 1.5 0 0 0-1.355-1.493zM10 0a6 6 0 1 1 0 12 6 6 0 0 1 0-12m0 1.5a4.5 4.5 0 1 0 0 9 4.5 4.5 0 0 0 0-9"})))},"delayed-icon":function(e){return t.createElement("svg",Vn({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Jr||(Jr=t.createElement("g",{clipPath:"url(#a)"},t.createElement("rect",{width:16,height:16,fill:"#fff",rx:8}),t.createElement("path",{fill:"#D67900",d:"M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0m0 1a7 7 0 1 0 0 14A7 7 0 0 0 8 1m0 9.5A.75.75 0 1 1 8 12a.75.75 0 0 1 0-1.5M8 4a.5.5 0 0 1 .492.41l.008.09V9a.5.5 0 0 1-.992.09L7.5 9V4.5A.5.5 0 0 1 8 4"}))),Zr||(Zr=t.createElement("defs",null,t.createElement("clipPath",{id:"a"},t.createElement("rect",{width:16,height:16,fill:"#fff",rx:8})))))},"delete-icon":function(e){return t.createElement("svg",Qt({xmlns:"http://www.w3.org/2000/svg",width:16,height:17,fill:"none",viewBox:"0 0 16 17"},e),Ie||(Ie=t.createElement("path",{fill:"currentColor",d:"M6.5 3h3a1.5 1.5 0 1 0-3 0m-1 0a2.5 2.5 0 0 1 5 0h5a.5.5 0 0 1 0 1h-1.054l-1.194 10.344A3 3 0 0 1 10.272 17H5.728a3 3 0 0 1-2.98-2.656L1.554 4H.5a.5.5 0 0 1 0-1zM3.741 14.23A2 2 0 0 0 5.728 16h4.544a2 2 0 0 0 1.987-1.77L13.439 4H2.561zM6.5 6.5A.5.5 0 0 1 7 7v6a.5.5 0 0 1-1 0V7a.5.5 0 0 1 .5-.5M10 7a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"})))},"delete-icon-red":function(e){return t.createElement("svg",$t({xmlns:"http://www.w3.org/2000/svg",width:17,height:18,fill:"none",viewBox:"0 0 17 18"},e),ze||(ze=t.createElement("path",{fill:"#98002E",d:"M9.5 3a1.5 1.5 0 1 0-3 0zm-4 0a2.5 2.5 0 0 1 5 0h5a.5.5 0 0 1 0 1h-1.054l-.485 4.196a5.5 5.5 0 0 0-.986-.176L13.44 4H2.561l1.18 10.23A2 2 0 0 0 5.728 16H7.6q.276.538.657 1H5.728a3 3 0 0 1-2.98-2.656L1.554 4H.5a.5.5 0 0 1 0-1zM17 13.5a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0m-2.646-1.146a.5.5 0 0 0-.708-.708L12.5 12.793l-1.146-1.147a.5.5 0 0 0-.708.708l1.147 1.146-1.147 1.146a.5.5 0 0 0 .708.708l1.146-1.147 1.146 1.147a.5.5 0 0 0 .708-.708L13.207 13.5z"})))},"delete-off-icon":function(e){return t.createElement("svg",ea({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ne||(Ne=t.createElement("path",{fill:"currentColor",d:"M3.94 5 2.22 3.28a.75.75 0 0 1 1.06-1.06l18.5 18.5a.75.75 0 0 1-1.06 1.06l-2.293-2.292-.003.031a2.75 2.75 0 0 1-2.561 2.476l-.176.005H8.313a2.75 2.75 0 0 1-2.714-2.307l-.023-.174L4.295 6.5H3.5a.75.75 0 0 1-.743-.648L2.75 5.75a.75.75 0 0 1 .648-.743L3.5 5zm13.115 13.116L14.5 15.56V17a.75.75 0 0 1-1.493.102L13 17v-2.94l-2-2V17a.75.75 0 0 1-1.493.102L9.5 17v-6.44L5.842 6.903l1.227 12.47a1.25 1.25 0 0 0 1.117 1.122l.127.006h7.374c.6 0 1.109-.425 1.225-1.002l.02-.126.123-1.256Zm-4.037-8.28 1.482 1.482V10l-.007-.102a.75.75 0 0 0-1.475-.062"})),Se||(Se=t.createElement("path",{fill:"currentColor",d:"m18.197 6.5-.762 7.753 1.372 1.372.897-9.125h.796l.102-.007A.75.75 0 0 0 20.5 5h-5.25l-.005-.184A3.25 3.25 0 0 0 8.75 5h-.568l1.5 1.5zM12 3.25c.966 0 1.75.784 1.75 1.75h-3.5l.006-.144A1.75 1.75 0 0 1 12 3.25"})))},"dismiss-circle-filled":function(e){return t.createElement("svg",ta({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Be||(Be=t.createElement("path",{fill:"currentColor",d:"M8 2a6 6 0 1 1 0 12A6 6 0 0 1 8 2M6.534 5.839a.5.5 0 0 0-.638.057l-.057.07a.5.5 0 0 0 .057.638L7.293 8 5.896 9.396l-.057.07a.5.5 0 0 0 .057.638l.07.057a.5.5 0 0 0 .638-.057L8 8.707l1.396 1.397.07.057a.5.5 0 0 0 .638-.057l.057-.07a.5.5 0 0 0-.057-.638L8.707 8l1.397-1.396.057-.07a.5.5 0 0 0-.057-.638l-.07-.057a.5.5 0 0 0-.638.057L8 7.293 6.604 5.896z"})))},"dismiss-icon":function(e){return t.createElement("svg",aa({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Le||(Le=t.createElement("path",{fill:"#404040",d:"m2.589 2.716.057-.07a.5.5 0 0 1 .638-.057l.07.057L8 7.293l4.646-4.647a.5.5 0 0 1 .708.708L8.707 8l4.647 4.646a.5.5 0 0 1 .057.638l-.057.07a.5.5 0 0 1-.638.057l-.07-.057L8 8.707l-4.646 4.647a.5.5 0 0 1-.708-.708L7.293 8 2.646 3.354a.5.5 0 0 1-.057-.638l.057-.07z"})))},"dismiss-icon-thicker":function(e){return t.createElement("svg",ra({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Oe||(Oe=t.createElement("path",{fill:"#404040",d:"m2.996 3.192.091-.105a.94.94 0 0 1 1.22-.09l.106.09L10 8.674l5.587-5.587a.937.937 0 1 1 1.326 1.326L11.326 10l5.587 5.587a.94.94 0 0 1 .09 1.22l-.09.106a.94.94 0 0 1-1.22.09l-.106-.09L10 11.326l-5.587 5.587a.938.938 0 0 1-1.326-1.326L8.674 10 3.087 4.413a.94.94 0 0 1-.09-1.22l.09-.106z"})))},"document-add":function(e){return t.createElement("svg",oa({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ae||(Ae=t.createElement("path",{fill:"currentColor",d:"M18.5 20a.5.5 0 0 1-.5.5h-5.732A6.5 6.5 0 0 1 11.19 22H18a2 2 0 0 0 2-2V9.828a2 2 0 0 0-.586-1.414l-5.829-5.828a1 1 0 0 0-.049-.04l-.036-.03a2 2 0 0 0-.219-.18 1 1 0 0 0-.08-.044l-.048-.024-.05-.029c-.054-.031-.109-.063-.166-.087a2 2 0 0 0-.624-.138l-.059-.007A1 1 0 0 0 12.172 2H6a2 2 0 0 0-2 2v7.498a6.5 6.5 0 0 1 1.5-.422V4a.5.5 0 0 1 .5-.5h6V8a2 2 0 0 0 2 2h4.5zm-5-15.379L17.378 8.5H14a.5.5 0 0 1-.5-.5z"})),De||(De=t.createElement("path",{fill:"currentColor",d:"M12 17.5a5.5 5.5 0 1 0-11 0 5.5 5.5 0 0 0 11 0M7 18l.001 2.503a.5.5 0 1 1-1 0V18H3.496a.5.5 0 0 1 0-1H6v-2.5a.5.5 0 1 1 1 0V17h2.497a.5.5 0 0 1 0 1z"})))},"document-archive":function(e){return t.createElement("svg",na({xmlns:"http://www.w3.org/2000/svg",width:16,height:14,fill:"none",viewBox:"0 0 16 14"},e),je||(je=t.createElement("path",{fill:"#5B6E7F",d:"M0 2.5A2.5 2.5 0 0 1 2.5 0h2.482c.464 0 .91.184 1.238.513L7.707 2H13.5A2.5 2.5 0 0 1 16 4.5v7a2.5 2.5 0 0 1-2.5 2.5h-11A2.5 2.5 0 0 1 0 11.5zM12 13h1.5a1.5 1.5 0 0 0 1.5-1.5v-7A1.5 1.5 0 0 0 13.5 3H13v3.5a.5.5 0 0 1-.5.5H12v2h.5a.5.5 0 0 1 0 1H12zm0-10h-1v3h1zm-2 0H7.707l-1.56 1.56A1.5 1.5 0 0 1 5.085 5H1v6.5A1.5 1.5 0 0 0 2.5 13H11v-2h-.5a.5.5 0 0 1 0-1h.5V9h-.5a.5.5 0 0 1 0-1h.5V7h-.5a.5.5 0 0 1-.5-.5zM2.5 1A1.5 1.5 0 0 0 1 2.5V4h4.086a.5.5 0 0 0 .353-.146L6.793 2.5l-1.28-1.28a.75.75 0 0 0-.53-.22z"})))},"document-code":function(e){return t.createElement("svg",la({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),Me||(Me=t.createElement("path",{fill:"#5B6E7F",d:"M3 0a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V3a3 3 0 0 0-3-3zM1 3a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2zm4.854 1.854a.5.5 0 1 0-.708-.708l-2.5 2.5a.5.5 0 0 0 0 .708l2.5 2.5a.5.5 0 0 0 .708-.708L3.707 7zm3-.708a.5.5 0 1 0-.708.708L10.293 7 8.146 9.146a.5.5 0 1 0 .708.708l2.5-2.5a.5.5 0 0 0 0-.708z"})))},"document-copy-icon":function(e){return t.createElement("svg",Hn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Kr||(Kr=t.createElement("path",{fill:"currentColor",d:"M5.503 4.627 5.5 6.75v10.504a3.25 3.25 0 0 0 3.25 3.25h8.616a2.25 2.25 0 0 1-2.122 1.5H8.75A4.75 4.75 0 0 1 4 17.254V6.75c0-.98.627-1.815 1.503-2.123M13.128 2c.597 0 1.17.237 1.592.66l4.618 4.62c.422.422.659.995.659 1.591v8.383a2.25 2.25 0 0 1-2.25 2.25H8.752a2.25 2.25 0 0 1-2.25-2.25V4.25A2.25 2.25 0 0 1 8.752 2zM13 3.5H8.752a.75.75 0 0 0-.75.75v13.004c0 .414.336.75.75.75h8.995a.75.75 0 0 0 .75-.75V9.003H15.25a2.25 2.25 0 0 1-2.245-2.095L13 6.754zm1.5 1.061.001 2.193c0 .38.282.693.648.743l.102.007 2.19-.001z"})))},"document-csv":function(e){return t.createElement("svg",ga({xmlns:"http://www.w3.org/2000/svg",width:12,height:16,fill:"none",viewBox:"0 0 12 16"},e),Ke||(Ke=t.createElement("path",{fill:"#096",d:"M2 8.5A1.5 1.5 0 0 1 3.5 7h5A1.5 1.5 0 0 1 10 8.5v4A1.5 1.5 0 0 1 8.5 14h-5A1.5 1.5 0 0 1 2 12.5zM4 13v-2H3v1.5a.5.5 0 0 0 .5.5zm1-3h4V8.5a.5.5 0 0 0-.5-.5H5zm0 3h3.5a.5.5 0 0 0 .5-.5V11H5zM3.5 8a.5.5 0 0 0-.5.5V10h1V8zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V5.414a1.5 1.5 0 0 0-.44-1.06L7.647.439A1.5 1.5 0 0 0 6.586 0zM1 2a1 1 0 0 1 1-1h4v3.5A1.5 1.5 0 0 0 7.5 6H11v8a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1zm9.793 3H7.5a.5.5 0 0 1-.5-.5V1.207z"})))},"document-data":function(e){return t.createElement("svg",ia({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Pe||(Pe=t.createElement("path",{fill:"currentColor",d:"M8.75 11a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5a.75.75 0 0 1 .75-.75m4 4.75a.75.75 0 0 0-1.5 0v2.5a.75.75 0 0 0 1.5 0zm2.5-2.75a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-4.5a.75.75 0 0 1 .75-.75M12.483 2c.331 0 .65.132.884.366L16.22 5.22l.025.025 3.169 3.169A2 2 0 0 1 20 9.828V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2zM6 3.5a.5.5 0 0 0-.5.5v16a.5.5 0 0 0 .5.5h12a.5.5 0 0 0 .5-.5V10H14a2 2 0 0 1-2-2V3.5zm7.5 1.121V8a.5.5 0 0 0 .5.5h3.378z"})))},"document-doc":function(e){return t.createElement("svg",pa({xmlns:"http://www.w3.org/2000/svg",width:12,height:16,fill:"none",viewBox:"0 0 12 16"},e),qe||(qe=t.createElement("path",{fill:"#0062B8",d:"M2.5 8a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1zm0 2a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1zm0 2a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1zM0 2a2 2 0 0 1 2-2h4.586a1.5 1.5 0 0 1 1.06.44l3.915 3.914A1.5 1.5 0 0 1 12 5.414V14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V6H7.5A1.5 1.5 0 0 1 6 4.5V1zm5.5 4h3.293L7 1.207V4.5a.5.5 0 0 0 .5.5"})))},"document-email":function(e){return t.createElement("svg",ca({xmlns:"http://www.w3.org/2000/svg",width:16,height:13,fill:"none",viewBox:"0 0 16 13"},e),He||(He=t.createElement("path",{fill:"#5B6E7F",d:"M13.5 0A2.5 2.5 0 0 1 16 2.5v8a2.5 2.5 0 0 1-2.5 2.5h-11A2.5 2.5 0 0 1 0 10.5v-8A2.5 2.5 0 0 1 2.5 0zM15 3.961l-6.746 3.97a.5.5 0 0 1-.426.038l-.082-.038L1 3.963V10.5A1.5 1.5 0 0 0 2.5 12h11a1.5 1.5 0 0 0 1.5-1.5zM13.5 1h-11A1.5 1.5 0 0 0 1 2.5v.302L8 6.92 15 2.8V2.5A1.5 1.5 0 0 0 13.5 1"})))},"document-folder":function(e){return t.createElement("svg",sa({xmlns:"http://www.w3.org/2000/svg",width:14,height:13,fill:"none",viewBox:"0 0 14 13"},e),Ve||(Ve=t.createElement("path",{fill:"#5B6E7F",d:"M0 2.5A2.5 2.5 0 0 1 2.5 0h2.086a1.5 1.5 0 0 1 1.06.44L7 1.792l-2.06 2.06A.5.5 0 0 1 4.585 4H0zM0 5v4.5A2.5 2.5 0 0 0 2.5 12h4.585a1.5 1.5 0 0 1 .297-.5 1.5 1.5 0 0 1-.382-1c0-.384.144-.735.382-1A1.5 1.5 0 0 1 8.5 7h5q.264.001.5.085V4.5A2.5 2.5 0 0 0 11.5 2H8.207l-2.56 2.56A1.5 1.5 0 0 1 4.585 5zm8.5 3a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1zm0 2a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1zM8 12.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5"})))},"document-folder-outline":function(e){return t.createElement("svg",_a({xmlns:"http://www.w3.org/2000/svg",width:14,height:13,fill:"none",viewBox:"0 0 14 13"},e),Fe||(Fe=t.createElement("path",{fill:"#5B6E7F",d:"M1 2.5V4h3.586a.5.5 0 0 0 .353-.146L6.293 2.5 4.939 1.146A.5.5 0 0 0 4.586 1H2.5A1.5 1.5 0 0 0 1 2.5m-1 0A2.5 2.5 0 0 1 2.5 0h2.086a1.5 1.5 0 0 1 1.06.44L7.207 2H11.5A2.5 2.5 0 0 1 14 4.5v2.585A1.5 1.5 0 0 0 13.5 7H13V4.5A1.5 1.5 0 0 0 11.5 3H7.207l-1.56 1.56A1.5 1.5 0 0 1 4.585 5H1v4.5A1.5 1.5 0 0 0 2.5 11h4.585a1.5 1.5 0 0 0 .297.5 1.5 1.5 0 0 0-.297.5H2.5A2.5 2.5 0 0 1 0 9.5zM8.5 8a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1zm0 2a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1zM8 12.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5"})))},"document-pdf":function(e){return t.createElement("svg",ma({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),We||(We=t.createElement("path",{fill:"#E81A3B",d:"M3.625 9.254A.625.625 0 0 0 3 9.88v2.5a.625.625 0 1 0 1.25 0v-.208h.416a1.459 1.459 0 0 0 0-2.917zm1.041 1.667H4.25v-.417h.416a.209.209 0 0 1 0 .417m5.835-1.043c0-.344.28-.624.625-.624h1.248a.625.625 0 1 1 0 1.25h-.624v.418h.624a.625.625 0 0 1 0 1.25h-.624v.206a.625.625 0 1 1-1.25.003l-.002-.835zm-3.128-.624a.625.625 0 0 0-.625.625v2.5c0 .345.28.625.625.625H8a1.875 1.875 0 1 0 0-3.75zm.625 2.5v-1.25H8a.625.625 0 0 1 0 1.25zM1.75 2.378v4.478A1.88 1.88 0 0 0 .5 8.624v5.004c0 1.036.84 1.875 1.875 1.875h11.25c1.036 0 1.875-.84 1.875-1.875V8.624c0-.816-.522-1.511-1.25-1.768V4.767c0-.498-.198-.975-.55-1.327l-2.395-2.39A1.88 1.88 0 0 0 9.98.504H3.625c-1.036 0-1.875.84-1.875 1.875m1.875-.625H9.25v1.872c0 1.036.84 1.875 1.875 1.875H13V6.75H3V2.378c0-.345.28-.625.625-.625m6.875.26 2.242 2.237h-1.617a.625.625 0 0 1-.625-.625zM2.375 8h11.25c.345 0 .625.28.625.625v5.004c0 .345-.28.625-.625.625H2.375a.625.625 0 0 1-.625-.625V8.624c0-.345.28-.625.625-.625"})))},"document-png":function(e){return t.createElement("svg",ha({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),Ge||(Ge=t.createElement("path",{fill:"#5B6E7F",d:"M11 4.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m-1 0a.5.5 0 1 0-1 0 .5.5 0 0 0 1 0M0 3a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H3a3 3 0 0 1-3-3zm3-2a2 2 0 0 0-2 2v8c0 .373.102.722.28 1.02l4.669-4.588a1.5 1.5 0 0 1 2.102 0l4.67 4.588A2 2 0 0 0 13 11V3a2 2 0 0 0-2-2zm0 12h8c.37 0 .715-.1 1.012-.274L7.35 8.146a.5.5 0 0 0-.7 0l-4.662 4.58A2 2 0 0 0 3 13"})))},"document-ppt":function(e){return t.createElement("svg",ua({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Ue||(Ue=t.createElement("path",{fill:"#D67900",d:"M7 10a1 1 0 0 1-1-1V3.083A6.002 6.002 0 0 0 7 15a6 6 0 0 0 5.917-5zm-.997-7.93C6.55 1.992 7 2.448 7 3v6h6c.552 0 1.008.45.93.997A7.002 7.002 0 0 1 0 9a7 7 0 0 1 6.003-6.93M9 7V1a6 6 0 0 1 6 6zm6.062 1c.498 0 .927-.366.937-.864L16 7A7 7 0 0 0 8.864.001C8.366.011 8 .441 8 .938V7a1 1 0 0 0 1 1z"})))},"document-video":function(e){return t.createElement("svg",fa({xmlns:"http://www.w3.org/2000/svg",width:16,height:14,fill:"none",viewBox:"0 0 16 14"},e),Ye||(Ye=t.createElement("path",{fill:"#5B6E7F",d:"M6.765 4.076A.5.5 0 0 0 6 4.5v5.15a.5.5 0 0 0 .776.417l4-2.649a.5.5 0 0 0-.01-.84zM2.5 0A2.5 2.5 0 0 0 0 2.5v9A2.5 2.5 0 0 0 2.5 14h11a2.5 2.5 0 0 0 2.5-2.5v-9A2.5 2.5 0 0 0 13.5 0zM1 2.5A1.5 1.5 0 0 1 2.5 1h11A1.5 1.5 0 0 1 15 2.5v9a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 11.5z"})))},"document-visio":function(e){return t.createElement("svg",ba({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",viewBox:"0 0 15 15"},e),Je||(Je=t.createElement("path",{fill:"#5B6E7F",d:"M2.5 0A1.5 1.5 0 0 0 1 1.5v2A1.5 1.5 0 0 0 2.5 5H3v3.84a1 1 0 0 0-.207.16L1 10.793a1 1 0 0 0 0 1.414L2.793 14a1 1 0 0 0 1.414 0L6 12.207q.094-.095.16-.207H10v.5a1.5 1.5 0 0 0 1.5 1.5h2a1.5 1.5 0 0 0 1.5-1.5v-2A1.5 1.5 0 0 0 13.5 9h-2a1.5 1.5 0 0 0-1.5 1.5v.5H6.16a1 1 0 0 0-.16-.207L4.207 9A1 1 0 0 0 4 8.84V5h.5A1.5 1.5 0 0 0 6 3.5v-2A1.5 1.5 0 0 0 4.5 0zM2 1.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5zm-.293 10L3.5 9.707 5.293 11.5 3.5 13.293zM11.5 10h2a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 1 .5-.5"})))},"documents-icon":function(e){return t.createElement("svg",da({xmlns:"http://www.w3.org/2000/svg",width:12,height:16,fill:"none",viewBox:"0 0 12 16"},e),Re||(Re=t.createElement("path",{fill:"currentColor",d:"M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V5.414a1.5 1.5 0 0 0-.44-1.06L7.647.439A1.5 1.5 0 0 0 6.586 0zM1 2a1 1 0 0 1 1-1h4v3.5A1.5 1.5 0 0 0 7.5 6H11v8a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1zm9.793 3H7.5a.5.5 0 0 1-.5-.5V1.207z"})))},"dot-icon":function(e){return t.createElement("svg",va({xmlns:"http://www.w3.org/2000/svg",width:4,height:4,fill:"none",viewBox:"0 0 4 4"},e),Ze||(Ze=t.createElement("circle",{cx:2,cy:2,r:2,fill:"#666"})))},Drag:go,drag:go,"drag-handle":function(e){return t.createElement("svg",ho({xmlns:"http://www.w3.org/2000/svg",width:16,height:42,fill:"none",viewBox:"0 0 16 42"},e),ya||(ya=t.createElement("path",{fill:"#242424",d:"M6 18a1 1 0 1 0 0-2 1 1 0 0 0 0 2m0 4a1 1 0 1 0 0-2 1 1 0 0 0 0 2m1 3a1 1 0 1 1-2 0 1 1 0 0 1 2 0m3-7a1 1 0 1 0 0-2 1 1 0 0 0 0 2m1 3a1 1 0 1 1-2 0 1 1 0 0 1 2 0m-1 5a1 1 0 1 0 0-2 1 1 0 0 0 0 2"})))},"dropdown-arrow-down":function(e){return t.createElement("svg",fo({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),xa||(xa=t.createElement("path",{fill:"#5B6E7F",d:"M3.2 5.74a.75.75 0 0 1 1.06-.04L8 9.227 11.74 5.7a.75.75 0 1 1 1.02 1.1l-4.25 4a.75.75 0 0 1-1.02 0l-4.25-4a.75.75 0 0 1-.04-1.06"})))},"edit-icon":function(e){return t.createElement("svg",jn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Wr||(Wr=t.createElement("path",{fill:"currentColor",d:"M21.03 2.97a3.58 3.58 0 0 1 0 5.06L9.062 20a2.25 2.25 0 0 1-.999.58l-5.116 1.395a.75.75 0 0 1-.92-.921l1.395-5.116a2.25 2.25 0 0 1 .58-.999L15.97 2.97a3.58 3.58 0 0 1 5.06 0M15 6.06 5.062 16a.75.75 0 0 0-.193.333l-1.05 3.85 3.85-1.05A.75.75 0 0 0 8 18.938L17.94 9zm2.03-2.03-.97.97L19 7.94l.97-.97a2.079 2.079 0 0 0-2.94-2.94"})))},"ellipses-horizontal-icon":function(e){return t.createElement("svg",vo({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Ca||(Ca=t.createElement("path",{fill:"#333",d:"M6 10a2 2 0 1 1-4 0 2 2 0 0 1 4 0m6 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0m4 2a2 2 0 1 0 0-4 2 2 0 0 0 0 4"})))},"ellipses-icon":function(e){return t.createElement("svg",bo({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Ea||(Ea=t.createElement("path",{fill:"#333",d:"M10 6a1.25 1.25 0 1 1 0-2.5A1.25 1.25 0 0 1 10 6m0 5.25a1.25 1.25 0 1 1 0-2.5 1.25 1.25 0 0 1 0 2.5m-1.25 4a1.25 1.25 0 1 0 2.5 0 1.25 1.25 0 0 0-2.5 0"})))},"envelope-light":function(e){return t.createElement("svg",wo({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),ka||(ka=t.createElement("path",{fill:"#E81A3B",d:"M2 3c-.553 0-1 .447-1 1v1.247l6.113 4.481a1.5 1.5 0 0 0 1.774 0L15 5.247V4c0-.553-.447-1-1-1zM1 6.488V12c0 .553.447 1 1 1h12c.553 0 1-.447 1-1V6.488l-5.522 4.046a2.5 2.5 0 0 1-2.956 0zM0 4c0-1.103.897-2 2-2h12c1.103 0 2 .897 2 2v8c0 1.103-.897 2-2 2H2c-1.103 0-2-.897-2-2z"})))},"error-icon":function(e){return t.createElement("svg",yo({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Ta||(Ta=t.createElement("path",{fill:"#98002E",d:"M10 .374a9.376 9.376 0 0 1 9.376 9.376 9.376 9.376 0 1 1-18.752 0A9.376 9.376 0 0 1 10 .374m-.004 7.735a.94.94 0 0 0-.93.829l-.006.11.003 5.157.006.11a.938.938 0 0 0 1.863-.002l.006-.11-.004-5.157-.006-.11a.94.94 0 0 0-.932-.827M10 4.594a1.173 1.173 0 1 0 0 2.346 1.173 1.173 0 0 0 0-2.346"})))},"error-icon-red":function(e){return t.createElement("svg",xo({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Ia||(Ia=t.createElement("path",{fill:"#98002E",d:"M10 0c5.523 0 10 4.477 10 10s-4.477 10-10 10S0 15.523 0 10 4.477 0 10 0m0 1.25a8.75 8.75 0 1 0 0 17.5 8.75 8.75 0 0 0 0-17.5m0 11.875A.937.937 0 1 1 10 15a.937.937 0 0 1 0-1.875M10 5c.307 0 .562.221.615.513l.01.112v5.625a.625.625 0 0 1-1.24.112l-.01-.112V5.625c0-.345.28-.625.625-.625"})))},"error-circle":function(e){return t.createElement("svg",Eo({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),za||(za=t.createElement("path",{fill:"currentColor",d:"M12 2c5.523 0 10 4.478 10 10s-4.477 10-10 10S2 17.522 2 12 6.477 2 12 2m0 1.667c-4.595 0-8.333 3.738-8.333 8.333S7.405 20.333 12 20.333s8.333-3.738 8.333-8.333S16.595 3.667 12 3.667m-.001 10.835a.999.999 0 1 1 0 1.998.999.999 0 0 1 0-1.998M11.994 7a.75.75 0 0 1 .744.648l.007.101.004 4.502a.75.75 0 0 1-1.493.103l-.007-.102-.004-4.501a.75.75 0 0 1 .75-.751Z"})))},"error-message-icon":function(e){return t.createElement("svg",Co({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Na||(Na=t.createElement("path",{fill:"#98002E",d:"M10.492 8.91A.5.5 0 0 0 9.5 9v4.502l.008.09a.5.5 0 0 0 .992-.09V9zm.307-2.16a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0M18 10a8 8 0 1 0-16 0 8 8 0 0 0 16 0M3 10a7 7 0 1 1 14 0 7 7 0 0 1-14 0"})))},"export-files":function(e){return t.createElement("svg",ko({xmlns:"http://www.w3.org/2000/svg",width:13,height:13,fill:"none",viewBox:"0 0 13 13"},e),Sa||(Sa=t.createElement("path",{fill:"#000",d:"M9 4.999A4.001 4.001 0 1 1 9 13 4.001 4.001 0 0 1 9 5M1 2.085V8.5a2.5 2.5 0 0 0 2.336 2.495L3.5 11h.915c.156.357.352.693.583 1H3a3 3 0 0 1-3-3V3.5a1.5 1.5 0 0 1 1-1.415m7.798 3.96-.076.044-.07.057-.057.07a.5.5 0 0 0 0 .568l.058.07 1.645 1.645L6.5 8.5l-.09.008a.5.5 0 0 0-.402.402L6 9l.008.09a.5.5 0 0 0 .402.402l.09.008 3.8-.001-1.647 1.647-.058.07a.5.5 0 0 0 .696.695l.069-.057 2.53-2.533.037-.05.042-.08.026-.083.01-.064v-.088l-.01-.064-.026-.083-.042-.08-.037-.05-2.53-2.533-.07-.057a.5.5 0 0 0-.492-.044M8.5 0A1.5 1.5 0 0 1 10 1.5v2.599a5 5 0 0 0-1-.1V1.5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .5.5h.499q0 .515.1 1H3.5A1.5 1.5 0 0 1 2 8.5v-7A1.5 1.5 0 0 1 3.5 0z"})))},"feedback-icon":function(e){return t.createElement("svg",To({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Ba||(Ba=t.createElement("path",{fill:"#CCCFD2",d:"M8.5 10a1.5 1.5 0 0 1 1.5 1.5v.5c0 1.971-1.86 4-5 4s-5-2.029-5-4v-.5A1.5 1.5 0 0 1 1.5 10zm0 1h-7a.5.5 0 0 0-.5.5v.5c0 1.438 1.432 3 4 3s4-1.562 4-3v-.5a.5.5 0 0 0-.5-.5M5 3.5A2.75 2.75 0 1 1 5 9a2.75 2.75 0 0 1 0-5.5M14 0a2 2 0 0 1 1.995 1.85L16 2v2a2 2 0 0 1-1.85 1.995L14 6h-1.501l-1.198 1.6c-.53.706-1.604.42-1.777-.376l-.017-.111L9.5 7 9.5 5.935l-.078-.02a2 2 0 0 1-1.397-1.6l-.02-.166L8 4V2A2 2 0 0 1 9.85.005L10 0zM5 4.5A1.75 1.75 0 1 0 5 8a1.75 1.75 0 0 0 0-3.5M14 1h-4a1 1 0 0 0-.993.883L9 2v2a1 1 0 0 0 .883.993L10 5h.5v2L12 5h2a1 1 0 0 0 .993-.883L15 4V2a1 1 0 0 0-.883-.993z"})))},"filter-down-arrow":function(e){return t.createElement("svg",Io({xmlns:"http://www.w3.org/2000/svg",width:7,height:12,fill:"none",viewBox:"0 0 7 12"},e),La||(La=t.createElement("path",{fill:"#333",d:"M3.146 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 1 0-.708-.708L4 10.293V.5a.5.5 0 0 0-1 0v9.793L.854 8.146a.5.5 0 1 0-.708.708z"})))},"filter-up-arrow":function(e){return t.createElement("svg",zo({xmlns:"http://www.w3.org/2000/svg",width:7,height:12,fill:"none",viewBox:"0 0 7 12"},e),Oa||(Oa=t.createElement("path",{fill:"#333",d:"M3.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 1 1-.708.708L4 1.707V11.5a.5.5 0 0 1-1 0V1.707L.854 3.854a.5.5 0 1 1-.708-.708z"})))},"folder-arrow-right":function(e){return t.createElement("svg",Lo({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ma||(Ma=t.createElement("path",{fill:"currentColor",d:"M17.5 11a5.5 5.5 0 1 1 0 11 5.5 5.5 0 0 1 0-11M8.207 4c.46 0 .908.141 1.284.402l.156.12L12.022 6.5h7.728a2.25 2.25 0 0 1 2.229 1.938l.016.158.005.154v3.06a6.5 6.5 0 0 0-1.499-1.077L20.5 8.75a.75.75 0 0 0-.648-.743L19.75 8h-7.729L9.647 9.979a2.25 2.25 0 0 1-1.244.512l-.196.009-4.707-.001v7.251c0 .38.282.694.648.743l.102.007h7.064c.172.534.412 1.038.709 1.501L4.25 20a2.25 2.25 0 0 1-2.245-2.096L2 17.75V6.25a2.25 2.25 0 0 1 2.096-2.245L4.25 4zm9.585 9.545-.076.044-.07.057-.057.07a.5.5 0 0 0 0 .568l.057.07L19.292 16H14l-.09.008a.5.5 0 0 0-.402.402l-.008.09.008.09a.5.5 0 0 0 .402.402L14 17h5.292l-1.646 1.646-.057.07a.5.5 0 0 0 .695.695l.07-.057 2.528-2.532.046-.063.034-.068.021-.063.015-.082L21 16.5l-.003-.053-.014-.075-.03-.083-.042-.074-.045-.056-2.512-2.513-.07-.057a.5.5 0 0 0-.492-.044M8.207 5.5H4.25a.75.75 0 0 0-.743.648L3.5 6.25v2.749L8.207 9a.75.75 0 0 0 .395-.113l.085-.06 1.891-1.578-1.89-1.575a.75.75 0 0 0-.377-.167z"})))},"folder-icon":function(e){return t.createElement("svg",No({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Aa||(Aa=t.createElement("path",{fill:"currentColor",d:"M8.207 4c.46 0 .908.141 1.284.402l.156.12L12.022 6.5h7.728a2.25 2.25 0 0 1 2.229 1.938l.016.158.005.154v9a2.25 2.25 0 0 1-2.096 2.245L19.75 20H4.25a2.25 2.25 0 0 1-2.245-2.096L2 17.75V6.25a2.25 2.25 0 0 1 2.096-2.245L4.25 4zm1.44 5.979a2.25 2.25 0 0 1-1.244.512l-.196.009-4.707-.001v7.251c0 .38.282.694.648.743l.102.007h15.5a.75.75 0 0 0 .743-.648l.007-.102v-9a.75.75 0 0 0-.648-.743L19.75 8h-7.729zM8.207 5.5H4.25a.75.75 0 0 0-.743.648L3.5 6.25v2.749L8.207 9a.75.75 0 0 0 .395-.113l.085-.06 1.891-1.578-1.89-1.575a.75.75 0 0 0-.377-.167z"})))},"folder-icon-filled":function(e){return t.createElement("svg",Bo({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),ja||(ja=t.createElement("path",{fill:"#5B6E7F",d:"M1 4.5A2.5 2.5 0 0 1 3.5 2h2.086a1.5 1.5 0 0 1 1.06.44L8 3.792l-2.06 2.06A.5.5 0 0 1 5.585 6H1zM1 7v4.5A2.5 2.5 0 0 0 3.5 14h9a2.5 2.5 0 0 0 2.5-2.5v-5A2.5 2.5 0 0 0 12.5 4H9.207l-2.56 2.56A1.5 1.5 0 0 1 5.585 7z"})))},"folder-open-icon":function(e){return t.createElement("svg",So({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Da||(Da=t.createElement("path",{fill:"currentColor",d:"M20 9.502V8.75a2.25 2.25 0 0 0-2.25-2.25h-5.725l-2.38-1.98A2.25 2.25 0 0 0 8.204 4H4.25A2.25 2.25 0 0 0 2 6.25l-.004 11.5A2.25 2.25 0 0 0 4.246 20H18.47a1.75 1.75 0 0 0 1.698-1.325l1.75-6.998a1.75 1.75 0 0 0-1.698-2.175zM4.25 5.5h3.956a.75.75 0 0 1 .48.173l2.588 2.154a.75.75 0 0 0 .48.173h5.996a.75.75 0 0 1 .75.75v.752H6.424a2.25 2.25 0 0 0-2.183 1.704l-.744 2.978L3.5 6.25a.75.75 0 0 1 .75-.75m1.447 6.07a.75.75 0 0 1 .727-.568H20.22a.25.25 0 0 1 .242.31l-1.75 6.999a.25.25 0 0 1-.242.189H4.285a.25.25 0 0 1-.243-.31z"})))},"folder-zip-icon":function(e){return t.createElement("svg",Pn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),qr||(qr=t.createElement("path",{fill:"currentColor",d:"M9.49 4.402A2.25 2.25 0 0 0 8.208 4H4.25l-.154.005A2.25 2.25 0 0 0 2 6.25v11.5l.005.154A2.25 2.25 0 0 0 4.25 20h15.5l.154-.005A2.25 2.25 0 0 0 22 17.75v-9l-.005-.154-.017-.158A2.25 2.25 0 0 0 19.75 6.5h-7.728L9.647 4.521zM13.498 8v2.245c0 .414.335.75.75.75h.75v1.003h-.25a.75.75 0 0 0 0 1.5h.25v1.5h-.25a.75.75 0 0 0 0 1.5h.25V18.5H4.25l-.102-.007a.75.75 0 0 1-.648-.743v-7.251l4.707.001.196-.009a2.25 2.25 0 0 0 1.244-.512L12.021 8zm3 10h.25a.75.75 0 0 0 0-1.5h-.25V15h.25a.75.75 0 0 0 0-1.5h-.25v-2.505h.75a.75.75 0 0 0 .75-.75V8h1.753l.102.007a.75.75 0 0 1 .648.743v9l-.007.102a.75.75 0 0 1-.743.648h-3.253zm0-10v1.495h-1.5V8zM4.25 5.5h3.957l.104.007a.75.75 0 0 1 .376.167l1.891 1.575-1.89 1.577-.086.061A.75.75 0 0 1 8.207 9L3.5 8.999V6.25l.007-.102A.75.75 0 0 1 4.25 5.5"})))},"gear-icon":function(e){return t.createElement("svg",Oo({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Pa||(Pa=t.createElement("path",{fill:"#242424",d:"M1.91 7.383a8.5 8.5 0 0 1 1.781-3.08.5.5 0 0 1 .54-.135l1.918.686a1 1 0 0 0 1.32-.762l.365-2.006a.5.5 0 0 1 .388-.4 8.5 8.5 0 0 1 3.555 0 .5.5 0 0 1 .388.4l.366 2.006a1 1 0 0 0 1.32.762l1.919-.686a.5.5 0 0 1 .539.136 8.5 8.5 0 0 1 1.78 3.079.5.5 0 0 1-.152.535l-1.555 1.32a1 1 0 0 0 0 1.524l1.555 1.32a.5.5 0 0 1 .152.535 8.5 8.5 0 0 1-1.78 3.08.5.5 0 0 1-.54.135l-1.918-.686a1 1 0 0 0-1.32.762l-.367 2.007a.5.5 0 0 1-.387.399 8.5 8.5 0 0 1-3.555 0 .5.5 0 0 1-.388-.4l-.365-2.006a1 1 0 0 0-1.32-.762l-1.919.686a.5.5 0 0 1-.539-.136 8.5 8.5 0 0 1-1.78-3.079.5.5 0 0 1 .152-.535l1.555-1.32a1 1 0 0 0 0-1.524l-1.555-1.32a.5.5 0 0 1-.152-.535m1.061-.006 1.294 1.098a2 2 0 0 1 0 3.05l-1.294 1.098c.292.782.714 1.51 1.245 2.152l1.596-.57q.154-.055.314-.085a2 2 0 0 1 2.327 1.609l.304 1.669a7.6 7.6 0 0 0 2.485 0l.305-1.67q.03-.16.084-.313a2 2 0 0 1 2.557-1.21l1.596.57a7.5 7.5 0 0 0 1.245-2.152l-1.294-1.098a1.998 1.998 0 0 1 0-3.05l1.294-1.098a7.5 7.5 0 0 0-1.245-2.152l-1.596.57a2 2 0 0 1-2.64-1.524l-.306-1.669a7.6 7.6 0 0 0-2.485 0l-.304 1.669a2 2 0 0 1-2.64 1.525l-1.597-.571a7.5 7.5 0 0 0-1.245 2.152M7.5 10a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0m1 0a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0"})))},"hamburger-menu":function(e){return t.createElement("svg",Ao({xmlns:"http://www.w3.org/2000/svg",width:20,height:15,fill:"none",viewBox:"0 0 20 15"},e),Ha||(Ha=t.createElement("path",{fill:"#242424",d:"M.752 13h18.5a.75.75 0 0 1 .102 1.493l-.102.007H.752a.75.75 0 0 1-.101-1.493zh18.5zm0-6.497h18.5a.75.75 0 0 1 .102 1.493l-.102.007H.752A.75.75 0 0 1 .651 6.51zh18.5zm0-6.5h18.5a.75.75 0 0 1 .101 1.493l-.102.007H.752A.75.75 0 0 1 .65.01zh18.5z"})))},"help-icon":function(e){return t.createElement("svg",Do({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Ra||(Ra=t.createElement("path",{fill:"#0062B8",d:"M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0m0 1a7 7 0 1 0 0 14A7 7 0 0 0 8 1m0 10.5A.75.75 0 1 1 8 13a.75.75 0 0 1 0-1.5m0-8a2.5 2.5 0 0 1 1.651 4.377l-.154.125-.219.164-.087.071a2 2 0 0 0-.156.149c-.339.36-.535.856-.535 1.614a.5.5 0 0 1-1 0c0-1.012.293-1.753.805-2.298.111-.119.227-.222.356-.323l.247-.185.118-.1A1.5 1.5 0 1 0 6.5 6a.5.5 0 0 1-1 .001A2.5 2.5 0 0 1 8 3.5"})))},"high-priority":function(e){return t.createElement("svg",Po({xmlns:"http://www.w3.org/2000/svg",width:12,height:12,fill:"none",viewBox:"0 0 12 12"},e),Wa||(Wa=t.createElement("path",{fill:"#E81A3B",d:"M0 4.174a.5.5 0 0 1 .227-.419l5.5-3.578a.5.5 0 0 1 .546 0l5.5 3.578a.5.5 0 0 1 .227.42v6.903a.5.5 0 0 1-.773.42L6.273 8.273a.5.5 0 0 0-.546 0L.773 11.497A.5.5 0 0 1 0 11.078z"})))},"history-icon":function(e){return t.createElement("svg",Mo({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Fa||(Fa=t.createElement("path",{fill:"currentColor",d:"M12 3a9 9 0 1 1-8.963 8.182.75.75 0 1 1 1.494.135 7.46 7.46 0 0 0 2.166 5.986A7.46 7.46 0 0 0 12 19.5 7.5 7.5 0 1 0 5.997 7.502h2.756a.75.75 0 0 1 .102 1.493l-.102.007H4.25a.75.75 0 0 1-.743-.648L3.5 8.252v-4.5a.75.75 0 0 1 1.493-.102L5 3.752l-.001 2.591A8.99 8.99 0 0 1 12 3m-.75 4a.75.75 0 0 1 .743.648L12 7.75V12h2.25a.75.75 0 0 1 .102 1.493l-.102.007h-3a.75.75 0 0 1-.743-.648l-.007-.102v-5a.75.75 0 0 1 .75-.75"})))},"home-icon":function(e){return t.createElement("svg",jo({xmlns:"http://www.w3.org/2000/svg",width:14,height:15,fill:"none",viewBox:"0 0 14 15"},e),Va||(Va=t.createElement("path",{fill:"#CCCFD2",d:"M5.998.388a1.5 1.5 0 0 1 2.004 0l5.5 4.942A1.5 1.5 0 0 1 14 6.445V13.5a1.5 1.5 0 0 1-1.5 1.5H10a1.5 1.5 0 0 1-1.5-1.5V10a.5.5 0 0 0-.5-.5H6a.5.5 0 0 0-.5.5v3.5A1.5 1.5 0 0 1 4 15H1.5A1.5 1.5 0 0 1 0 13.5V6.445c0-.425.18-.83.498-1.115zm1.336.744a.5.5 0 0 0-.668 0l-5.5 4.942A.5.5 0 0 0 1 6.445V13.5a.5.5 0 0 0 .5.5H4a.5.5 0 0 0 .5-.5V10A1.5 1.5 0 0 1 6 8.5h2A1.5 1.5 0 0 1 9.5 10v3.5a.5.5 0 0 0 .5.5h2.5a.5.5 0 0 0 .5-.5V6.445a.5.5 0 0 0-.166-.371z"})))},"image-circle":function(e){return t.createElement("svg",Ho({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ua||(Ua=t.createElement("path",{fill:"currentColor",d:"M12 3.5a8.5 8.5 0 0 0-6.51 13.965l4.934-4.843a2.25 2.25 0 0 1 3.152 0l4.935 4.843A8.5 8.5 0 0 0 12 3.5m5.449 15.024-4.924-4.831a.75.75 0 0 0-1.05 0L6.55 18.524A8.47 8.47 0 0 0 12 20.5a8.47 8.47 0 0 0 5.449-1.976M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12m13.25-2.5a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5m0 1.5a2.25 2.25 0 1 0 0-4.5 2.25 2.25 0 0 0 0 4.5"})))},"info-icon":function(e){return t.createElement("svg",Ro({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),qa||(qa=t.createElement("path",{fill:"#0062B8",d:"M10 .374a9.376 9.376 0 0 1 9.376 9.376A9.376 9.376 0 0 1 10 19.127 9.376 9.376 0 1 1 10 .374m-.004 7.735a.94.94 0 0 0-.93.829l-.006.11.003 5.157.006.11a.938.938 0 0 0 1.863-.002l.006-.11-.004-5.157-.006-.11a.94.94 0 0 0-.932-.827M10 4.594a1.173 1.173 0 1 0 0 2.346 1.173 1.173 0 0 0 0-2.346"})))},"info-icon-black-and-white":function(e){return t.createElement("svg",Fo({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),Ga||(Ga=t.createElement("path",{fill:"#333",d:"M7.499 6.5a.5.5 0 1 0-1 0v3a.5.5 0 0 0 1 0zm.25-2a.749.749 0 1 1-1.499 0 .749.749 0 0 1 1.498 0M7 0a7 7 0 1 0 0 14A7 7 0 0 0 7 0M1 7a6 6 0 1 1 12 0A6 6 0 0 1 1 7"})))},"inprogress-icon":function(e){return t.createElement("svg",Fn({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Xr||(Xr=t.createElement("rect",{width:16,height:16,fill:"#fff",rx:8})),Qr||(Qr=t.createElement("path",{fill:"#218F8B",d:"M8 .001V0a7.96 7.96 0 0 0-4.66 1.497 8 8 0 0 0-1.854 1.858A7.96 7.96 0 0 0 0 8a8 8 0 0 0 .109 1.322 7.96 7.96 0 0 0 1.388 3.339 8 8 0 0 0 1.856 1.851A7.96 7.96 0 0 0 8 16v-.001c.44 0 .878-.035 1.302-.105a.75.75 0 0 0-.242-1.48 6.503 6.503 0 0 1-7.521-7.126 6.503 6.503 0 0 1 7.52-5.702.75.75 0 1 0 .243-1.48A8 8 0 0 0 8 0m4.66 1.496a.75.75 0 0 0-.874 1.219 6.5 6.5 0 0 1 1.498 1.498.75.75 0 0 0 1.219-.875 8 8 0 0 0-1.843-1.842m3.234 5.2a.75.75 0 0 0-1.48.243c.113.689.113 1.431 0 2.12a.75.75 0 0 0 1.48.243c.14-.849.14-1.757 0-2.606m-1.391 5.964a.75.75 0 0 0-1.219-.875 6.5 6.5 0 0 1-1.498 1.498.75.75 0 1 0 .874 1.219 8 8 0 0 0 1.843-1.842"})))},"left-arrow":function(e){return t.createElement("svg",Wo({xmlns:"http://www.w3.org/2000/svg",width:16,height:14,fill:"none",viewBox:"0 0 16 14"},e),Ya||(Ya=t.createElement("path",{fill:"currentColor",d:"M7.161 13.868a.5.5 0 1 0 .675-.739L1.668 7.5h13.83a.5.5 0 1 0 0-1H1.672L7.836.87A.5.5 0 1 0 7.16.132L.245 6.446a.75.75 0 0 0-.24.632.75.75 0 0 0 .24.476z"})))},"link-icon":function(e){return t.createElement("svg",Uo({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ja||(Ja=t.createElement("path",{fill:"currentColor",d:"M9.25 7a.75.75 0 0 1 .11 1.492l-.11.008H7a3.5 3.5 0 0 0-.206 6.994L7 15.5h2.25a.75.75 0 0 1 .11 1.492L9.25 17H7a5 5 0 0 1-.25-9.994L7 7zM17 7a5 5 0 0 1 .25 9.994L17 17h-2.25a.75.75 0 0 1-.11-1.492l.11-.008H17a3.5 3.5 0 0 0 .206-6.994L17 8.5h-2.25a.75.75 0 0 1-.11-1.492L14.75 7zM7 11.25h10a.75.75 0 0 1 .102 1.493L17 12.75H7a.75.75 0 0 1-.102-1.493zh10z"})))},"lock-icon":function(e){return t.createElement("svg",qo({xmlns:"http://www.w3.org/2000/svg",width:8,height:10,fill:"none",viewBox:"0 0 8 10"},e),Za||(Za=t.createElement("path",{fill:"#666",d:"M6 3V2a2 2 0 1 0-4 0v1h-.5A1.5 1.5 0 0 0 0 4.5v4A1.5 1.5 0 0 0 1.5 10h5A1.5 1.5 0 0 0 8 8.5v-4A1.5 1.5 0 0 0 6.5 3zM4 1a1 1 0 0 1 1 1v1H3V2a1 1 0 0 1 1-1"})))},"low-priority":function(e){return t.createElement("svg",Ko({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Xa||(Xa=t.createElement("path",{fill:"#E81A3B",d:"M4.22 15.53a.75.75 0 0 0 1.06 0L12 8.81l6.72 6.72a.75.75 0 1 0 1.06-1.06l-7.25-7.25a.75.75 0 0 0-1.06 0l-7.25 7.25a.75.75 0 0 0 0 1.06"})))},"mail-black":function(e){return t.createElement("svg",Go({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Qa||(Qa=t.createElement("path",{fill:"currentColor",d:"M5.25 4h13.5a3.25 3.25 0 0 1 3.245 3.066L22 7.25v9.5a3.25 3.25 0 0 1-3.066 3.245L18.75 20H5.25a3.25 3.25 0 0 1-3.245-3.066L2 16.75v-9.5a3.25 3.25 0 0 1 3.066-3.245zh13.5zM20.5 9.373l-8.15 4.29a.75.75 0 0 1-.603.043l-.096-.042L3.5 9.374v7.376a1.75 1.75 0 0 0 1.606 1.744l.144.006h13.5a1.75 1.75 0 0 0 1.744-1.607l.006-.143zM18.75 5.5H5.25a1.75 1.75 0 0 0-1.744 1.606L3.5 7.25v.429l8.5 4.473 8.5-4.474V7.25a1.75 1.75 0 0 0-1.607-1.744z"})))},"mail-dismiss":function(e){return t.createElement("svg",Yo({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),$a||($a=t.createElement("path",{fill:"currentColor",d:"M23 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0m-7.146-2.354a.5.5 0 0 0-.708.708L16.793 6.5l-1.647 1.646a.5.5 0 0 0 .708.708L17.5 7.207l1.646 1.647a.5.5 0 0 0 .708-.708L18.207 6.5l1.647-1.646a.5.5 0 0 0-.708-.708L17.5 5.793zM20.5 16.75v-4.482A6.5 6.5 0 0 0 22 11.19v5.56a3.25 3.25 0 0 1-3.066 3.245L18.75 20H5.25a3.25 3.25 0 0 1-3.245-3.066L2 16.75v-9.5a3.25 3.25 0 0 1 3.066-3.245L5.25 4h6.248a6.5 6.5 0 0 0-.422 1.5H5.25a1.75 1.75 0 0 0-1.744 1.606L3.5 7.25v.429l8.5 4.473 1.305-.687c.439.371.927.685 1.454.93l-2.41 1.269a.75.75 0 0 1-.602.042l-.096-.042L3.5 9.374v7.376a1.75 1.75 0 0 0 1.606 1.744l.144.006h13.5a1.75 1.75 0 0 0 1.744-1.607z"})))},"mail-icon":function(e){return t.createElement("svg",Jo({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none",viewBox:"0 0 48 48"},e),er||(er=t.createElement("path",{fill:"#242424",d:"M10.25 8A6.25 6.25 0 0 0 4 14.25v19.5A6.25 6.25 0 0 0 10.25 40h27.5A6.25 6.25 0 0 0 44 33.75v-19.5A6.25 6.25 0 0 0 37.75 8zM6.5 14.25a3.75 3.75 0 0 1 3.75-3.75h27.5a3.75 3.75 0 0 1 3.75 3.75v.89l-17.498 9.21L6.5 15.063zm0 3.644 16.914 8.974a1.25 1.25 0 0 0 1.168.002L41.5 17.965V33.75a3.75 3.75 0 0 1-3.75 3.75h-27.5a3.75 3.75 0 0 1-3.75-3.75z"})))},"move-document-file-icon":function(e){return t.createElement("svg",Zo({xmlns:"http://www.w3.org/2000/svg",width:17,height:16,fill:"none",viewBox:"0 0 17 16"},e),tr||(tr=t.createElement("path",{fill:"#242424",d:"M2.5 0A2.5 2.5 0 0 0 0 2.5v9A2.5 2.5 0 0 0 2.5 14h5.1a5.5 5.5 0 0 1-.393-1H2.5A1.5 1.5 0 0 1 1 11.5V5h4.086a1.5 1.5 0 0 0 1.06-.44L7.707 3H13.5A1.5 1.5 0 0 1 15 4.5v2.1q.538.276 1 .657V4.5A2.5 2.5 0 0 0 13.5 2H7.707L6.22.513A1.75 1.75 0 0 0 4.982 0zM1 2.5A1.5 1.5 0 0 1 2.5 1h2.482a.75.75 0 0 1 .53.22l1.28 1.28L5.44 3.854A.5.5 0 0 1 5.086 4H1zM12.5 7a4.5 4.5 0 1 1 0 9 4.5 4.5 0 0 1 0-9m2.353 4.854.003-.003a.5.5 0 0 0 .144-.348v-.006a.5.5 0 0 0-.146-.35l-2-2a.5.5 0 0 0-.708.707L13.293 11H10.5a.5.5 0 0 0 0 1h2.793l-1.147 1.146a.5.5 0 0 0 .708.708z"})))},"move-document-to-recycle-bin-icon":function(e){return t.createElement("svg",Xo({xmlns:"http://www.w3.org/2000/svg",width:16,height:17,fill:"none",viewBox:"0 0 16 17"},e),ar||(ar=t.createElement("path",{fill:"#98002E",d:"M6.5 3h3a1.5 1.5 0 1 0-3 0m-1 0a2.5 2.5 0 0 1 5 0h5a.5.5 0 0 1 0 1h-1.054l-1.194 10.344A3 3 0 0 1 10.272 17H5.728a3 3 0 0 1-2.98-2.656L1.554 4H.5a.5.5 0 0 1 0-1zM3.741 14.23A2 2 0 0 0 5.728 16h4.544a2 2 0 0 0 1.987-1.77L13.439 4H2.561zM6.5 6.5A.5.5 0 0 1 7 7v6a.5.5 0 0 1-1 0V7a.5.5 0 0 1 .5-.5M10 7a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"})))},"open-icon":function(e){return t.createElement("svg",Qo({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),rr||(rr=t.createElement("path",{fill:"currentColor",d:"M6.25 4.5A1.75 1.75 0 0 0 4.5 6.25v11.5c0 .966.783 1.75 1.75 1.75h11.5a1.75 1.75 0 0 0 1.75-1.75v-4a.75.75 0 0 1 1.5 0v4A3.25 3.25 0 0 1 17.75 21H6.25A3.25 3.25 0 0 1 3 17.75V6.25A3.25 3.25 0 0 1 6.25 3h4a.75.75 0 0 1 0 1.5zM13 3.75a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0V5.56l-5.22 5.22a.75.75 0 0 1-1.06-1.06l5.22-5.22h-4.69a.75.75 0 0 1-.75-.75"})))},"pause-icon":function(e){return t.createElement("svg",$o({xmlns:"http://www.w3.org/2000/svg",width:14,height:16,fill:"none",viewBox:"0 0 14 16"},e),or||(or=t.createElement("path",{fill:"#fff",d:"M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zM1 2a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1zm9-2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zM9 2a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1z"})))},"pen-icon":function(e){return t.createElement("svg",en({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),nr||(nr=t.createElement("path",{fill:"currentColor",d:"M10.733 2.56a1.914 1.914 0 0 1 2.707 2.708L12.707 6l.263.263a1.75 1.75 0 0 1 0 2.474l-1.116 1.116a.5.5 0 1 1-.708-.707l1.117-1.116a.75.75 0 0 0 0-1.06L12 6.707l-5.955 5.955a1.65 1.65 0 0 1-.644.398l-2.743.914a.5.5 0 0 1-.632-.632l.914-2.743c.08-.243.217-.463.398-.644z"})))},"people-icon":function(e){return t.createElement("svg",tn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),lr||(lr=t.createElement("path",{fill:"currentColor",d:"M4 13.999 13 14a2 2 0 0 1 1.995 1.85L15 16v1.5C14.999 21 11.284 22 8.5 22c-2.722 0-6.335-.956-6.495-4.27L2 17.5v-1.501c0-1.054.816-1.918 1.85-1.995zM15.22 14H20a2 2 0 0 1 1.994 1.85L22 16v1c-.001 3.062-2.858 4-5 4a7.2 7.2 0 0 1-2.14-.322c.336-.386.607-.827.802-1.327A6.2 6.2 0 0 0 17 19.5l.267-.006c.985-.043 3.086-.363 3.226-2.289L20.5 17v-1a.5.5 0 0 0-.41-.492L20 15.5h-4.051a2.96 2.96 0 0 0-.595-1.34zH20zM4 15.499l-.1.01a.5.5 0 0 0-.254.136.5.5 0 0 0-.136.253l-.01.101V17.5c0 1.009.45 1.722 1.417 2.242.826.445 2.003.714 3.266.753l.317.005.317-.005c1.263-.039 2.439-.308 3.266-.753.906-.488 1.359-1.145 1.412-2.057l.005-.186V16a.5.5 0 0 0-.41-.492L13 15.5zM8.5 3a4.5 4.5 0 1 1 0 9 4.5 4.5 0 0 1 0-9m9 2a3.5 3.5 0 1 1 0 7 3.5 3.5 0 0 1 0-7m-9-.5c-1.654 0-3 1.346-3 3s1.346 3 3 3 3-1.346 3-3-1.346-3-3-3m9 2c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2"})))},"people-search":function(e){return t.createElement("svg",nn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),sr||(sr=t.createElement("path",{fill:"#333",d:"m11.91 13.998 7.843.002a2.25 2.25 0 0 1 2.25 2.25v.905A3.75 3.75 0 0 1 20.696 20C19.13 21.344 16.89 22 14 22h-.179c.234-.47.242-1.025.026-1.502l.153.003c2.56 0 4.458-.557 5.719-1.64a2.25 2.25 0 0 0 .784-1.706v-.905a.75.75 0 0 0-.75-.75h-7.776a5.6 5.6 0 0 0-.068-1.502ZM6.5 10.5a4.5 4.5 0 0 1 3.46 7.376l2.823 2.814a.75.75 0 0 1-.975 1.135l-.085-.073-2.903-2.896A4.5 4.5 0 1 1 6.5 10.5m0 1.5a3 3 0 1 0 0 6 3 3 0 0 0 0-6M14 2.004a5 5 0 1 1 0 10 5 5 0 0 1 0-10m0 1.5a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7"})))},"person-icon":function(e){return t.createElement("svg",an({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),ir||(ir=t.createElement("path",{fill:"currentColor",d:"M17.754 14a2.25 2.25 0 0 1 2.25 2.249v.575c0 .894-.32 1.76-.902 2.438-1.57 1.834-3.957 2.739-7.102 2.739s-5.532-.905-7.098-2.74a3.75 3.75 0 0 1-.898-2.435v-.577a2.25 2.25 0 0 1 2.249-2.25h11.501Zm0 1.5H6.253a.75.75 0 0 0-.75.749v.577c0 .536.192 1.054.54 1.461 1.253 1.468 3.219 2.214 5.957 2.214s4.706-.746 5.962-2.214a2.25 2.25 0 0 0 .541-1.463v-.575a.75.75 0 0 0-.749-.75ZM12 2.004a5 5 0 1 1 0 10 5 5 0 0 1 0-10m0 1.5a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7"})))},"person-filled":function(e){return t.createElement("svg",rn({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),cr||(cr=t.createElement("path",{fill:"#5B6E7F",d:"M11.5 8A1.5 1.5 0 0 1 13 9.5v.5c0 1.971-1.86 4-5 4s-5-2.029-5-4v-.5A1.5 1.5 0 0 1 4.5 8zM8 1.5A2.75 2.75 0 1 1 8 7a2.75 2.75 0 0 1 0-5.5"})))},"person-warning-icon":function(e){return t.createElement("svg",on({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),dr||(dr=t.createElement("path",{fill:"#fff",d:"M9.267 8 6.29 13.964C3.58 13.684 2 11.818 2 10v-.5A1.5 1.5 0 0 1 3.5 8zM7 1.5A2.75 2.75 0 1 1 7 7a2.75 2.75 0 0 1 0-5.5m3.603 6.054-3.496 6.998A1 1 0 0 0 8.002 16h6.996a1 1 0 0 0 .895-1.448l-3.5-6.999a1 1 0 0 0-1.79 0m1.395 1.941v3.002a.5.5 0 1 1-1 0V9.495a.5.5 0 1 1 1 0m-.5 5.504a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1"})))},"phone-light":function(e){return t.createElement("svg",ln({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),_r||(_r=t.createElement("g",{clipPath:"url(#a)"},t.createElement("path",{fill:"#E81A3B",d:"M11.744 8.6a1.25 1.25 0 0 0-1.46.356l-1.037 1.27a9.6 9.6 0 0 1-3.472-3.473L7.041 5.72c.43-.353.578-.947.356-1.46l-1.5-3.5a1.25 1.25 0 0 0-1.41-.73l-3.5.75A1.25 1.25 0 0 0 0 2c0 7.397 5.734 13.453 13 13.966q.211.015.428.025h.003q.283.01.572.012c.59 0 1.1-.412 1.222-.987l.75-3.5a1.25 1.25 0 0 0-.731-1.41l-3.5-1.5zM13.99 15C6.816 14.994 1 9.178 1 2c0-.119.081-.219.197-.244l3.5-.75a.25.25 0 0 1 .281.147l1.5 3.5a.246.246 0 0 1-.072.29L5.138 5.982a1 1 0 0 0-.232 1.275 10.54 10.54 0 0 0 3.835 3.835 1 1 0 0 0 1.275-.231l1.037-1.27a.25.25 0 0 1 .29-.071l3.5 1.5a.25.25 0 0 1 .148.281l-.75 3.5a.25.25 0 0 1-.244.197h-.01z"}))),mr||(mr=t.createElement("defs",null,t.createElement("clipPath",{id:"a"},t.createElement("path",{fill:"#fff",d:"M0 0h16v16H0z"})))))},"play-icon":function(e){return t.createElement("svg",cn({xmlns:"http://www.w3.org/2000/svg",width:13,height:14,fill:"none",viewBox:"0 0 13 14"},e),ur||(ur=t.createElement("path",{fill:"#fff",d:"M12.22 5.687a1.498 1.498 0 0 1 0 2.626l-9.997 5.499A1.5 1.5 0 0 1 0 12.499V1.501A1.5 1.5 0 0 1 2.223.188zm-.482 1.75a.5.5 0 0 0 0-.875L1.741 1.063A.5.5 0 0 0 1 1.501v10.998a.5.5 0 0 0 .741.438z"})))},"prohibited-icon":function(e){return t.createElement("svg",_n({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),hr||(hr=t.createElement("path",{fill:"currentColor",d:"M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2m6.517 4.543L6.543 18.517A8.5 8.5 0 0 0 18.517 6.543M12 3.5a8.5 8.5 0 0 0-6.517 13.957L17.457 5.483A8.47 8.47 0 0 0 12 3.5"})))},"projects-icon":function(e){return t.createElement("svg",dn({xmlns:"http://www.w3.org/2000/svg",width:16,height:14,fill:"none",viewBox:"0 0 16 14"},e),pr||(pr=t.createElement("path",{fill:"#CCCFD2",d:"M3.5 3a.5.5 0 1 0 0 1h4a.5.5 0 0 0 0-1zM3 6a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 3 6m.5 2a.5.5 0 1 0 0 1h4a.5.5 0 0 0 0-1zM14 2.5A2.5 2.5 0 0 0 11.5 0h-9A2.5 2.5 0 0 0 0 2.5v7A2.5 2.5 0 0 0 2.5 12h9A2.5 2.5 0 0 0 14 9.5zm-13 7v-7A1.5 1.5 0 0 1 2.5 1h9A1.5 1.5 0 0 1 13 2.5v7a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 1 9.5M4.5 14a2.5 2.5 0 0 1-2-1h9A3.5 3.5 0 0 0 15 9.5v-7c.607.456 1 1.182 1 2v5a4.5 4.5 0 0 1-4.5 4.5z"})))},"read-only":function(e){return t.createElement("svg",mn({xmlns:"http://www.w3.org/2000/svg",width:12,height:8,fill:"none",viewBox:"0 0 12 8"},e),fr||(fr=t.createElement("path",{fill:"#666",d:"M.984 4.625v.003a.5.5 0 0 1-.612.355C-.06 4.87.017 4.372.017 4.372l.017-.062s.026-.084.047-.145a6.7 6.7 0 0 1 1.117-1.982C2.097 1.089 3.606 0 6 0S9.904 1.089 10.8 2.183a6.7 6.7 0 0 1 1.117 1.982 4 4 0 0 1 .06.187l.003.013.002.004v.002a.5.5 0 0 1-.966.258l-.001-.004-.008-.025-.035-.109a5.703 5.703 0 0 0-.945-1.674C9.285 1.911 8.044 1 6 1s-3.286.912-4.028 1.817a5.7 5.7 0 0 0-.945 1.674L.992 4.6zM3.5 5.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0"})))},"recycle-bin":function(e){return t.createElement("svg",pn({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),vr||(vr=t.createElement("path",{fill:"#5B6E7F",d:"M6 2h2a1 1 0 0 0-2 0M5 2a2 2 0 1 1 4 0h4a.5.5 0 0 1 0 1h-.564l-1.205 8.838A2.5 2.5 0 0 1 8.754 14H5.246a2.5 2.5 0 0 1-2.477-2.162L1.564 3H1a.5.5 0 0 1 0-1zm1 3.5a.5.5 0 0 0-1 0v5a.5.5 0 0 0 1 0zM8.5 5a.5.5 0 0 1 .5.5v5a.5.5 0 0 1-1 0v-5a.5.5 0 0 1 .5-.5m-4.74 6.703A1.5 1.5 0 0 0 5.246 13h3.508a1.5 1.5 0 0 0 1.487-1.297L11.427 3H2.573z"})))},"rename-icon":function(e){return t.createElement("svg",gn({xmlns:"http://www.w3.org/2000/svg",width:16,height:17,fill:"none",viewBox:"0 0 16 17"},e),wr||(wr=t.createElement("path",{fill:"#242424",d:"M2.098 11H2a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2a2 2 0 0 0-2-2H8.696l-.57.573a2.5 2.5 0 0 1-.568.426H14a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h.08a1.5 1.5 0 0 1-.067-.587l.004-.042.028-.165zm8.076-9.38a2.263 2.263 0 0 1 3.07-.125l.13.12.126.136a2.276 2.276 0 0 1 0 2.952l-.12.13-5.963 5.99a1.5 1.5 0 0 1-.547.35l-.157.047-3.086.76a.5.5 0 0 1-.618-.526l.015-.084.792-3.07a1.5 1.5 0 0 1 .283-.566l.106-.118zm2.494.703a1.263 1.263 0 0 0-1.683-.089l-.103.093L4.914 8.32a.5.5 0 0 0-.1.145l-.03.083-.583 2.26 2.273-.56a.5.5 0 0 0 .113-.043l.052-.03.07-.06 5.962-5.988a1.277 1.277 0 0 0-.003-1.805"})))},"restore-icon":function(e){return t.createElement("div",{style:{width:"16px, height: 16px"}},t.createElement("svg",un({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),br||(br=t.createElement("path",{fill:"#242424",d:"M7 1a6 6 0 1 1-5.982 5.538.5.5 0 1 0-.998-.076Q0 6.73 0 7a7 7 0 1 0 2-4.899V.5a.5.5 0 0 0-1 0v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 0-1H2.528C3.627 1.772 5.223 1 7 1m0 2.5a.5.5 0 0 0-1 0v4a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 0-1H7z"}))))},"right-arrow":function(e){return t.createElement("svg",hn({xmlns:"http://www.w3.org/2000/svg",width:16,height:14,fill:"none",viewBox:"0 0 16 14"},e),yr||(yr=t.createElement("path",{fill:"#fff",d:"M8.838.132a.5.5 0 1 0-.674.739l6.168 5.63H.502a.5.5 0 1 0 0 1h13.827L8.164 13.13a.5.5 0 1 0 .674.738l6.917-6.314a.75.75 0 0 0 .24-.632.75.75 0 0 0-.24-.476z"})))},"right-chevron-breadcrumb":function(e){return t.createElement("svg",fn({xmlns:"http://www.w3.org/2000/svg",width:6,height:10,fill:"none",viewBox:"0 0 6 10"},e),xr||(xr=t.createElement("path",{fill:"#666",d:"M.74.2A.75.75 0 0 0 .7 1.26L4.227 5 .7 8.74a.75.75 0 1 0 1.1 1.02l4-4.25a.75.75 0 0 0 0-1.02L1.8.24A.75.75 0 0 0 .74.2"})))},"search-icon":function(e){return t.createElement("svg",bn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Er||(Er=t.createElement("path",{fill:"currentColor",d:"M10 2.75a7.25 7.25 0 0 1 5.63 11.819l4.9 4.9a.75.75 0 0 1-.976 1.134l-.084-.073-4.901-4.9A7.25 7.25 0 1 1 10 2.75m0 1.5a5.75 5.75 0 1 0 0 11.5 5.75 5.75 0 0 0 0-11.5"})))},"send-icon":function(e){return t.createElement("svg",Mn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ur||(Ur=t.createElement("path",{fill:"currentColor",d:"M5.694 12 2.299 3.272c-.236-.607.356-1.188.942-.982l.093.04 18 9a.75.75 0 0 1 .097 1.283l-.097.058-18 9a.75.75 0 0 1-1.065-.847l.03-.096zL2.299 3.272zM4.402 4.54l2.61 6.71h6.627a.75.75 0 0 1 .743.648l.007.102a.75.75 0 0 1-.649.743l-.101.007H7.01l-2.609 6.71L19.322 12 4.401 4.54Z"})))},"sign-out":function(e){return t.createElement("svg",vn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Cr||(Cr=t.createElement("path",{fill:"currentColor",d:"M8.502 11.5a1.002 1.002 0 1 1 0 2.004 1.002 1.002 0 0 1 0-2.004"})),kr||(kr=t.createElement("path",{fill:"currentColor",d:"M12 4.354v6.651l7.442-.001L17.72 9.28a.75.75 0 0 1-.073-.976l.073-.084a.75.75 0 0 1 .976-.073l.084.073 2.997 2.997a.75.75 0 0 1 .073.976l-.073.084-2.996 3.004a.75.75 0 0 1-1.134-.975l.072-.085 1.713-1.717-7.431.001L12 19.25a.75.75 0 0 1-.88.739l-8.5-1.502A.75.75 0 0 1 2 17.75v-12a.75.75 0 0 1 .628-.74l8.5-1.396a.75.75 0 0 1 .872.74m-1.5.883-7 1.15V17.12l7 1.236z"})),Tr||(Tr=t.createElement("path",{fill:"currentColor",d:"M13 18.501h.765l.102-.006a.75.75 0 0 0 .648-.745l-.007-4.25H13zM13.002 10 13 8.725V5h.745a.75.75 0 0 1 .743.647l.007.102.007 4.251z"})))},"signature-icon":function(e){return t.createElement("svg",yn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),zr||(zr=t.createElement("path",{fill:"currentColor",d:"M14.75 16.5c1.308 0 1.818.582 2.205 1.874l.068.237c.183.658.292.854.513.946.259.106.431.091.703-.048l.147-.083q.078-.047.176-.111l.663-.452c.616-.405 1.17-.672 1.843-.84a.75.75 0 0 1 .364 1.454 4 4 0 0 0-1.146.49l-.298.19-.48.329a6 6 0 0 1-.583.357c-.643.33-1.27.385-1.96.1-.746-.306-1.046-.78-1.327-1.721l-.156-.542c-.181-.59-.305-.68-.732-.68-.31 0-.63.155-1.069.523l-.184.16-.921.876c-1.408 1.324-2.609 1.966-4.328 1.966q-2.53.001-4.368-.768l2.947-.805q.67.073 1.421.073c1.183 0 2.032-.415 3.087-1.362l.258-.239.532-.511c.236-.227.414-.39.592-.54.684-.573 1.305-.873 2.033-.873m4.28-13.53a3.58 3.58 0 0 1 0 5.06l-.288.289c1.151 1.401 1.11 2.886.039 3.96l-2.001 2.002a.75.75 0 0 1-1.06-1.062l1.999-1.999c.485-.486.54-1.09-.04-1.838l-8.617 8.617a2.25 2.25 0 0 1-1 .58l-5.115 1.394a.75.75 0 0 1-.92-.92l1.394-5.116a2.25 2.25 0 0 1 .58-1L13.97 2.97a3.58 3.58 0 0 1 5.061 0Zm-4 1.06L5.062 14a.75.75 0 0 0-.193.332l-1.05 3.85 3.85-1.05A.75.75 0 0 0 8 16.938l9.969-9.969a2.078 2.078 0 1 0-2.94-2.939Z"})))},"signature-request":function(e){return t.createElement("svg",wn({xmlns:"http://www.w3.org/2000/svg",width:14,height:14,fill:"none",viewBox:"0 0 14 14"},e),Ir||(Ir=t.createElement("path",{fill:"#fff",d:"M11.479.77a2.445 2.445 0 0 0-3.458 0L1.396 7.397a.5.5 0 0 0-.12.195l-1.25 3.75a.5.5 0 0 0 .595.643l4-1a.5.5 0 0 0 .233-.131L10.5 5.206l.086.086a1 1 0 0 1 0 1.414l-1.44 1.44a.5.5 0 1 0 .708.707l1.439-1.44a2 2 0 0 0 0-2.828l-.086-.086.272-.271a2.445 2.445 0 0 0 0-3.457M3.967 14c-1.235 0-2.208-.567-2.855-1.107l1.284-.322c.446.25.973.428 1.57.428.406 0 .824-.164 1.27-.457s.878-.687 1.314-1.095l.113-.107c.385-.361.784-.737 1.166-.996.408-.278.95-.53 1.517-.311.376.144.62.41.791.736.161.306.272.688.38 1.105.05.19.133.374.23.498q.07.088.114.112c.025.015.036.015.039.015.122 0 .331-.082.63-.286.185-.126.344-.252.502-.378q.146-.119.302-.236a5 5 0 0 1 .697-.438 3 3 0 0 1 .278-.124l.02-.008.007-.002.003-.001h.001l.16.473-.159-.474a.5.5 0 0 1 .321.947l-.004.002-.031.012q-.046.018-.144.067c-.13.065-.321.175-.55.347-.059.043-.135.104-.223.173-.181.144-.409.325-.618.467-.31.212-.741.46-1.192.46-.436 0-.751-.265-.946-.517a2.4 2.4 0 0 1-.405-.858c-.11-.423-.196-.7-.296-.889-.09-.17-.17-.233-.266-.27-.082-.032-.245-.034-.595.205-.313.212-.655.533-1.058.912l-.1.093c-.431.405-.92.855-1.449 1.202-.53.348-1.14.621-1.818.621"})))},"star-icon":function(e){return t.createElement("svg",xn({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Nr||(Nr=t.createElement("path",{fill:"#fff",stroke:"#cccfd2",d:"M7.104.899a1 1 0 0 1 1.794 0l1.93 3.911 4.317.628a1 1 0 0 1 .554 1.705l-3.123 3.045.737 4.3a1 1 0 0 1-1.451 1.053l-3.86-2.03-3.862 2.03a1 1 0 0 1-1.45-1.054l.737-4.299L.303 7.143a1 1 0 0 1 .554-1.705l4.317-.628z"})))},"star-icon-yellow":function(e){return t.createElement("svg",En({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"#f3d429",viewBox:"0 0 16 16"},e),Sr||(Sr=t.createElement("path",{d:"M7.104.899a1 1 0 0 1 1.794 0l1.93 3.911 4.317.628a1 1 0 0 1 .554 1.705l-3.123 3.045.737 4.3a1 1 0 0 1-1.451 1.053l-3.86-2.03-3.862 2.03a1 1 0 0 1-1.45-1.054l.737-4.299L.303 7.143a1 1 0 0 1 .554-1.705l4.317-.628z"})))},"status-icon":function(e){return t.createElement("svg",Cn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Br||(Br=t.createElement("path",{fill:"currentColor",d:"M12 3a9 9 0 0 1 3.164.572l-1.183 1.192a7.5 7.5 0 1 0 5.276 5.335l1.194-1.203A9 9 0 0 1 21 12a9 9 0 1 1-9-9m9.06-.328.146.136a2.763 2.763 0 0 1 .008 3.9l-6.304 6.354a1.5 1.5 0 0 1-.652.385l-4.212 1.209a.5.5 0 0 1-.618-.619l1.21-4.22a1.5 1.5 0 0 1 .377-.642l6.309-6.36a2.74 2.74 0 0 1 3.736-.143m-2.671 1.2-6.31 6.36-.712 2.484 2.478-.71 6.304-6.355a1.263 1.263 0 0 0 .088-1.68l-.095-.107a1.24 1.24 0 0 0-1.753.007Z"})))},"success-checkmark":function(e){return t.createElement("svg",kn({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),Lr||(Lr=t.createElement("path",{fill:"#218F8B",d:"M10 0c5.523 0 10 4.477 10 10s-4.477 10-10 10S0 15.523 0 10 4.477 0 10 0m3.22 6.97-4.47 4.47-1.97-1.97a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0l5-5a.75.75 0 0 0-1.06-1.06"})))},"tasks-icon":function(e){return t.createElement("svg",Tn({xmlns:"http://www.w3.org/2000/svg",width:16,height:14,fill:"none",viewBox:"0 0 16 14"},e),Or||(Or=t.createElement("path",{fill:"#CCCFD2",d:"M3.854 1.354a.5.5 0 1 0-.708-.708L1.5 2.293l-.646-.647a.5.5 0 1 0-.708.708l1 1a.5.5 0 0 0 .708 0zM6.5 2a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zm0 5a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zM6 12.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5M3.854 6.854a.5.5 0 1 0-.708-.708L1.5 7.793l-.646-.647a.5.5 0 1 0-.708.708l1 1a.5.5 0 0 0 .708 0zm0 4.292a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708 0l-1-1a.5.5 0 0 1 .708-.708l.646.647 1.646-1.647a.5.5 0 0 1 .708 0"})))},"tasks-square-icon":function(e){return t.createElement("svg",In({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ar||(Ar=t.createElement("path",{fill:"currentColor",d:"M13.25 8.5a.75.75 0 0 0 0 1.5h3.5a.75.75 0 0 0 0-1.5zm-.75 6.25a.75.75 0 0 1 .75-.75h3.5a.75.75 0 1 1 0 1.5h-3.5a.75.75 0 0 1-.75-.75m-1.72-7.03a.75.75 0 0 1 0 1.06l-2 2a.75.75 0 0 1-1.06 0l-1-1a.75.75 0 0 1 1.06-1.06l.47.47 1.47-1.47a.75.75 0 0 1 1.06 0m0 6.56a.75.75 0 1 0-1.06-1.06l-1.47 1.47-.47-.47a.75.75 0 0 0-1.06 1.06l1 1a.75.75 0 0 0 1.06 0zM5.25 3A2.25 2.25 0 0 0 3 5.25v13.5A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V5.25A2.25 2.25 0 0 0 18.75 3zM4.5 5.25a.75.75 0 0 1 .75-.75h13.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H5.25a.75.75 0 0 1-.75-.75z"})))},"task-ltr-icon":function(e){return t.createElement("svg",zn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Dr||(Dr=t.createElement("path",{fill:"currentColor",d:"M6.78 4.78a.75.75 0 0 0-1.06-1.06L3.75 5.69l-.47-.47a.75.75 0 0 0-1.06 1.06l1 1a.75.75 0 0 0 1.06 0zm14.47 13.227H9.75l-.102.007a.75.75 0 0 0 .102 1.493h11.5l.102-.007a.75.75 0 0 0-.102-1.493m0-6.507H9.75l-.102.007A.75.75 0 0 0 9.75 13h11.5l.102-.007a.75.75 0 0 0-.102-1.493m0-6.5H9.75l-.102.007A.75.75 0 0 0 9.75 6.5h11.5l.102-.007A.75.75 0 0 0 21.25 5M6.78 17.78a.75.75 0 1 0-1.06-1.06l-1.97 1.97-.47-.47a.75.75 0 0 0-1.06 1.06l1 1a.75.75 0 0 0 1.06 0zm0-7.56a.75.75 0 0 1 0 1.06l-2.5 2.5a.75.75 0 0 1-1.06 0l-1-1a.75.75 0 1 1 1.06-1.06l.47.47 1.97-1.97a.75.75 0 0 1 1.06 0"})))},"teams-icon":function(e){return t.createElement("svg",Nn({xmlns:"http://www.w3.org/2000/svg",width:20,height:18,fill:"none",viewBox:"0 0 20 18"},e),jr||(jr=t.createElement("path",{fill:"#333",d:"M12.754 7c.966 0 1.75.784 1.75 1.75v4.749a4.501 4.501 0 0 1-9.002 0V8.75c0-.966.783-1.75 1.75-1.75zm0 1.5H7.252a.25.25 0 0 0-.25.25v4.749a3.001 3.001 0 0 0 6.002 0V8.75a.25.25 0 0 0-.25-.25M1.75 7h3.381a2.74 2.74 0 0 0-.618 1.5H1.75a.25.25 0 0 0-.25.25v3.249a2.5 2.5 0 0 0 3.082 2.433c.085.504.24.985.453 1.432Q4.539 15.999 4 16A4 4 0 0 1 0 11.999V8.75C0 7.784.784 7 1.75 7m13.125 0h3.375c.966 0 1.75.784 1.75 1.75V12a4 4 0 0 1-5.03 3.866c.214-.448.369-.929.455-1.433q.277.066.575.067a2.5 2.5 0 0 0 2.5-2.5V8.75a.25.25 0 0 0-.25-.25h-2.757a2.74 2.74 0 0 0-.618-1.5M10 0a3 3 0 1 1 0 6 3 3 0 0 1 0-6m6.5 1a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5m-13 0a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5m6.5.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3m6.5 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2m-13 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2"})))},"text-box":function(e){return t.createElement("svg",Sn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Mr||(Mr=t.createElement("path",{fill:"currentColor",d:"M18.25 3A2.75 2.75 0 0 1 21 5.75v12.5A2.75 2.75 0 0 1 18.25 21H5.75A2.75 2.75 0 0 1 3 18.25V5.75A2.75 2.75 0 0 1 5.75 3zm0 1.5H5.75c-.69 0-1.25.56-1.25 1.25v12.5c0 .69.56 1.25 1.25 1.25h12.5c.69 0 1.25-.56 1.25-1.25V5.75c0-.69-.56-1.25-1.25-1.25m-4 7h-7.5l-.102.007A.75.75 0 0 0 6.75 13h7.5l.102-.007a.75.75 0 0 0-.102-1.493m-7.5 4h10.5a.75.75 0 0 1 .102 1.493L17.25 17H6.75a.75.75 0 0 1-.102-1.493zm10.5-8H6.75l-.102.007A.75.75 0 0 0 6.75 9h10.5l.102-.007A.75.75 0 0 0 17.25 7.5"})))},"typing-icon":function(e){return t.createElement("svg",Bn({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Pr||(Pr=t.createElement("path",{fill:"currentColor",d:"M6.5 2a.5.5 0 0 0 0 1h1v10h-1a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-1V3h1a.5.5 0 0 0 0-1zM4 4h2.5v1H4a1 1 0 0 0-1 1v3.997a1 1 0 0 0 1 1h2.5v1H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2m8 6.997H9.5v1H12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H9.5v1H12a1 1 0 0 1 1 1v3.997a1 1 0 0 1-1 1"})))},"up-arrow":function(e){return t.createElement("svg",On({xmlns:"http://www.w3.org/2000/svg",width:10,height:12,fill:"none",viewBox:"0 0 10 12"},e),Rr||(Rr=t.createElement("path",{fill:"#fff",d:"M.75 0a.75.75 0 1 0 0 1.5h8.5a.75.75 0 0 0 0-1.5zm4.78 2.72a.75.75 0 0 0-1.06 0L1.22 5.97a.75.75 0 0 0 1.06 1.06l1.97-1.97v6.19a.75.75 0 0 0 1.5 0V5.06l1.97 1.97a.75.75 0 0 0 1.06-1.06z"})))},"upcoming-icon":function(e){return t.createElement("svg",Wn({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),$r||($r=t.createElement("rect",{width:16,height:16,fill:"#fff",rx:8})),eo||(eo=t.createElement("path",{fill:"#CCCFD2",d:"M4 8a4 4 0 1 1 8 0 4 4 0 0 1-8 0"})))},"updated-burger":function(e){return t.createElement("svg",Ln({xmlns:"http://www.w3.org/2000/svg",width:40,height:40,fill:"none",viewBox:"0 0 40 40"},e),Hr||(Hr=t.createElement("path",{fill:"#F2F2F2",d:"M10.753 26h18.5a.75.75 0 0 1 .102 1.493l-.102.007h-18.5a.75.75 0 0 1-.102-1.494zh18.5zm0-6.497h18.5a.75.75 0 0 1 .102 1.493l-.102.007h-18.5a.75.75 0 0 1-.102-1.494zh18.5zm-.001-6.5h18.5a.75.75 0 0 1 .102 1.493l-.102.007h-18.5a.75.75 0 0 1-.102-1.493zh18.5z"})))},"warning-icon":function(e){return t.createElement("svg",An({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 16 16"},e),Vr||(Vr=t.createElement("path",{fill:"currentColor",d:"M8.492 6.91A.5.5 0 0 0 7.5 7v4.502l.008.09a.5.5 0 0 0 .992-.09V7zM8.8 4.75a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0M16 8A8 8 0 1 0 0 8a8 8 0 0 0 16 0M1 8a7 7 0 1 1 14 0A7 7 0 0 1 1 8"})))},"warning-icon-solid":function(e){return t.createElement("svg",Dn({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Fr||(Fr=t.createElement("path",{fill:"#D67900",d:"M10.03 3.659c.856-1.548 3.081-1.548 3.937 0l7.746 14.001c.83 1.5-.255 3.34-1.969 3.34H4.254c-1.715 0-2.8-1.84-1.97-3.34zM12.997 17A.999.999 0 1 0 11 17a.999.999 0 0 0 1.997 0m-.259-7.853a.75.75 0 0 0-1.493.103l.004 4.501.007.102a.75.75 0 0 0 1.493-.103l-.004-4.502z"})))},alert:function(e){return t.createElement("svg",_t({xmlns:"http://www.w3.org/2000/svg",width:48,height:48,fill:"none",viewBox:"0 0 48 48"},e),M||(M=t.createElement("path",{fill:"currentColor",d:"M11.5 19v8.75c0 .174-.036.346-.106.505L8.638 34.5h30.724l-2.755-6.245a1.25 1.25 0 0 1-.107-.505V19c0-6.904-5.596-12.5-12.5-12.5-6.903 0-12.5 5.596-12.5 12.5M18 37H8.254a2.25 2.25 0 0 1-2.058-3.158L9 27.487V19c0-8.284 6.716-15 15-15s15 6.716 15 15v8.487l2.804 6.355A2.25 2.25 0 0 1 39.746 37H30v1a6 6 0 0 1-12 0zm9.5 0h-7v1a3.5 3.5 0 1 0 7 0z"})))},chevron:function(e){return t.createElement("svg",St({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",viewBox:"0 0 20 20"},e),re||(re=t.createElement("path",{fill:"#0062B8",d:"M7.733 4.207a.75.75 0 0 1 1.06.026l5.001 5.25a.75.75 0 0 1 0 1.035l-5 5.25a.75.75 0 1 1-1.087-1.034L12.216 10l-4.51-4.734a.75.75 0 0 1 .027-1.06"})))},circle:function(e){return t.createElement("svg",Kt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),we||(we=t.createElement("path",{fill:"currentColor",d:"M12 3.5a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12"})))},info:function(e){return t.createElement("svg",Vo({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none",viewBox:"0 0 24 24"},e),Ka||(Ka=t.createElement("path",{fill:"currentColor",d:"M12 1.999c5.524 0 10.002 4.478 10.002 10.002 0 5.523-4.478 10.001-10.002 10.001S1.998 17.524 1.998 12.001C1.998 6.477 6.476 1.999 12 1.999m0 1.5a8.502 8.502 0 1 0 0 17.003A8.502 8.502 0 0 0 12 3.5Zm-.004 7a.75.75 0 0 1 .744.648l.007.102.003 5.502a.75.75 0 0 1-1.493.102l-.007-.101-.003-5.502a.75.75 0 0 1 .75-.75ZM12 7.003A.999.999 0 1 1 12 9a.999.999 0 0 1 0-1.997"})))},priority:function(e){return t.createElement("svg",sn({xmlns:"http://www.w3.org/2000/svg",width:12,height:11.5,fill:"none",viewBox:"0 0 12 11.5"},e),gr||(gr=t.createElement("path",{fill:"#E81A3B",d:"M.207 11.267a.75.75 0 0 1 .026-1.06l5.25-5.001a.75.75 0 0 1 1.034 0l5.25 5a.75.75 0 0 1-1.034 1.087L6 6.784l-4.733 4.51a.75.75 0 0 1-1.06-.027m0-4.998a.75.75 0 0 1 .026-1.06L5.483.206a.75.75 0 0 1 1.034 0l5.25 5.001a.75.75 0 0 1-1.034 1.086L6 1.786 1.267 6.294a.75.75 0 0 1-1.06-.025"})))}});S(".icon-module__iconComponent___ssGnu{align-items:center;display:flex}");var rl,ol,nl=function(e){var t=e.iconName,r=e.size,o=e.dataTestId;if(!t)return null;var n=al[t];return n?a.createElement(n,{className:"icon-module__iconComponent___ssGnu","aria-label":e.altText,width:r||e.width||16,height:r||e.height||16,color:e.color,"data-testid":void 0===o?"uikit-icon":o,role:e.role,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy,tabIndex:e.tabIndex,"aria-hidden":"true"}):(t&&console.error("Icon not found: "+t),null)};(rl=exports.ButtonTypeEnum||(exports.ButtonTypeEnum={})).primary="primary",rl.secondary="secondary",rl.tertiary="tertiary",(ol=exports.ButtonSizeEnum||(exports.ButtonSizeEnum={})).large="large",ol.long="long",ol.small="small",ol.icon="icon";var ll,il,cl={button:"button-module__button___YrVJX",large:"button-module__large___jSy8V",small:"button-module__small___7YW5d",long:"button-module__long___9LkWB",primary:"button-module__primary___2nlh8",secondary:"button-module__secondary___8gbTk",tertiary:"button-module__tertiary___I32Jg",warningButton:"button-module__warningButton___gUstU",iconWrapper:"button-module__iconWrapper___BCrPj",border:"button-module__border___8WoN8",noBorder:"button-module__noBorder___g2A55",isHighLighted:"button-module__isHighLighted___W0kGv",spinner:"button-module__spinner___-IXcL",spinnerWithLabel:"button-module__spinnerWithLabel___3QIxn"};S(".button-module__button___YrVJX{align-items:center;border-radius:2px;cursor:pointer;display:flex;flex-direction:row;font-family:ProximaNova,Arial,sans-serif;font-size:var(--fontsize-body);font-style:normal;font-weight:600;gap:.5rem;justify-content:center;line-height:var(--lineheight-body);transition:background-color .2s}.button-module__button___YrVJX:focus-visible{outline:3px solid var(--color-secondary-cobalt);outline-offset:2px}.button-module__button___YrVJX.button-module__large___jSy8V{height:56px;padding:.5rem 1.5rem}.button-module__button___YrVJX.button-module__small___7YW5d{height:40px;padding:.5rem 1rem}.button-module__button___YrVJX.button-module__long___9LkWB{height:44px;text-align:left;width:208px}.button-module__button___YrVJX.button-module__primary___2nlh8:disabled,.button-module__button___YrVJX.button-module__primary___2nlh8:disabled:hover{background-color:var(--color-primary-pale-charcoal);color:var(--color-bg-inactive-charcoal);cursor:not-allowed;path{fill:var(--color-bg-inactive-charcoal)}}.button-module__button___YrVJX.button-module__primary___2nlh8{background-color:var(--color-primary-red);border:none;color:var(--color-bg-white)}.button-module__button___YrVJX.button-module__primary___2nlh8:hover{background-color:var(--color-primary-slate)}.button-module__button___YrVJX.button-module__secondary___8gbTk{background-color:var(--color-bg-white);border:1px solid var(--color-primary-charcoal);color:var(--color-primary-charcoal);path{fill:var(--color-primary-charcoal)}}.button-module__button___YrVJX.button-module__secondary___8gbTk:hover{background-color:var(--color-primary-charcoal);color:var(--color-bg-white);path{fill:var(--color-bg-white)}}.button-module__button___YrVJX.button-module__secondary___8gbTk:disabled,.button-module__button___YrVJX.button-module__secondary___8gbTk:disabled:hover{background-color:transparent;border:1px solid var(--color-primary-pale-charcoal);color:var(--color-bg-inactive-charcoal);cursor:not-allowed;path{fill:var(--color-bg-inactive-charcoal)}}.button-module__button___YrVJX.button-module__tertiary___I32Jg{background-color:transparent;border:none;color:var(--color-primary-charcoal);path{fill:var(--color-primary-charcoal)}&:hover{text-decoration:underline}}.button-module__button___YrVJX.button-module__tertiary___I32Jg:disabled,.button-module__button___YrVJX.button-module__tertiary___I32Jg:disabled:hover{color:var(--color-primary-inactive-charcoal);cursor:not-allowed;path{fill:var(--color-primary-inactive-charcoal)}}.button-module__button___YrVJX.button-module__warningButton___gUstU{color:var(--color-secondary-burgundy);svg{path{fill:var(--color-secondary-burgundy)}}}.button-module__iconWrapper___BCrPj,.button-module__iconWrapper___BCrPj.button-module__small___7YW5d{align-items:center;display:flex;height:100%;justify-content:center}.button-module__button___YrVJX.button-module__border___8WoN8{border:1px solid var(--color-primary-charcoal)}.button-module__button___YrVJX.button-module__noBorder___g2A55{border:none}.button-module__isHighLighted___W0kGv{background-color:var(--color-bg-light-grey)!important;color:var(--color-primary-light-charcoal)!important}.button-module__spinner___-IXcL{display:flex;padding:0 1rem}.button-module__spinnerWithLabel___3QIxn{padding:0}"),S('.spinner-module__loader___wXiJo{animation:spinner-module__rotation___g99zR 1s linear infinite;border:5px solid var(--color-primary-red);border-radius:50%;border-top-color:var(--color-border-white);box-sizing:border-box;display:inline-block}.spinner-module__dropdownLoader___Azm1k,.spinner-module__tableLoader___wc-S5{border-color:var(--color-primary-red);border-top-color:var(--color-bg-light-grey)}.spinner-module__buttonLoader___eLwVY{border-color:var(--color-primary-red);border-top-color:var(--color-primary-pale-charcoal)}@keyframes spinner-module__rotation___g99zR{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.spinner-module__spinnerWrapper___U9V1m{display:inline-block;height:100%;position:relative;width:100%}.spinner-module__spinnerWrapper___U9V1m:before{background:inherit;bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:var(--z-index-spinner)}.spinner-module__spinnerCentered___R1zF-{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);z-index:var(--z-index-spinner)}'),(ll=exports.SpinnerSize||(exports.SpinnerSize={})).SMALL="small",ll.MEDIUM="medium",ll.LARGE="large",ll.EXTRA_LARGE="extraLarge",(il=exports.SpinnerType||(exports.SpinnerType={})).PAGE="page",il.DROPDOWN="dropdown",il.BUTTON="button",il.TABLE="table";var dl=function(e){var t,o=e.children,n=e.background,l=e.minHeight,i=e.size,c=void 0===i?exports.SpinnerSize.MEDIUM:i,d=e.message,s=e.dataTestId,_=void 0===s?"uikit-spinner":s,m=e.role,u=e.ariaLabel,p=void 0===u?"Animated Spinner":u,g=e.ariaDescribedBy,h=e.ariaLabelledBy,f=e.tabIndex,b=e.borderColor,v=e.borderWidth,w=e.type,y=void 0===w?exports.SpinnerType.PAGE:w;if(!e.isLoading)return a.createElement(a.Fragment,null,o);var x=function(){if("number"==typeof c)return c;if("string"==typeof c&&c.includes("px"))return parseInt(c.replace("px",""));switch(c){case exports.SpinnerSize.SMALL:return 16;case exports.SpinnerSize.MEDIUM:return 24;case exports.SpinnerSize.LARGE:return 40;case exports.SpinnerSize.EXTRA_LARGE:return 48;default:return 24}}(),E=function(e){return Math.max(1,Math.round(e/12))},C=E(x),k=at({width:x,height:x,borderWidth:v||C},b&&{borderColor:"transparent transparent "+b+" transparent",borderBottomColor:b});c===exports.SpinnerSize.MEDIUM&&(y===exports.SpinnerType.BUTTON?(k.width=20,k.height=20,k.borderWidth=v||E(20)):y!==exports.SpinnerType.DROPDOWN&&y!==exports.SpinnerType.TABLE||(k.width=16,k.height=16,k.borderWidth=v||E(16)));var T=r("spinner-module__loader___wXiJo",((t={})["spinner-module__dropdownLoader___Azm1k"]=y===exports.SpinnerType.DROPDOWN,t["spinner-module__tableLoader___wc-S5"]=y===exports.SpinnerType.TABLE,t["spinner-module__buttonLoader___eLwVY"]=y===exports.SpinnerType.BUTTON,t));return o?a.createElement("div",{className:"spinner-module__spinnerWrapper___U9V1m",style:{background:n,minHeight:l},"data-testid":_,role:m,"aria-label":p,"aria-describedby":g,"aria-labelledby":h,tabIndex:f},a.createElement("div",{className:"spinner-module__spinnerCentered___R1zF-"},a.createElement("span",{className:T,style:k}),d&&a.createElement("div",null,d)),o):a.createElement("span",{className:T,style:k})},sl=["id","type","size","onClick","label","rightIconName","disabled","withRightIcon","withLeftIcon","leftIconName","dataTestId","role","ariaLabel","ariaDescribedBy","ariaLabelledBy","tabIndex","withBorder","isHighLighted","loading","labelOnLoading","className","isWarning"],_l=t.forwardRef((function(e,t){var o,n,l=e.id,i=e.type,c=e.size,d=e.onClick,s=e.label,_=e.rightIconName,m=e.disabled,u=void 0!==m&&m,p=e.withRightIcon,g=void 0===p||p,h=e.withLeftIcon,f=void 0!==h&&h,b=e.leftIconName,v=e.dataTestId,w=void 0===v?"uikit-button":v,y=e.role,x=e.ariaLabel,E=void 0===x?"":x,C=e.ariaDescribedBy,k=e.ariaLabelledBy,T=e.tabIndex,I=e.withBorder,z=void 0!==I&&I,N=e.isHighLighted,S=void 0!==N&&N,B=e.loading,L=void 0!==B&&B,O=e.labelOnLoading,A=void 0!==O&&O,D=e.className,j=e.isWarning,M=void 0!==j&&j,P=rt(e,sl);return a.createElement("button",Object.assign({},P,{ref:t,disabled:u||L,id:l,className:r(D,cl.button,cl[i],cl[c],(o={},o[cl.border]=z,o[cl.noBorder]=!z,o[cl.isHighLighted]=S,o[cl.warningButton]=M,o)),onClick:d,"data-testid":w,role:y,"aria-label":E,"aria-describedby":C,"aria-labelledby":k,tabIndex:T}),L?a.createElement(a.Fragment,null,a.createElement("div",{className:r(cl.spinner,(n={},n[cl.spinnerWithLabel]=A,n))},a.createElement(dl,{isLoading:!0,background:"rgba(0,0,0,0.4)",type:exports.SpinnerType.BUTTON})),A?s:""):a.createElement(a.Fragment,null,f&&a.createElement("div",{className:cl.iconWrapper+" "+cl[i]+" "+cl[c]+" "+(u?cl.disabled:"")},a.createElement(nl,{iconName:b,altText:b,size:c===exports.ButtonSizeEnum.large?24:16})),s,g&&a.createElement("div",{className:cl.iconWrapper+" "+cl[i]+" "+cl[c]+" "+(u?cl.disabled:"")},a.createElement(nl,{iconName:_||"arrow-right",altText:_||"arrow-right",size:c===exports.ButtonSizeEnum.large?24:16}))))}));function ml(){var e=t.useRef(null),a=t.useRef(null),r=t.useCallback((function(t){e.current=t,a.current=null}),[]),o=t.useCallback((function(t){a.current=t,e.current=null}),[]),n=t.useCallback((function(){setTimeout((function(){if(a.current)a.current.focus();else if(e.current){var t,r=document.getElementById(null!=(t=e.current)?t:"");null==r||r.focus()}l()}),100)}),[]),l=t.useCallback((function(){e.current=null,a.current=null}),[]);return{saveSelector:r,saveRef:o,focus:n,reset:l}}var ul=o.singletonHook({saveSelector:function(){},saveRef:function(){},focus:function(){},reset:function(){}},ml),pl=["Tab","ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Enter"],gl=o.singletonHook({focusClass:"focusedByClick"},(function(){var e=t.useState("mouse"),a=e[0],r=e[1];return t.useEffect((function(){var e=function(e){pl.includes(e.key)&&r("keyboard")},t=function(){return r("mouse")};return window.addEventListener("keydown",e),window.addEventListener("mousedown",t),function(){window.removeEventListener("keydown",e),window.removeEventListener("mousedown",t)}}),[]),{focusClass:"mouse"===a?"focusedByClick":"focusedByKeyboard"}})),hl=function(e){var t=n.parseISO(e);return n.isPast(t)&&!n.isToday(t)},fl=function(e,t){if(void 0===t&&(t="en-CA"),!e)return"";var a=t.includes("fr")?"fr-CA":"en-CA",r=n.parseISO(e);return new Intl.DateTimeFormat(a,{month:"short",day:"2-digit",year:"numeric"}).format(r)},bl=function(e,t,a,r){if(void 0===t&&(t="en-CA"),void 0===a&&(a=!1),void 0===r&&(r=!1),!e)return"";var o=n.parseISO(e),l=t.includes("fr")?"fr-CA":"en-US",i=new Intl.DateTimeFormat(l,{hour:"numeric",minute:"2-digit",hour12:"fr-CA"!=l}).format(o);if(n.isToday(o)){var c=t.includes("fr")?"aujourd'hui à":"today at";return r?c.charAt(0).toUpperCase()+c.slice(1)+" "+i:c+" "+i}var d=fl(e,t);return a?d+" "+(t.includes("fr")?"à":"at")+" "+i:d},vl=function(e,t){if(void 0===t&&(t="en-CA"),!e||0===e.size)return"";var a=Array.from(e).sort((function(e,t){return new Date(e).getTime()-new Date(t).getTime()}));if(a.length>=2){var r=new Date(a[0]),o=new Date(a[1]),l=fl(n.formatISO(r),t),i=fl(n.formatISO(o),t);return r.getFullYear()===o.getFullYear()&&r.getMonth()===o.getMonth()&&r.getDate()===o.getDate()?l:l+" – "+i}return 1==a.length?fl(n.formatISO(a[0]),t):""},wl=function(e){var t=new Date,a=null,r=null;switch(e){case"today":return{startOfRange:a=new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,0,0),endOfRange:r=new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,0,0)};case"last7Days":return r=t,(a=new Date).setDate(r.getUTCDate()-6),{startOfRange:a,endOfRange:r};case"last30Days":return r=t,(a=new Date).setDate(r.getUTCDate()-29),{startOfRange:a,endOfRange:r}}},yl=function(e){return"Deliverables"===e.split("/").filter(Boolean)[0]},xl=function(e,t){void 0===t&&(t="download");var a=document.createElement("a");a.href=e,a.target="_self",a.rel="noopener noreferrer",a.download=t,a.style.display="none",a.dataset.interception="off",document.body.appendChild(a),a.click(),document.body.removeChild(a)};function El(e){var t;if(!e)return"";var a=e.replace(/-/g," ").split(" ").filter(Boolean);return 0===a.length?"":a[0].charAt(0).toUpperCase()+((null==(t=a[1])?void 0:t.charAt(0).toUpperCase())||"")}var Cl=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;function kl(e){return Cl.test(e)}var Tl=function(e){return e.replace(/(.*)\..*/,"$1")},Il=function(e){var t=e.lastIndexOf(".");return t>0?e.slice(t+1).toLowerCase():""},zl=["pdf","jpg","jpeg","png","gif","zip","zipx","rar","7z","txt","log","csv","doc","docx","docm","rtf","xls","xlsx","xlsm","xlm","ppt","pptx","pptm","eml","msg","vsd","vsdx","html","htm","xsl","xslt","xml","xps","mp4","gme","preview","nd","qbw","sds","tlg"],Nl=["application/php"],Sl=function(e){var t,a=[],r=Tl(e.name),o=Il(e.name),n=r.includes("."),l=zl.includes(o);return n&&a.push(i.t("documents:error-invalid-file-name")),Nl.includes(e.type)&&a.push(i.t("documents:error-invalid-file-type")),l||a.push(i.t("documents:error-invalid-file-type")),(/[.*:<>?\\/~|"';()]/.test(t=r)||t.length>=178)&&a.push(i.t("documents:error-invalid-file-name")),e.size>2147483648&&a.push(i.t("documents:error-too-large")),{isValid:0===a.length,errors:a}};function Bl(e,a){t.useEffect((function(){if(e){var t=function(e){"Escape"===e.key&&(e.preventDefault(),null==a||a())};return document.addEventListener("keydown",t),function(){document.removeEventListener("keydown",t)}}}),[e,a])}var Ll,Ol=["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed",'[tabindex]:not([tabindex="-1"])',"[contenteditable]"],Al=function(e){return e.current?Array.from(e.current.querySelectorAll(Ol.join(","))).filter((function(e){return null!==e.offsetParent&&!e.hasAttribute("disabled")&&"-1"!==e.getAttribute("tabindex")&&"true"!==e.getAttribute("aria-hidden")})):[]},Dl={chip:"chip-module__chip___oBqxy","chip--priority":"chip-module__chip--priority___NZvsn","chip--request":"chip-module__chip--request___D-qms","chip--readOnly":"chip-module__chip--readOnly___-nsx0","chip--warning":"chip-module__chip--warning___8-1Kp","chip--error":"chip-module__chip--error___W42N9","chip--action":"chip-module__chip--action___Mmctx","chip--location":"chip-module__chip--location___qJKYY","chip--overview":"chip-module__chip--overview___M1ZQB","chip--status":"chip-module__chip--status___02PII","chip--dueDate":"chip-module__chip--dueDate___exnOK",textOnly:"chip-module__textOnly___qOidK",interactive:"chip-module__interactive___NvwkH",buttonReset:"chip-module__buttonReset___CR9r6",avatar:"chip-module__avatar___Hw4aY","avatar--priority":"chip-module__avatar--priority___CWgkZ","avatar--request":"chip-module__avatar--request___mStdv","avatar--warning":"chip-module__avatar--warning___G2c7K","avatar--error":"chip-module__avatar--error___9WvK9","avatar--action":"chip-module__avatar--action___LyUXs","avatar--status":"chip-module__avatar--status___KieJv",text:"chip-module__text___lu-WO","text--request":"chip-module__text--request___mbOAK","text--readOnly":"chip-module__text--readOnly___A12EK","text--priority":"chip-module__text--priority___S4cdv","text--action":"chip-module__text--action___pQ3ZM","text--overview":"chip-module__text--overview___5yQI7","text--location":"chip-module__text--location___A2pLY","text--status":"chip-module__text--status___ZFZWF",dismissButton:"chip-module__dismissButton___BYZiH"};S(".chip-module__chip___oBqxy{text-wrap-mode:nowrap;align-items:center;border:1px solid var(--color-border-grey);border-radius:2px;display:grid;grid-template-columns:auto 1fr auto;padding:.25rem .5rem}.chip-module__chip--priority___NZvsn,.chip-module__chip--request___D-qms{background-color:var(--color-bg-light-grey);border:none;padding:.25rem .5rem .25rem .25rem}.chip-module__chip--readOnly___-nsx0{background-color:transparent;border:1px solid var(--color-border-grey);border-radius:2px;gap:.25rem;padding:.125rem .25rem;width:fit-content}.chip-module__chip--warning___8-1Kp{border:1px solid var(--color-secondary-gold);border-radius:2px;padding:.25rem .5rem}.chip-module__chip--error___W42N9{border:none;border-bottom:1px solid var(--color-secondary-burgundy);border-radius:0;padding:.25rem 0}.chip-module__chip--action___Mmctx{padding:.125rem .5rem .125rem .25rem;width:fit-content}.chip-module__chip--action___Mmctx,.chip-module__chip--location___qJKYY{background-color:var(--color-bg-light-grey);border:none;border-radius:100px;color:var(--color-primary-slate);grid-template-columns:auto 1fr;justify-content:start;transition:all .1s ease-out}.chip-module__chip--location___qJKYY{column-gap:.5rem;padding:.125rem .5rem}.chip-module__chip--overview___M1ZQB{background-color:#fff;border:1px solid #e7e7e7;border-radius:100px;color:var(--color-primary-slate);grid-template-columns:auto 1fr}.chip-module__chip--overview___M1ZQB,.chip-module__chip--status___02PII{column-gap:.5rem;justify-content:start;padding:.125rem .5rem;transition:all .1s ease-out}.chip-module__chip--status___02PII{align-items:center;background-color:#e5eff8;border:none;border-radius:2px;color:var(--color-secondary-cobalt)!important}.chip-module__chip--dueDate___exnOK{align-items:center;background-color:var(--color-bg-white);border:none;border-radius:6.25rem;display:flex;flex-direction:row;font-size:var(--fontsize-body-small);gap:.5rem;padding:.125rem .5rem}.chip-module__textOnly___qOidK{grid-template-columns:none;padding:.25rem .5rem}.chip-module__chip--location___qJKYY{&.chip-module__interactive___NvwkH{&:hover{background-color:var(--color-bg-primary-pale-charcoal);cursor:pointer}}}.chip-module__buttonReset___CR9r6{padding:0;&:focus-visible{outline:none;.chip-module__chip--location___qJKYY{outline:2px solid var(--color-secondary-cobalt);outline-offset:-2px}}}.chip-module__avatar___Hw4aY{border-radius:9999px;height:1.5rem;margin-right:.5rem;width:1.5rem}.chip-module__avatar--priority___CWgkZ,.chip-module__avatar--request___mStdv,.chip-module__avatar___Hw4aY{align-items:center;display:grid;font-size:var(--fontsize-body-label);justify-content:center;line-height:var(--lineheight-body-label)}.chip-module__avatar--priority___CWgkZ,.chip-module__avatar--request___mStdv{border-radius:2px;color:var(--color-bg-white);height:1.25rem}.chip-module__avatar--request___mStdv{width:1.25rem;svg{path{fill:#fff}}}.chip-module__avatar--priority___CWgkZ{padding-right:.5rem;svg{path{fill:var(--color-primary-red)}}}.chip-module__avatar--request___mStdv:has([aria-label=up-arrow]){background-color:var(--color-secondary-ocean);margin-right:.5rem}.chip-module__avatar--request___mStdv:has([aria-label=signature-request]){background-color:var(--color-secondary-jade);margin-right:.5rem}.chip-module__avatar--warning___G2c7K{align-items:center;background-color:var(--color-secondary-ocean);background-color:var(--color-secondary-gold);border-radius:9999px;color:var(--color-bg-white);display:grid;font-size:var(--fontsize-body-label);height:1.5rem;justify-content:center;line-height:var(--lineheight-body-label);margin-right:.5rem;width:1.5rem}.chip-module__avatar--error___9WvK9{margin-right:.5rem}.chip-module__avatar--action___LyUXs{align-items:center;background-color:none;color:var(--color-primary-light-slate);padding-left:4px;padding-right:4px}.chip-module__avatar--status___KieJv{display:none}.chip-module__text___lu-WO{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-small);font-weight:600;line-height:var(--lineheight-body-small)}.chip-module__text--request___mbOAK{color:var(--color-primary-charcoal)}.chip-module__text--readOnly___A12EK,.chip-module__text--request___mbOAK{font-family:var(--primary-font-family);font-size:var(--fontsize-body-small);font-weight:400;line-height:var(--lineheight-body-small)}.chip-module__text--readOnly___A12EK{color:var(--color-primary-inactive-charcoal)}.chip-module__text--priority___S4cdv{font-weight:400}.chip-module__text--action___pQ3ZM,.chip-module__text--priority___S4cdv{color:var(--color-primary-light-charcoal);font-family:var(--primary-font-family);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small)}.chip-module__text--action___pQ3ZM{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.chip-module__text--location___A2pLY,.chip-module__text--overview___5yQI7{color:var(--color-primary-light-charcoal);font-family:ProximaNova,Arial,sans-serif;font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small)}.chip-module__text--location___A2pLY{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.chip-module__text--status___ZFZWF{color:var(--color-primary-light-charcoal);color:#0062b8;font-family:ProximaNova,Arial,sans-serif;font-size:12px;font-weight:600;line-height:16px;text-align:center}.chip-module__dismissButton___BYZiH{align-items:center;background:none;border:none;border-radius:50%;color:var(--color-primary-slate);cursor:pointer;display:grid;margin-left:.5rem;outline:0;padding:.25rem;transition:background-color .2s;&:hover{background-color:var(--color-bg-light-grey)}}"),(Ll=exports.ChipType||(exports.ChipType={})).DEFAULT="default",Ll.WARNING="warning",Ll.ERROR="error",Ll.ACTION="action",Ll.LOCATION="location",Ll.REQUEST="request",Ll.PRIORITY="priority",Ll.STATUS="status",Ll.DUE_DATE="dueDate",Ll.OVERVIEW="overview",Ll.READONLY="readOnly";var jl,Ml,Pl={avatar:"avatar-module__avatar___eebFp","avatar--x-small":"avatar-module__avatar--x-small___o0h9k","avatar--small":"avatar-module__avatar--small___Xyh-y","avatar--small-medium":"avatar-module__avatar--small-medium___t75O2","avatar--medium-small":"avatar-module__avatar--medium-small___j-FkF","avatar--medium":"avatar-module__avatar--medium___bg5Wa","avatar--large":"avatar-module__avatar--large___eoMcN","avatar--x-large":"avatar-module__avatar--x-large___oqTYK",clickable:"avatar-module__clickable___DabDR",fallback:"avatar-module__fallback___sXkqT","fallback--chip":"avatar-module__fallback--chip___Ix5MA","fallback--x-small":"avatar-module__fallback--x-small___xZzGD","fallback--small":"avatar-module__fallback--small___Bn-Rc","fallback-small-medium":"avatar-module__fallback-small-medium___eY3Bq","fallback-medium-small":"avatar-module__fallback-medium-small___E-blx",fallbackBorder:"avatar-module__fallbackBorder___UCUbz",initials:"avatar-module__initials___dUWaf","initials--x-small":"avatar-module__initials--x-small___IjWaY","initials--small":"avatar-module__initials--small___R-w3j","initials--large":"avatar-module__initials--large___jocha","initials--bold":"avatar-module__initials--bold___JXaq0","fallback--small-medium":"avatar-module__fallback--small-medium___xNJIF","fallback--medium-small":"avatar-module__fallback--medium-small___yHhIC"};S(".avatar-module__avatar___eebFp{border-radius:50%;display:block;height:4rem;width:4rem}.avatar-module__avatar--x-small___o0h9k{font-size:var(--fontsize-body-xsmall);height:1.5rem;width:1.5rem}.avatar-module__avatar--small___Xyh-y{font-size:var(--fontsize-body-xsmall);height:2rem;width:2rem}.avatar-module__avatar--small-medium___t75O2{font-size:var(--fontsize-body-small);height:2.5rem;width:2.5rem}.avatar-module__avatar--medium-small___j-FkF{font-size:var(--fontsize-body-small);height:3.5rem;width:3.5rem}.avatar-module__avatar--medium___bg5Wa{font-size:var(--fontsize-body-small);height:4rem;width:4rem}.avatar-module__avatar--large___eoMcN{font-size:var(--fontsize-h5);font-weight:600;height:7.5rem;width:7.5rem}.avatar-module__avatar--x-large___oqTYK{font-size:var(--fontsize-h5);font-weight:600;height:9.375rem;width:9.375rem}.avatar-module__clickable___DabDR{cursor:pointer}.avatar-module__fallback___sXkqT{align-items:center;background-color:var(--color-bg-primary-pale-charcoal);border-radius:50%;color:var(--color-primary-charcoal);display:flex;font-size:var(--fontsize-body-small);font-weight:600;height:3rem;justify-content:center;width:3rem}.avatar-module__fallback--chip___Ix5MA{font-size:var(--fontsize-body-xsmall);height:1rem;width:1rem}.avatar-module__fallback--x-small___xZzGD{font-size:.75rem;height:1.5rem!important;width:1.5rem!important}.avatar-module__fallback--small___Bn-Rc{font-size:1rem;height:2rem;width:2rem}.avatar-module__fallback-small-medium___eY3Bq{height:2.5rem;width:2.5rem}.avatar-module__fallback-medium-small___E-blx{font-size:1.25rem;height:3.5rem;width:3.5rem}.avatar-module__fallbackBorder___UCUbz{border:1px solid var(--color-primary-white)}.avatar-module__initials___dUWaf{margin:0;user-select:none}.avatar-module__initials--x-small___IjWaY{font-size:var(--fontsize-body-xsmall);font-weight:600}.avatar-module__initials--small___R-w3j{font-size:var(--fontsize-body-small);font-weight:600}.avatar-module__initials--large___jocha{font-size:var(--fontsize-h6)}.avatar-module__initials--bold___JXaq0{font-weight:600}@media (min-width:769px){.avatar-module__fallback___sXkqT{height:4rem;width:4rem}.avatar-module__fallback--chip___Ix5MA{height:1.5rem;width:1.5rem}.avatar-module__fallback--small___Bn-Rc{height:2rem;width:2rem}.avatar-module__fallback--small-medium___xNJIF{font-size:1.25rem;height:2.5rem;width:2.5rem}.avatar-module__fallback--medium-small___yHhIC{height:3.5rem;width:3.5rem}}"),(Ml=exports.ThemeColor||(exports.ThemeColor={})).PrimaryRed="primaryRed",Ml.PrimaryCharcoal="primaryCharcoal",Ml.PrimaryPaleCharcoal="primaryPaleCharcoal",Ml.PrimaryLightCharcoal="primaryLightCharcoal",Ml.PrimarySlate="primarySlate",Ml.SecondaryBurgundy="secondaryBurgundy",Ml.SecondaryCobalt="secondaryCobalt",Ml.SecondaryEmerald="secondaryEmerald",Ml.SecondaryOcean="secondaryOcean",Ml.SecondaryGold="secondaryGold",Ml.SecondaryJade="secondaryJade",Ml.BgWhite="bgWhite",Ml.BgPrimaryCharcoal="bgPrimaryCharcoal",Ml.BgPrimaryPaleCharcoal="bgPrimaryPaleCharcoal",Ml.BgGrey="bgGrey",Ml.BgLightGrey="bgLightGrey",Ml.BgLightCharcoal="bgLightCharcoal",Ml.BgInactiveCharcoal="bgInactiveCharcoal";var Hl=((jl={})[exports.ThemeColor.PrimaryRed]="--color-primary-red",jl[exports.ThemeColor.PrimaryCharcoal]="--color-primary-charcoal",jl[exports.ThemeColor.PrimaryPaleCharcoal]="--color-primary-pale-charcoal",jl[exports.ThemeColor.PrimaryLightCharcoal]="--color-primary-light-charcoal",jl[exports.ThemeColor.PrimarySlate]="--color-primary-slate",jl[exports.ThemeColor.SecondaryBurgundy]="--color-secondary-burgundy",jl[exports.ThemeColor.SecondaryCobalt]="--color-secondary-cobalt",jl[exports.ThemeColor.SecondaryEmerald]="--color-secondary-emerald",jl[exports.ThemeColor.SecondaryOcean]="--color-secondary-ocean",jl[exports.ThemeColor.SecondaryGold]="--color-secondary-gold",jl[exports.ThemeColor.SecondaryJade]="--color-secondary-jade",jl[exports.ThemeColor.BgWhite]="--color-bg-white",jl[exports.ThemeColor.BgPrimaryCharcoal]="--color-bg-primary-charcoal",jl[exports.ThemeColor.BgPrimaryPaleCharcoal]="--color-bg-primary-pale-charcoal",jl[exports.ThemeColor.BgGrey]="--color-bg-grey",jl[exports.ThemeColor.BgLightGrey]="--color-bg-light-grey",jl[exports.ThemeColor.BgLightCharcoal]="--color-bg-light-charcoal",jl[exports.ThemeColor.BgInactiveCharcoal]="--color-bg-inactive-charcoal",jl),Rl=function(e){return t.useMemo((function(){return e?Object.hasOwnProperty.call(Hl,e)?getComputedStyle(document.documentElement).getPropertyValue(Hl[e]).trim():e:null}),[e])},Vl=function(e){var t,o,n,l=e.imageUrl,i=e.initials,c=e.fgColor,s=e.avatarSize,_=void 0===s?"medium":s,m=e.fontSize,u=void 0===m?"medium":m,p=e.fontBold,g=void 0!==p&&p,h=e.type,f=e.iconName,b=e.useBorder,v=void 0!==b&&b,w=e.dataTestId,y=void 0===w?"uikit-avatar":w,x=e.role,E=e.ariaLabel,C=void 0===E?"":E,k=e.ariaDescribedBy,T=e.ariaLabelledBy,I=e.tabIndex,z=e.clickable,N=void 0!==z&&z,S=e.onClick,B=void 0===S?function(){}:S,L=Rl(e.bgColor),O=Rl(c);return a.createElement(d.Avatar.Root,{className:r((t={},t[Pl.clickable]=N,t)),"data-testid":y,role:x,"aria-label":C,"aria-describedby":k,"aria-labelledby":T,tabIndex:I},a.createElement(d.Avatar.Fallback,null,("monogram"===h||"photo"===h)&&a.createElement("div",{className:r("fallback",Pl.fallback,Pl["fallback--"+_],(o={},o[Pl.fallbackBorder]=v,o)),style:at({},null!=O&&{color:O},L&&{backgroundColor:L}),onClick:N?B:void 0},a.createElement("p",{className:r(Pl.initials,Pl["initials--"+u],(n={},n[Pl["initials--bold"]]=g,n))},i.toUpperCase())),"icon"===h&&a.createElement(nl,{iconName:null!=f?f:"info-icon",altText:null!=f?f:"icon"})),l&&a.createElement(d.Avatar.Image,{className:r(Pl.avatar,Pl["avatar--"+_]),src:l,alt:i}))},Fl=function(e){var t,o=e.text,n=e.onDismissBtnClick,l=e.onClickAction,i=e.type,d=e.iconName,s=e.dataTestId,_=void 0===s?"uikit-chip":s,m=e.role,u=e.ariaLabel,p=e.ariaDescribedBy,g=e.ariaLabelledBy,h=e.tabIndex,f=e.iconHeight,b=void 0===f?16:f,v=e.iconWidth,w=void 0===v?16:v,y=e.textOnly,x=void 0!==y&&y,E=e.id,C=e.leftElement,k=c.useTranslation("dropdown").t,T="avatar--"+i,I="text--"+i;return a.createElement(l?"button":"span",{onClick:l,"data-testid":_,role:m,"aria-label":u,"aria-describedby":p,"aria-labelledby":g,tabIndex:h,className:Dl.buttonReset,id:E},a.createElement("div",{className:r(Dl.chip,(t={},t[Dl["chip--"+i]]=i&&i!==exports.ChipType.DEFAULT,t[Dl.chip]=!i||i===exports.ChipType.DEFAULT,t[Dl.textOnly]=x,t[Dl.interactive]=l,t))},!x&&a.createElement("div",{className:i&&i!==exports.ChipType.DEFAULT?Dl[T]:Dl.avatar},C||(i!==exports.ChipType.DEFAULT&&i||d||!o?i!==exports.ChipType.STATUS&&a.createElement(nl,{iconName:d,altText:d,width:w,height:b,color:i==exports.ChipType.ERROR?"#98002E":void 0}):a.createElement(Vl,{initials:o.slice(0,1),avatarSize:"chip",fontSize:"x-small",type:"monogram"}))),a.createElement("div",{className:i&&i!=exports.ChipType.DEFAULT?Dl[I]:Dl.text},o),n&&a.createElement("button",{className:Dl.dismissButton,onClick:n,"aria-label":k("remove",{option:o})},a.createElement(nl,{iconName:"dismiss-icon"}))))},Wl={accordion:"accordion-module__accordion___Na9ur",accordionHeader:"accordion-module__accordionHeader___r3t6m",title:"accordion-module__title___F-2Fk",subtitle:"accordion-module__subtitle___xWoSl",chevronWrapper:"accordion-module__chevronWrapper___8NNVf"};S(".accordion-module__accordion___Na9ur{display:flex;flex-direction:column;min-width:22rem;width:100%;.accordion-module__accordionHeader___r3t6m{align-items:center;border-bottom:1px solid var(--color-primary-pale-charcoal);border-radius:0;border-top:1px solid var(--color-primary-pale-charcoal);cursor:pointer;display:flex;flex-direction:row;gap:1rem;padding:1rem;.accordion-module__title___F-2Fk{color:var(--color-primary-charcoal);font-size:var(--fontsize-body-small);font-weight:600;line-height:var(--lineheight-body)}.accordion-module__subtitle___xWoSl{color:var(--color-primary-charcoal);font-size:.875rem}}.accordion-module__accordionHeader___r3t6m:hover{background-color:#f5f5f5}.accordion-module__chevronWrapper___8NNVf{svg{path{fill:var(--color-primary-slate)}}}[data-scope=accordion][data-part=chevron][data-state=open]{transform:rotate(90deg);transition:transform .1s ease-in-out}[data-scope=accordion][data-part=chevron][data-state=closed]{transition:transform .1s ease-in-out}[data-scope=accordion][data-part=content]{background-color:var(--color-bg-white);border-bottom:1px solid var(--color-primary-pale-charcoal);border-top:none;padding:1rem 1rem 1rem 3rem}[data-scope=accordion][data-part=content][data-state=open]{align-items:flex-start;align-self:stretch;display:flex;flex-direction:column;gap:1rem;justify-content:center;opacity:1;transition:all .1s ease-in-out;visibility:visible}[data-scope=accordion][data-part=content][data-state=closed]{height:0;opacity:0;padding:0 1rem 0 3rem;transition:all .1s ease-in-out;visibility:hidden}}"),S(".assigneeTooltip-module__tooltip___NxT7N{background-color:var(--color-primary-charcoal);border-radius:3px;color:var(--color-primary-pale-charcoal);font-family:ProximaNova,Arial,sans-serif;font-size:var(--fontsize-body);line-height:var(--lineheight-body-small);max-width:235px;padding:16px;white-space:pre-line;z-index:var(--z-index-tooltip)}.assigneeTooltip-module__smallSize___Bj-qG{font-size:var(--fontsize-body-small);padding:4px 8px}.assigneeTooltip-module__wide___Tc8XE{max-width:280px}.assigneeTooltip-module__arrow___YD-EX{--arrow-size:9px;--arrow-background:var(--color-primary-charcoal)}");var Ul={"up-start":"bottom-start",up:"bottom","up-end":"bottom-end","down-start":"top-start",down:"top","down-end":"top-end",right:"left",left:"right"},ql={inputWrapper:"input-module__inputWrapper___QwJwa",inputField:"input-module__inputField___BsUEA",inputThin:"input-module__inputThin___As51m",searchTerm:"input-module__searchTerm___oOCqr",baseAlign:"input-module__baseAlign___lyZiR",centerAlign:"input-module__centerAlign___yOfNA",disappearingLabel:"input-module__disappearingLabel___-SjiJ",floatingLabel:"input-module__floatingLabel___z4Wsn",floatingLabelActive:"input-module__floatingLabelActive___KWdqs",hidden:"input-module__hidden___GaZcX",inputError:"input-module__inputError___OUDEA",errorTextContainer:"input-module__errorTextContainer___-Kvch",additionalErrorMessage:"input-module__additionalErrorMessage___Olqhp",errorText:"input-module__errorText___jT5Un",iconWrapper:"input-module__iconWrapper___OEOi8",requiredFieldsTextAsterisk:"input-module__requiredFieldsTextAsterisk___L44F-",searchIconWrapper:"input-module__searchIconWrapper___iJhnm"};S(".input-module__inputWrapper___QwJwa{margin-bottom:1rem;position:relative;width:100%}.input-module__inputField___BsUEA{background-color:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:2px;box-sizing:border-box;color:var(--color-primary-charcoal);font-family:inherit;font-size:var(--fontsize-body);height:48px;line-height:var(--lineheight-body);padding:1rem;transition:border-color .2s ease-in-out,background-color .2s ease-in-out;width:100%;&:hover{background-color:var(--color-primary-soft-charcoal)}}.input-module__inputField___BsUEA:read-only{cursor:pointer}.input-module__inputThin___As51m{height:auto;padding:.5rem}.input-module__searchTerm___oOCqr{border:1px solid var(--color-primary-charcoal)}.input-module__inputField___BsUEA:disabled{background-color:var(--color-bg-light-grey);border-color:var(--color-border-pale-grey);color:var(--color-bg-inactive-charcoal);cursor:not-allowed}.input-module__baseAlign___lyZiR{padding:2.25rem 1rem 1rem}.input-module__centerAlign___yOfNA{padding:1.625rem 1rem}.input-module__inputField___BsUEA:focus{border:1px solid var(--color-primary-charcoal);outline:none;&.focusedByKeyboard{border:1px solid var(--color-border-pale-grey);outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}}.input-module__disappearingLabel___-SjiJ,.input-module__floatingLabel___z4Wsn{color:var(--color-bg-inactive-charcoal);font-size:var(--fontsize-body);left:1rem;line-height:var(--lineheight-body);pointer-events:none;position:absolute;top:1rem;z-index:1}.input-module__floatingLabel___z4Wsn{transition:.2s ease-in-out}.input-module__disappearingLabel___-SjiJ{opacity:1;transform:translate(15px,-10px);transition:opacity 2s linear;visibility:visible}.input-module__floatingLabelActive___KWdqs{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-xsmall);line-height:var(--lineheight-body-xsmall);top:8px}.input-module__inputWrapper___QwJwa input:not(:placeholder-shown)+.input-module__floatingLabel___z4Wsn,.input-module__inputWrapper___QwJwa:focus-within .input-module__floatingLabel___z4Wsn{font-size:var(--fontsize-body-xsmall);line-height:var(--lineheight-body-xsmall);top:8px}.input-module__hidden___GaZcX{display:none}.input-module__inputError___OUDEA{border:1px solid var(--color-primary-red)}.input-module__errorTextContainer___-Kvch{display:flex;gap:.5rem;margin-top:.75rem}.input-module__additionalErrorMessage___Olqhp,.input-module__errorTextContainer___-Kvch{color:var(--color-secondary-burgundy);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small)}.input-module__errorText___jT5Un{line-height:1;margin:0;text-align:left}.input-module__iconWrapper___OEOi8{color:var(--color-primary-slate);left:10px;position:absolute;top:16px;transition:opacity 2s linear}.input-module__requiredFieldsTextAsterisk___L44F-{color:var(--color-primary-red);font-size:var(--fontsize-body)}.input-module__searchIconWrapper___iJhnm{position:absolute;right:25px;top:14px;svg{path{fill:var(--color-primary-charcoal)}}}@media (min-width:769px){.input-module__inputWrapper___QwJwa{margin-bottom:0}}");var Kl=["placeholder","value","onChange","onValueChange","inputId","className","optionalLabel","disabled","onClick","onBlur","onKeyDown","error","errorMessage","additionalErrorMessages","floatingLabelEnabled","withLeftIcon","leftIconName","dataTestId","role","ariaLabel","ariaDescribedBy","ariaLabelledBy","tabIndex","required","maxLength","enableSearch","errorMessageWithLineBreaks","replaceLineBreaksWithBulletPoints","showClearSearchTerm","thin","leftIconPaddingLeft","showRequiredIndicator"],Gl=["defaultValue"],Yl=function(e){var o,n,l,i=e.placeholder,c=e.value,d=e.onChange,s=e.onValueChange,_=e.inputId,m=e.className,u=e.optionalLabel,p=e.disabled,g=void 0!==p&&p,h=e.onClick,f=void 0===h?function(){}:h,b=e.onBlur,v=e.onKeyDown,w=e.error,y=void 0!==w&&w,x=e.errorMessage,E=void 0===x?"Invalid text":x,C=e.additionalErrorMessages,k=void 0===C?[]:C,T=e.floatingLabelEnabled,I=void 0===T||T,z=e.withLeftIcon,N=e.leftIconName,S=e.dataTestId,B=void 0===S?"uikit-input":S,L=e.role,O=e.ariaLabel,A=void 0===O?"":O,D=e.ariaDescribedBy,j=e.ariaLabelledBy,M=e.tabIndex,P=e.required,H=e.maxLength,R=void 0===H?250:H,V=e.enableSearch,F=void 0===V||V,W=e.errorMessageWithLineBreaks,U=void 0!==W&&W,q=e.replaceLineBreaksWithBulletPoints,K=void 0!==q&&q,G=e.showClearSearchTerm,Y=void 0!==G&&G,J=e.thin,Z=void 0!==J&&J,X=e.leftIconPaddingLeft,Q=void 0===X?"2rem":X,$=e.showRequiredIndicator,ee=void 0!==$&&$,te=rt(e,Kl),ae=t.useState(""),re=ae[0],oe=ae[1],ne=t.useState(!1),le=ne[0],ie=ne[1],ce=gl().focusClass,de=rt(te,Gl),se=void 0!==c?c:re,_e=[D,y&&_+"-error-message"].filter(Boolean).join(" ");return a.createElement("div",{className:ql.inputWrapper,"data-testid":B,tabIndex:M},z&&a.createElement("div",{className:r("input-left-icon",ql.iconWrapper),"data-forspacing":"forSearchIcon"},a.createElement(nl,{iconName:N,altText:N,size:16})),a.createElement("input",Object.assign({disabled:g,id:_,className:r(ql.inputField,ce,(o={},o[ql.inputThin]=Z,o[ql.inputError]=y,o[ql.searchTerm]=c&&Y,o),m),style:at({},I&&{paddingTop:"2.25rem"},z&&{paddingLeft:Q}),placeholder:i&&!I?i:" ",value:se,onChange:function(e){var t=e.target.value;d||s?(null==d||d(e),null==s||s(t)):oe(t)},onFocus:function(){return ie(!0)},onBlur:function(e){null==b||b(),""===e.target.value&&ie(!1)},onClick:f,onKeyDown:v,maxLength:R,autoComplete:"off",readOnly:!F,required:null!=P?P:void 0,"aria-invalid":null!=y?y:void 0,role:L,"aria-label":A,"aria-labelledby":j},de,{"aria-describedby":_e})),I&&a.createElement("label",{className:r(ql.floatingLabel,(n={},n[ql.floatingLabelActive]=le||se,n)),htmlFor:null!=(l=de.id)?l:_},i," ",ee&&a.createElement("span",{className:ql.requiredFieldsTextAsterisk},"*"),u&&a.createElement("span",null,"- ",u)),y&&E&&a.createElement("div",{className:ql.errorContainer,id:_+"-error-message"},a.createElement("div",{className:ql.errorTextContainer},a.createElement(nl,{iconName:"warning-icon"}),a.createElement("p",{className:ql.errorText},U&&K?E.split("\n").map((function(e,t){return a.createElement(a.Fragment,{key:t},0===t&&a.createElement("span",{style:{fontSize:"14px"}},e),0!==t&&a.createElement("li",{style:{paddingLeft:"10px",paddingTop:"8px",fontSize:"14px"}},a.createElement("span",{style:{position:"relative",left:"-5px"}},e)))})):E)),k.length>0&&a.createElement("ul",{className:ql.additionalErrorMessage},k.map((function(e,t){return a.createElement("li",{key:t},e)})))),Y&&a.createElement("div",{className:ql.searchIconWrapper,onClick:function(){null==s||s("")}},a.createElement(nl,{iconName:"close-icon",altText:"remove search term",size:12})))};S(".autocomplete-module__autoCompleteWrapper___QFJ8Y{position:relative;width:90%}.autocomplete-module__autoCompleteInputWrapper___nqwaY{position:relative;width:100%}.autocomplete-module__autoCompleteInput___MX0lu{background-color:var(--color-bg-white);border:1px solid var(--color-border-grey);border-radius:2px;box-sizing:border-box;color:var(--color-primary-charcoal);font-size:var(--fontsize-body);height:48px;line-height:var(--lineheight-body);padding:2.25rem 1rem 1rem;transition:border-color .2s ease-in-out;width:100%}.autocomplete-module__autoCompleteInput___MX0lu:focus-visible{border:1px solid var(--color-primary-charcoal);outline:none}.autocomplete-module__floatingLabel___q-frp{color:var(--color-bg-inactive-charcoal);font-size:var(--fontsize-body);left:1rem;line-height:var(--lineheight-body);pointer-events:none;position:absolute;top:50%;transform:translateY(-50%);transition:.2s ease-in-out;z-index:1}.autocomplete-module__floatingLabelActive___dEPCh{color:var(--color-primary-charcoal);font-size:var(--fontsize-body-xsmall);line-height:var(--lineheight-body-xsmall);top:15px}.autocomplete-module__autoCompleteInputWrapper___nqwaY input:not(:placeholder-shown)+.autocomplete-module__floatingLabel___q-frp,.autocomplete-module__autoCompleteInputWrapper___nqwaY:focus-within .autocomplete-module__floatingLabel___q-frp{color:var(--color-primary-charcoal);font-size:var(--fontsize-body-xsmall);line-height:var(--lineheight-body-xsmall);top:15px}.autocomplete-module__autoCompleteDropdown___fcmlR{align-items:flex-start;background-color:var(--color-bg-white);border:1px solid var(--color-border-grey);border-radius:2px;box-sizing:border-box;color:#000;display:flex;flex-direction:column;gap:.5rem;max-width:100%;padding:1rem;width:100%}.autocomplete-module__checkMark___1iTW3{display:flex;position:absolute;right:10px;top:50%;transform:translateY(-50%)}");var Jl={avatarCard:"avatarCard-module__avatarCard___z1N8s",name:"avatarCard-module__name___kwQUM",cardInfo:"avatarCard-module__cardInfo___MBtbd",cardInfoItem:"avatarCard-module__cardInfoItem___zYxGN"};S(".avatarCard-module__avatarCard___z1N8s{align-items:center;background-color:var(--color-bg-white);border-radius:8px;color:var(--color-primary-charcoal);display:flex;gap:32px;max-height:100px;max-width:574px;width:100%}.avatarCard-module__name___kwQUM{font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body);margin:0 0 .5rem}.avatarCard-module__cardInfo___MBtbd p{font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small);margin:0}.avatarCard-module__cardInfoItem___zYxGN{align-items:center;display:flex;gap:.5rem;padding-bottom:.25rem}@media (min-width:1025px){.avatarCard-module__avatarCard___z1N8s{box-shadow:0 4px 24px 0 #00000026;max-height:136px;padding:1.5rem}.avatarCard-module__name___kwQUM{margin-bottom:.5rem}}");var Zl={buttonDropdownWrapper:"buttonDropdown-module__buttonDropdownWrapper___mZIKF",tertiaryTrigger:"buttonDropdown-module__tertiaryTrigger___uED9A",primaryTrigger:"buttonDropdown-module__primaryTrigger___nd6kn",charcoalIconItem:"buttonDropdown-module__charcoalIconItem___THHg4"};S(".buttonDropdown-module__buttonDropdownWrapper___mZIKF{position:relative;z-index:var(--z-index-btn-dropdown);[data-scope=menu][data-part=trigger]{align-items:center;background-color:var(--color-primary-soft-charcoal);border:1px solid var(--color-primary-charcoal);border-radius:2px;color:var(--color-primary-charcoal);display:flex;flex-direction:row;font-weight:600;line-height:var(--lineheight-body);outline:none;padding:.5rem 1rem;transition:background-color .2s;svg{path{fill:var(--color-primary-charcoal)}}}[data-scope=menu][data-part=trigger]:hover{background-color:var(--color-primary-charcoal);color:var(--color-bg-white);cursor:pointer;svg{path{fill:var(--color-bg-white)}}}[data-scope=menu][data-part=trigger]:focus-visible{border:1px solid var(--color-primary-charcoal);margin:0;outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}[data-scope=menu][data-part=trigger].buttonDropdown-module__tertiaryTrigger___uED9A{background-color:var(--color-primary-soft-charcoal);border:1px solid var(--color-primary-charcoal);color:var(--color-primary-charcoal);svg path{fill:var(--color-primary-charcoal)}}[data-scope=menu][data-part=trigger].buttonDropdown-module__tertiaryTrigger___uED9A:hover{background-color:var(--color-primary-charcoal);color:var(--color-bg-white);svg path{fill:var(--color-bg-white)}}[data-scope=menu][data-part=trigger].buttonDropdown-module__tertiaryTrigger___uED9A:focus-visible{border:1px solid var(--color-primary-charcoal);margin:0;outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}[data-scope=menu][data-part=trigger].buttonDropdown-module__primaryTrigger___nd6kn{background-color:var(--color-primary-red);border:1px solid var(--color-primary-red);color:var(--color-bg-white);svg path{fill:var(--color-bg-white)}}[data-scope=menu][data-part=trigger].buttonDropdown-module__primaryTrigger___nd6kn:hover{background-color:var(--color-secondary-burgundy);border:1px solid var(--color-secondary-burgundy);color:var(--color-bg-white);svg path{fill:var(--color-bg-white)}}[data-scope=menu][data-part=trigger].buttonDropdown-module__primaryTrigger___nd6kn:focus-visible{border:1px solid var(--color-secondary-burgundy);margin:0;outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}[data-scope=menu][data-part=content]{border:0;padding:0;right:0;top:calc(100% + .25rem);width:15rem}[data-scope=menu][data-part=item]{text-wrap-mode:nowrap;align-items:center;color:var(--color-primary-charcoal);cursor:pointer;display:flex;flex-direction:row;gap:1rem;height:auto;justify-content:start;padding:1rem;transition:background-color .3s linear;width:auto;svg{path{fill:var(--color-bg-white)}}}.buttonDropdown-module__charcoalIconItem___THHg4{svg{path{fill:var(--color-primary-charcoal)!important}}}[data-scope=menu][data-part=item]:hover{background-color:var(--color-primary-pale-charcoal)}}");var Xl={taskType:"taskType-module__taskType___EbV6J",burgundy:"taskType-module__burgundy___zs7G-",emerald:"taskType-module__emerald___Onlnd",ocean:"taskType-module__ocean___tT9LA",gold:"taskType-module__gold___BE0zn",jade:"taskType-module__jade___xnVx2"};S(".taskType-module__taskType___EbV6J{align-items:center;border-radius:2px;color:var(--color-primary-white);display:grid;height:1.25rem;justify-content:center;min-width:1.25rem;width:1.25rem}.taskType-module__burgundy___zs7G-{background-color:var(--color-secondary-burgundy)}.taskType-module__emerald___Onlnd{background-color:var(--color-secondary-emerald)}.taskType-module__ocean___tT9LA{background-color:var(--color-secondary-ocean)}.taskType-module__gold___BE0zn{background-color:var(--color-secondary-gold)}.taskType-module__jade___xnVx2{background-color:var(--color-secondary-jade)}");var Ql=function(e){var t=e.iconName,o=e.dataTestId,n=void 0===o?"uikit-taskType":o,l=e.role,i=e.ariaLabel,c=e.ariaDescribedBy,d=e.ariaLabelledBy,s=e.tabIndex,_=e.width,m=void 0===_?16:_,u=e.height,p=void 0===u?16:u;return a.createElement("div",{className:r(Xl.taskType,Xl[e.backgroundColor]),"data-testid":n,role:l,"aria-label":i,"aria-describedby":c,"aria-labelledby":d,tabIndex:s},a.createElement(nl,{iconName:t,width:m,height:p}))};S('.selector-module__selector___PBMIP{display:flex;flex-direction:column}.selector-module__selectorWrapperRegularRadio___DQVqz,.selector-module__selectorWrapper___mZCi5{background-color:var(--color-bg-white);width:100%}.selector-module__selectorWrapper___mZCi5{border:1px solid var(--color-border-grey);border-radius:2px;min-height:104px;padding:1rem}.selector-module__selectorWrapperRegularRadio___DQVqz{padding-bottom:1.5rem;padding-right:1rem}.selector-module__selectorWrapperRegularRadio___DQVqz:last-child{padding-bottom:0}.selector-module__selectorWrapper___mZCi5:focus-within{&.focusedByKeyboard{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}}.selector-module__selectorWrapperActive___r3W2M{border-color:var(--color-border-primary-charcoal)}.selector-module__selectorSelect___I3o52{display:flex;flex-direction:column;height:100%;justify-content:center}.selector-module__selectorLabel___AoHR1{display:none}.selector-module__selectorItem___WfmuZ{align-items:center;cursor:pointer;display:flex;gap:1rem;height:100%;justify-content:center;&:hover{.selector-module__selectorInput___4OQNY{border-color:var(--color-primary-charcoal)}}}.selector-module__selectorControlWrapper___la-EU{display:flex;justify-content:center;width:1.5rem}.selector-module__selectorTextRegularRadio___EZI7K,.selector-module__selectorText___hYjll{color:var(--color-primary-charcoal);display:flex;flex:2;gap:.5rem;height:100%}.selector-module__selectorText___hYjll{align-items:flex-start;flex-direction:column}.selector-module__selectorTextRegularRadio___EZI7K{align-items:center}.selector-module__title___kYBGF{font-size:var(--fontsize-body);line-height:var(--lineheight-body-small);margin:0}.selector-module__description___ZHQAP{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small);margin:0;text-align:left}.selector-module__selectorWrapperActive___r3W2M .selector-module__selectorText___hYjll .selector-module__title___kYBGF{font-weight:600}.selector-module__selectorInput___4OQNY{border:1px solid var(--color-border-grey);border-radius:50%;height:1.5rem;position:relative;transition:outline-offset .3s ease,outline-width .3s ease,height .3s ease,width .3s ease;width:1.5rem;&.focusedByKeyboard{&[data-focus]{&:before{border-radius:50%;bottom:0;content:"";left:0;outline:2px solid var(--color-secondary-cobalt);outline-offset:1px;position:absolute;right:0;top:0}}}}.selector-module__selectorInput___4OQNY:is(:checked,[data-checked],[aria-checked=true],[data-state=checked]){background:var(--color-border-primary-charcoal);border-color:var(--color-border-primary-charcoal);height:.75rem;outline-color:var(--color-border-primary-charcoal);outline-offset:3px;outline-style:solid;outline-width:2px;width:.75rem;&[data-focus]{&:before{outline-offset:.5rem}}}@media (min-width:769px){.selector-module__selectorWrapper___mZCi5{flex-basis:calc(50% - 10px);max-width:calc(50% - 10px)}}');var $l=function(e){var t,o=e.title,n=e.description,l=e.isActiveSelector,i=e.onSelect,c=e.dataTestId,s=void 0===c?"uikit-selector":c,_=e.role,m=e.ariaLabel,u=e.ariaDescribedBy,p=e.ariaLabelledBy,g=e.tabIndex,h=e.regularRadioInput,f=void 0!==h&&h,b=gl().focusClass;return a.createElement("div",{className:r("selector-wrapper",(t={},t["selector-module__selectorWrapperRegularRadio___DQVqz"]=f,t["selector-module__selectorWrapper___mZCi5"]=!f,t["selector-module__selectorWrapperActive___r3W2M"]=!f&&l,t)),"data-testid":s,role:_,"aria-describedby":u,"aria-labelledby":p,tabIndex:g},a.createElement("div",{className:"selector-module__selectorSelect___I3o52"},a.createElement("div",{className:"selector-module__selectorLabel___AoHR1"},a.createElement(d.RadioGroup.Label,null,o)),a.createElement(d.RadioGroup.Item,{"aria-label":m,className:"selector-module__selectorItem___WfmuZ",value:o,onClick:function(){null==i||i()}},a.createElement("div",{className:"selector-module__selectorControlWrapper___la-EU"},a.createElement(d.RadioGroup.ItemControl,{className:r("selector-module__selectorInput___4OQNY",b),"data-state":l?"checked":"unchecked"})),a.createElement(d.RadioGroup.ItemHiddenInput,null),a.createElement("div",{className:f?"selector-module__selectorTextRegularRadio___EZI7K":"selector-module__selectorText___hYjll"},a.createElement("p",{className:"selector-module__title___kYBGF"},o),a.createElement("p",{className:"selector-module__description___ZHQAP"},n)))))},ei={cardSelectorRegularRadio:"cardSelector-module__cardSelectorRegularRadio___UtDFG",cardSelector:"cardSelector-module__cardSelector___NsCZE"};S(".cardSelector-module__cardSelectorRegularRadio___UtDFG,.cardSelector-module__cardSelector___NsCZE{border-radius:0;display:flex;flex-wrap:wrap;width:100%}.cardSelector-module__cardSelector___NsCZE{gap:1.25rem}"),S(".carousel-module__carousel___ealh-{display:flex;flex-direction:column;gap:.5rem;height:100%;justify-content:flex-end;position:relative;width:100%}.carousel-module__carousel-viewport___JD9Sy{position:relative}.carousel-module__carousel-img___rba07{border-bottom-left-radius:.5rem;border-top-left-radius:.5rem;max-height:550px;object-fit:contain;opacity:0;overflow:hidden;position:relative;transition:opacity .5s ease-in-out,transform .25s ease-in-out;width:100%}.carousel-module__carousel-item-active___y0w-U .carousel-module__carousel-img___rba07{opacity:1;transform:none}.carousel-module__carousel-controls___pdqjL{align-items:center;display:flex;flex-direction:column;flex-wrap:wrap;justify-content:flex-end;margin-top:2rem;padding-right:1rem;position:relative;z-index:2}.carousel-module__carousel-buttons___W5ILr{display:flex;gap:1rem;margin-top:1rem}.carousel-module__carouselButton___gw5uI{background-color:transparent;border:none;color:var(--color-bg-white);cursor:pointer;outline:none;padding:10px}.carousel-module__carouselButtonControlColors___cp7k6{background-color:var(--color-bg-primary-charcoal);border-radius:50%}.carousel-module__carouselButton___gw5uI:hover{background-color:rgba(0,0,0,.1);border-radius:50%}.carousel-module__carouselButtonControlColors___cp7k6:hover{background-color:var(--color-bg-primary-charcoal);border-radius:50%}.carousel-module__carouselButton___gw5uI:focus-visible{box-shadow:none;outline:none}.carousel-module__pausePlayButton___qVisS{background-color:transparent;display:flex;justify-content:center;z-index:2}.carousel-module__pausePlayButton___qVisS button{background-color:transparent;border:none;cursor:pointer;outline:none;padding:10px}.carousel-module__pausePlayButton___qVisS button:hover{background-color:rgba(0,0,0,.1);border-radius:50%}.carousel-module__pausePlayButton___qVisS button:focus-visible{box-shadow:none;outline:none}.carousel-module__carouselButton___gw5uI:focus-visible,.carousel-module__pausePlayButtonControlColors___otr-9 button,.carousel-module__pausePlayButtonControlColors___otr-9:hover,.carousel-module__pausePlayButton___qVisS button:focus-visible{background-color:var(--color-bg-primary-charcoal);border-radius:50%}.carousel-module__carousel-item-group___2tOEr{display:flex;height:100%;transition:transform .5s ease-in-out}.carousel-module__carousel-item___fnwSS{flex:0 0 100%;opacity:0;position:relative;transition:opacity .25s ease-in-out,transform .25s ease-in-out}.carousel-module__carousel-item-active___y0w-U{opacity:1;transform:none}.carousel-module__carousel-text___R-srU{color:var(--color-bg-white);flex-shrink:1;font-size:var(--fontsize-body-small);justify-self:flex-start;line-height:var(--lineheight-body-small);width:65%}@media (min-width:769px){.carousel-module__carousel-img___rba07{max-height:800px}.carousel-module__carousel-controls___pdqjL{flex-direction:row;margin-top:0}.carousel-module__carousel-buttons___W5ILr{margin-top:0}}");var ti,ai={checkboxWrapper:"checkbox-module__checkboxWrapper___2fFXa",checkbox:"checkbox-module__checkbox___FLuy1",large:"checkbox-module__large___EpVxK",small:"checkbox-module__small___K75Km",disabled:"checkbox-module__disabled___zhmLu",checkmark:"checkbox-module__checkmark___Gafvb",checkboxLabel:"checkbox-module__checkboxLabel___lyYL5"};S(".checkbox-module__checkboxWrapper___2fFXa{align-items:center;display:flex;flex-direction:row;gap:.75rem;justify-content:center;position:relative;&:focus-within{outline:none;&.focusedByKeyboard{.checkbox-module__checkbox___FLuy1{border-radius:2px;outline:2px solid var(--color-secondary-cobalt);outline-offset:-2px}}}}.checkbox-module__checkbox___FLuy1{align-items:center;border-radius:4px;cursor:pointer;display:flex;flex-shrink:0;justify-content:center}.checkbox-module__checkbox___FLuy1.checkbox-module__large___EpVxK{height:1.5rem;width:1.5rem}.checkbox-module__checkbox___FLuy1.checkbox-module__small___K75Km{height:1.25rem;width:1.25rem}.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu{background-color:var(--color-border-light-grey)!important}.checkbox-module__checkbox___FLuy1[data-state=unchecked]{background-color:var(--color-bg-white);border:1.5px solid var(--color-border-grey);margin:0;transition:background-color .1s linear;svg{display:none}}.checkbox-module__checkbox___FLuy1[data-state=unchecked]:focus-visible{border:1.5px solid var(--color-secondary-cobalt)}.checkbox-module__checkbox___FLuy1[data-state=checked]{background-color:var(--color-primary-charcoal);border:1.5px solid var(--color-primary-charcoal);transition:background-color .1s linear}.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu[data-state=checked]{background-color:var(--color-border-grey)!important;border:1.5px solid var(--color-border-grey)}.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu[data-state=checked]:hover,.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu[data-state=unchecked]:hover{border:1.5px solid var(--color-border-grey)!important}.checkbox-module__checkbox___FLuy1:hover{border:1.5px solid var(--color-primary-charcoal)!important}.checkbox-module__checkmark___Gafvb{align-items:center;cursor:pointer;display:flex;height:24px;justify-content:center;width:24px}.checkbox-module__checkboxLabel___lyYL5{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);font-weight:400;line-height:var(--lineheight-body)}.checkbox-module__checkboxLabel___lyYL5.checkbox-module__disabled___zhmLu{color:var(--color-bg-inactive-charcoal)}@media (min-width:768px){.checkbox-module__checkboxLabel___lyYL5{font-size:var(--fontsize-default)}}"),function(e){e.large="large",e.small="small"}(ti||(ti={}));var ri=function(e){var o=e.value,n=void 0!==o&&o,l=e.className,i=void 0===l?"":l,c=e.onChange,s=void 0===c?function(){}:c,_=e.disabled,m=void 0!==_&&_,u=e.id,p=e.label,g=void 0===p?"":p,h=e.customLabel,f=e.size,b=void 0===f?ti.large:f,v=e.dataTestId,w=void 0===v?"uikit-checkbox":v,y=e.role,x=e.ariaLabel,E=void 0===x?"":x,C=e.ariaDescribedBy,k=e.ariaLabelledBy,T=e.tabIndex,I=t.useState(n),z=I[0],N=I[1],S=gl().focusClass;t.useEffect((function(){N(n)}),[n]);var B=function(){var e=!z;N(e),s(e)};return a.createElement(d.Checkbox.Root,{id:u,className:r(ai.checkboxWrapper,i,S),checked:z,disabled:m,onCheckedChange:m?function(){}:B,onKeyDown:function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),m||B())},"data-testid":w,role:y,"aria-label":E,"aria-describedby":C,"aria-labelledby":k,tabIndex:T},a.createElement(d.Checkbox.Control,{className:ai.checkbox+" "+ai[b]+" "+(m?ai.disabled:"")},a.createElement(d.Checkbox.Indicator,{className:ai.checkmark+" "+(m?ai.disabled:"")},a.createElement(nl,{iconName:"checkbox-checkmark-icon",altText:"checkmark"}))),a.createElement(d.Checkbox.HiddenInput,null),h&&a.createElement(d.Checkbox.Label,null,h),g.length>0&&a.createElement(d.Checkbox.Label,{className:ai.checkboxLabel+" "+(m?ai.disabled:""),"data-checked":z},g))};S('.checkboxCard-module__checkboxCard___6-MvT{align-items:center;background-color:var(--color-bg-white);border:1px solid var(--color-border-grey);border-radius:2px;color:var(--color-primary-charcoal);display:flex;flex-direction:row;justify-content:space-between;padding:1rem;position:relative;width:100%;.checkboxCard-module__labelWrapper___6Ed27{align-items:center;display:flex;flex-direction:row;margin-right:1rem;svg{margin-right:1rem}}.checkboxCard-module__checkbox___KZ8ke{width:1.5rem}[data-scope=checkbox][data-part=control]:hover{border:1.5px solid var(--color-border-grey)}[data-scope=checkbox][data-part=label]{display:none}&:focus-within{outline:3px solid var(--color-secondary-cobalt)}}.checkboxCard-module__checkboxCard___6-MvT[data-state=unchecked]:focus-visible:before{border:1.5px solid var(--color-secondary-cobalt);content:"";display:block;height:100%;left:-1.5px;position:absolute;top:-1.5px;width:100%}.checkboxCard-module__checkboxCard___6-MvT[data-state=unchecked]:focus-visible{border:3px solid var(--color-outline-dark-bg)}.checkboxCard-module__checkboxCard___6-MvT:hover,.checkboxCard-module__checkboxCard___6-MvT[data-scope=checkbox-card][data-state=checked],[data-scope=checkbox][data-part=control][data-state=checked]:hover{border:1px solid var(--color-primary-charcoal)}.checkboxCard-module__checkboxCard___6-MvT[data-scope=checkbox-card][data-state=checked][data-disabled=active]{.checkboxCard-module__labelWrapper___6Ed27{font-weight:700}}.checkboxCard-module__checkboxCard___6-MvT[data-scope=checkbox-card][data-disabled=disabled]{background:var(--color-bg-light-grey);border:1px solid var(--color-border-grey);color:var(--color-bg-inactive-charcoal);[data-part=indicator]:hover{cursor:not-allowed}.checkboxCard-module__labelWrapper___6Ed27{svg{path{fill:var(--color-bg-inactive-charcoal)}}}}');var oi,ni={chipDropdown:"chipDropdown-module__chipDropdown___9z3S-",trigger:"chipDropdown-module__trigger___mbdgb",chipDownLabel:"chipDropdown-module__chipDownLabel___iz9II",chipDownLabelDivider:"chipDropdown-module__chipDownLabelDivider___vqDBb",triggerChevronWrapper:"chipDropdown-module__triggerChevronWrapper___H7EmK",triggerIconWrapper:"chipDropdown-module__triggerIconWrapper___1vlGN",searchBarWrapper:"chipDropdown-module__searchBarWrapper___fUdsF",searchBar:"chipDropdown-module__searchBar___N3J1B",searchBarIconWrapper:"chipDropdown-module__searchBarIconWrapper___cgQ1h",resetFilter:"chipDropdown-module__resetFilter___Zim5w",dropDownItemsContainer:"chipDropdown-module__dropDownItemsContainer___x3AZN"};S(".chipDropdown-module__chipDropdown___9z3S-{position:relative;z-index:var(--z-index-dropdown-list);.chipDropdown-module__trigger___mbdgb{align-items:center;background:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:6.25rem;color:var(--color-primary-light-charcoal);cursor:pointer;display:flex;flex-direction:row;gap:.5rem;padding:.5rem 1rem;transition:border .1s linear;.chipDropdown-module__chipDownLabel___iz9II{align-items:center;display:flex;font-size:var(--fontsize-body-small);gap:.5rem;line-height:var(--lineheight-body-small)}.chipDropdown-module__chipDownLabelDivider___vqDBb{background-color:var(--color-primary-pale-charcoal);height:1rem;width:1px}.chipDropdown-module__triggerChevronWrapper___H7EmK,.chipDropdown-module__triggerIconWrapper___1vlGN{height:1rem}.chipDropdown-module__triggerIconWrapper___1vlGN{svg{path{fill:var(--color-primary-slate)}}}&[data-state=closed]{.chipDropdown-module__triggerChevronWrapper___H7EmK{svg{path{fill:var(--color-primary-slate)}}}}&[data-state=open]{.chipDropdown-module__triggerChevronWrapper___H7EmK{transform:rotate(180deg);svg{path{fill:var(--color-primary-charcoal)}}}}}.chipDropdown-module__trigger___mbdgb[data-state=open]{border:1px solid var(--color-primary-charcoal);outline:none;transition:border .1s linear}.chipDropdown-module__trigger___mbdgb:focus-visible{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}.chipDropdown-module__searchBarWrapper___fUdsF{align-items:flex-start;align-self:stretch;display:flex;flex-direction:column;gap:.5rem;margin:1rem 1rem 0;position:relative}.chipDropdown-module__searchBar___N3J1B{align-items:center;align-self:stretch;background:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:100px;display:flex;font-size:.875rem;gap:.25rem;padding:.75rem .75rem .75rem 2rem;position:relative;&:hover{border-color:var(--color-primary-charcoal)}&:focus-visible{border:1px solid var(--color-primary-charcoal);outline:none;&.focusedByKeyboard{outline:2px solid var(--color-outline-dark-bg)}}}.chipDropdown-module__searchBarIconWrapper___cgQ1h{color:var(--color-primary-slate);left:.5rem;pointer-events:none;position:absolute;top:50%;transform:translateY(-50%);z-index:200}.chipDropdown-module__searchBar___N3J1B::placeholder{color:var(--color-bg-inactive-charcoal);line-height:var(--lineheight-h6)}[data-scope=popover][data-part=content]{align-items:flex-start;background-color:var(--color-bg-white);border:none;border-radius:.125rem;box-shadow:0 2px 4px 0 rgba(0,0,0,.25);flex-direction:column;max-height:300px;max-width:37.5rem;min-width:17.5rem;overflow-y:auto;padding:0}.chipDropdown-module__resetFilter___Zim5w{background-color:var(--color-bg-white);border-radius:0;border-top:1px solid var(--color-primary-pale-charcoal);bottom:0;color:var(--color-bg-inactive-charcoal);cursor:pointer;padding:1rem;position:sticky;width:100%;&:focus-visible{outline-offset:-2px}}}.chipDropdown-module__trigger___mbdgb:hover{background-color:#f2f2f2;border-color:var(--color-primary-charcoal)}.chipDropdown-module__dropDownItemsContainer___x3AZN{display:flex;flex-direction:column;gap:.25rem;margin-top:.5rem;max-width:100%;label{cursor:pointer;justify-content:flex-start;width:100%}}"),(oi=exports.ChipDropdownItemType||(exports.ChipDropdownItemType={}))[oi.Radio=0]="Radio",oi[oi.Checkbox=1]="Checkbox";var li={checkboxDropdownItem:"checkboxDropdownItem-module__checkboxDropdownItem___IsTt6",avatarAndLabel:"checkboxDropdownItem-module__avatarAndLabel___oSyzf",label:"checkboxDropdownItem-module__label___GX61T"};S(".checkboxDropdownItem-module__checkboxDropdownItem___IsTt6{align-items:center;align-self:stretch;cursor:pointer;display:flex;gap:1rem;height:3rem;padding:.25rem 1rem}.checkboxDropdownItem-module__avatarAndLabel___oSyzf{align-items:center;display:flex;flex-direction:row;gap:.5rem;overflow:hidden}.checkboxDropdownItem-module__label___GX61T{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}");var ii=function(e){var t=e.label,r=e.value,o=e.onClick,n=e.avatarUrl;return a.createElement("div",{className:li.checkboxDropdownItem},a.createElement(ri,{onChange:function(){null==o||o(r)},id:e.id+"-"+r,value:e.checked,customLabel:a.createElement("div",{className:li.avatarAndLabel},e.includeAvatar&&a.createElement("div",{className:li.avatarWrapper},a.createElement(Vl,{initials:El(t),imageUrl:null!=n?n:"",avatarSize:"x-small",fontSize:"x-small",type:n?"photo":"monogram"})),a.createElement("span",{className:li.label},t))}))};S(".radioDropdownItem-module__radioDropdownItem___ra9Bk{align-items:center;align-self:stretch;display:flex;gap:1rem;height:3rem;padding:.25rem 1rem}");var ci,di=function(e){var t=e.label,r=e.value,o=e.onClick;return a.createElement("div",{className:"radioDropdownItem-module__radioDropdownItem___ra9Bk",onClick:function(){null==o||o(r)}},a.createElement(d.RadioGroup.Root,{id:e.id},a.createElement(d.RadioGroup.Item,{className:"selector-module__selectorItem___WfmuZ",value:r},a.createElement("div",{className:"selector-module__selectorControlWrapper___la-EU"},a.createElement(d.RadioGroup.ItemControl,{className:"selector-module__selectorInput___4OQNY","data-state":e.checked?"checked":"unchecked"})),a.createElement(d.RadioGroup.ItemHiddenInput,null),a.createElement("div",{className:"selector-module__selectorText___hYjll"},a.createElement("p",{className:"selector-module__title___kYBGF"},t)))))},si={circleButton:"circlebutton-module__circleButton___4tntF",circleButtonSmall:"circlebutton-module__circleButtonSmall___zIdlT",circleButtonWithBorder:"circlebutton-module__circleButtonWithBorder___HPB-E",circleButtonEnabled:"circlebutton-module__circleButtonEnabled___h4xdJ",circleButtonDisabled:"circlebutton-module__circleButtonDisabled___kSbIc"};S(".circlebutton-module__circleButton___4tntF{align-items:center;background-color:var(--color-bg-white);border-radius:50%;cursor:pointer;display:flex;height:2.5rem;justify-content:center;padding:0;transition:background-color .2s ease-in-out;width:2.5rem}.circlebutton-module__circleButtonSmall___zIdlT{height:2rem;width:2rem}.circlebutton-module__circleButtonWithBorder___HPB-E{border:1px solid var(--color-border-grey)}.circlebutton-module__circleButton___4tntF:focus-visible,.circlebutton-module__circleButton___4tntF:hover{background-color:var(--color-bg-light-grey)}.circlebutton-module__circleButtonEnabled___h4xdJ{background-color:var(--color-primary-red);svg{color:var(--color-bg-white)}}.circlebutton-module__circleButtonDisabled___kSbIc{cursor:default}.circlebutton-module__circleButtonDisabled___kSbIc:hover{background-color:var(--color-bg-white)}"),(ci=exports.CircleButtonIconSizeEnum||(exports.CircleButtonIconSizeEnum={})).small="small",ci.regular="regular",S(".tooltip-module__tooltip___3gdiI{background-color:var(--color-primary-charcoal);border-radius:3px;color:var(--color-primary-pale-charcoal);font-family:ProximaNova,Arial,sans-serif;font-size:var(--fontsize-body);line-height:var(--lineheight-body-small);max-width:235px;padding:16px;white-space:pre-line;z-index:var(--z-index-tooltip)}.tooltip-module__smallSize___pfMRI{font-size:var(--fontsize-body-small);padding:4px 8px}.tooltip-module__wide___ozcpb{max-width:280px}.tooltip-module__arrow___CBW5f{--arrow-size:9px;--arrow-background:var(--color-primary-charcoal)}.tooltip-module__closerToDrawerButton___cOwdf [data-scope=tooltip]{--y:42px!important}");var _i=function(e){var t,o=e.message,n=e.direction,l=e.dataTestId,i=e.withArrow,c=void 0===i||i,s=e.smallSize,_=void 0!==s&&s,m=e.closerToDrawerButton,u=e.wide,p=void 0!==u&&u;return a.createElement("div",{role:e.role,tabIndex:e.tabIndex,"data-testid":void 0===l?"uikit-tooltip":l,className:void 0!==m&&m?"tooltip-module__closerToDrawerButton___cOwdf":""},a.createElement(d.Tooltip.Root,{id:e.inputId,positioning:{placement:Ul[null!=n?n:"up"]},closeDelay:e.closeDelay,openDelay:e.openDelay,"aria-label":e.ariaLabel,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy,disabled:e.disabled},a.createElement(d.Tooltip.Trigger,{asChild:!0},e.children),a.createElement(d.Tooltip.Positioner,null,o&&a.createElement(d.Tooltip.Content,{className:r("tooltip-module__tooltip___3gdiI",(t={},t["tooltip-module__smallSize___pfMRI"]=_,t["tooltip-module__wide___ozcpb"]=p,t))},c&&a.createElement(d.Tooltip.Arrow,{className:"tooltip-module__arrow___CBW5f"},a.createElement(d.Tooltip.ArrowTip,null)),o))))},mi=function(e){var t,o,n=e.toolTipText,l=e.inputId,i=e.icon,c=e.onClick,d=e.ariaLabel,s=void 0===d?n:d,_=e.dataTestId,m=void 0===_?"uikit-circleButton":_,u=e.role,p=e.ariaDescribedBy,g=e.ariaLabelledBy,h=e.tabIndex,f=e.withArrow,b=void 0===f||f,v=e.smallTooltip,w=void 0!==v&&v,y=e.toolTipCloserToButton,x=void 0!==y&&y,E=e.withBorder,C=void 0===E||E,k=e.size,T=void 0===k?exports.CircleButtonIconSizeEnum.regular:k,I=e.disabled,z=void 0!==I&&I,N=e.isComment,S=void 0!==N&&N;return a.createElement("div",{className:r(si.circleButtonWrapper,e.className),"data-testid":m},n?a.createElement(_i,{message:n||"",inputId:l,withArrow:b,smallSize:w,closerToDrawerButton:x,disabled:z},a.createElement("button",{type:"button",className:r(si.circleButton,(t={},t[si.circleButtonWithBorder]=C,t[si.circleButtonSmall]=T===exports.CircleButtonIconSizeEnum.small,t[si.circleButtonEnabled]=S&&!z,t[si.circleButtonDisabled]=z,t)),onClick:c,disabled:z,role:u,"aria-label":s,"aria-describedby":p,"aria-labelledby":g,tabIndex:h},i)):a.createElement("button",{type:"button",className:r(si.circleButton,(o={},o[si.circleButtonWithBorder]=C,o[si.circleButtonSmall]=T===exports.CircleButtonIconSizeEnum.small,o[si.circleButtonDisabled]=z,o)),onClick:c,role:u,"aria-label":s,"aria-describedby":p,"aria-labelledby":g,tabIndex:h},i))};S('@font-face{font-display:swap;font-family:ProximaNova;font-style:normal;font-weight:400;src:url(/fonts/proximanova-regular-webfont.woff2) format("woff2"),url(/fonts/proximanova-regular-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:normal;font-weight:800;src:url(/fonts/proximanova-extrabold-webfont.woff2) format("woff2"),url(/fonts/proximanova-extrabold-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:normal;font-weight:700;src:url(/fonts/proximanova-bold-webfont.woff2) format("woff2"),url(/fonts/proximanova-bold-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:normal;font-weight:600;src:url(/fonts/proximanova-semibold-webfont.woff2) format("woff2"),url(/fonts/proximanova-semibold-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:normal;font-weight:300;src:url(/fonts/proximanova-light-webfont.woff2) format("woff2"),url(/fonts/proximanova-light-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:normal;font-weight:100;src:url(/fonts/proximanova-thin-webfont.woff2) format("woff2"),url(/fonts/proximanova-thin-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:italic;font-weight:100;src:url(/fonts/proximanova-thinit-webfont.woff2) format("woff2"),url(/fonts/proximanova-thinit-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:italic;font-weight:600;src:url(/fonts/proximanova-boldit-webfont.woff2) format("woff2"),url(/fonts/proximanova-boldit-webfont.woff) format("woff")}@font-face{font-display:swap;font-family:ProximaNova;font-style:italic;font-weight:400;src:url(/fonts/proximanova-regularit-webfont.woff2) format("woff2"),url(/fonts/proximanova-regularit-webfont.woff) format("woff")}'),S(':root{--fontsize-h1:2.25rem;--fontsize-h2:1.875rem;--fontsize-h3:1.625rem;--fontsize-h4:1.375rem;--fontsize-h5:1.25rem;--fontsize-h6:1.125rem;--fontsize-body:1rem;--fontsize-body-small:0.875rem;--fontsize-body-xsmall:0.75rem;--fontsize-body-badge:0.75rem;--fontsize-body-label:0.75rem;--fontsize-body-eyebrow:0.625rem;--fontsize-default:1rem;--lineheight-h1:3rem;--lineheight-h2:2.5rem;--lineheight-h3:2rem;--lineheight-h4:2rem;--lineheight-h5:1.5rem;--lineheight-h6:1.5rem;--lineheight-body:1.5rem;--lineheight-body-small:1.25rem;--lineheight-body-xsmall:1rem;--lineheight-body-badge:1rem;--lineheight-body-label:1rem;--lineheight-body-eyebrow:1.125rem;--lineheight-default:1.5rem;--color-primary-red:#e81a3b;--color-primary-red-transparent:#e81a3b14;--color-primary-red-disabled:#e81a3b4d;--color-primary-charcoal:#333;--color-primary-pale-charcoal:#e7e7e7;--color-primary-light-charcoal:#404040;--color-primary-slate:#5b6e7f;--color-primary-white:#fff;--color-primary-inactive-charcoal:#666;--color-primary-soft-charcoal:#fafafa;--color-secondary-burgundy:#98002e;--color-secondary-cobalt:#0062b8;--color-secondary-cobalt-transparent:#0062b814;--color-secondary-emerald:#218f8b;--color-secondary-ocean:#008fd2;--color-secondary-gold:#d67900;--color-secondary-gold-transparent:#d679001a;--color-secondary-jade:#096;--color-secondary-emerald-badge:#218f8b33;--color-secondary-gold-badge:#d6790033;--color-secondary-burgundy-badge:#98002e33;--color-bg-white:#fff;--color-bg-primary-charcoal:var(--color-primary-charcoal);--color-bg-primary-pale-charcoal:var(--color-primary-pale-charcoal);--color-bg-secondary-cobalt:var(--color-secondary-cobalt);--color-bg-grey:#cccfd2;--color-bg-light-grey:#f2f2f2;--color-bg-light-charcoal:var(--color-primary-light-charcoal);--color-bg-inactive-charcoal:#666;--color-bg-light-emerald:#e9f4f3;--color-bg-light-cobalt:#e9f1f7;--color-bg-light-burgundy:#f7ebee;--color-bg-light-cobalt-translucent:#f2f7fb;--color-bg-warning-yellow:#f3d429;--color-bg-warning-yellow-translucent:#f3d42914;--color-border-pale-grey:var(--color-primary-pale-charcoal);--color-border-primary-charcoal:var(--color-primary-charcoal);--color-border-light-charcoal:var(--color-primary-light-charcoal);--color-border-grey:var(--color-bg-grey);--color-border-light-grey:var(--color-bg-light-grey);--color-border-white:var(--color-bg-white);--color-outline-dark-bg:#cce0f1;--z-index-dropdown-list-in-table:50;--z-index-table-header:80;--z-index-dropdown-list:100;--z-index-modal:700;--z-index-dropdown-menu:650;--z-index-drawer:600;--z-index-drawer-overlay:500;--z-index-toast:800;--z-index-spinner:800;--z-index-chip-dropdown:1000;--z-index-btn-dropdown:200;--z-index-popup:300;--z-index-tooltip:300;--screensize-xxs:320px;--screensize-xs:481px;--screensize-s:769px;--screensize-m:1025px;--screensize-l:1201px;--screensize-xl:1441px;--screensize-xxl:1601px;--primary-font-family:"ProximaNova",Arial,sans-serif;--main-header-height:3.5rem}@media (min-width:769px){:root{--fontsize-h1:2.812rem;--fontsize-h2:2.25rem;--fontsize-h3:1.875rem;--fontsize-h4:1.375rem;--fontsize-h5:1.375rem;--fontsize-h6:1.25rem;--lineheight-h1:3.5rem;--lineheight-h2:3rem;--lineheight-h3:2.5rem;--lineheight-h4:2rem;--lineheight-h5:2rem;--lineheight-h6:1.5rem;--main-header-height:3.75rem}}body{font-family:var(--primary-font-family)}h1{font-size:var(--fontsize-h1);line-height:var(--lineheight-h1)}h1,h2{font-weight:600}h2{font-size:var(--fontsize-h2);line-height:var(--lineheight-h2)}h3{font-size:var(--fontsize-h3);line-height:var(--lineheight-h3)}h3,h4{font-weight:600}h4{font-size:var(--fontsize-h4);line-height:var(--lineheight-h4)}h5{font-size:var(--fontsize-h5);line-height:var(--lineheight-h5)}h5,h6{font-weight:600}h6{font-size:var(--fontsize-h6);line-height:var(--lineheight-h6)}p{font-size:var(--fontsize-default);line-height:var(--lineheight-default)}'),S(".copyclipboard-module__copyToClipboard___ohnuh{align-items:center;display:flex;gap:.25rem}.copyclipboard-module__copyToClipboardText___mP-dd{margin:0}.copyclipboard-module__copyButton___ERldK{background-color:transparent;border:none;border-radius:50%;display:flex;padding:.5rem;transition:background-color .2s}.copyclipboard-module__copyButton___ERldK:hover{background-color:var(--color-bg-light-grey)}"),S('.datepicker-module__datePickerLabel___cSRsu{display:none}.datepicker-module__datePickerCalendarDayRow___bnF1b,.datepicker-module__datePickerCalendarRow___Y8ISP:nth-child(2n),.datepicker-module__datePickerCalendar___tNtJw,.datepicker-module__datePicker___s3de9{background-color:var(--color-bg-white)}.datepicker-module__datePicker___s3de9{box-shadow:0 2px 4px 0 #00000026;font-family:ProximaNova,Arial,sans-serif}.datepicker-module__control___Jd0ph button{transition:background-color .2s;&:hover{background-color:var(--color-primary-soft-charcoal)}}.datepicker-module__datePickerButton___QTu6X{border:none;cursor:pointer;padding:0}[data-scope=date-picker][data-part=positioner]{display:flex;min-height:25rem}[data-scope=date-picker][data-part=content][data-placement=top-end],[data-scope=date-picker][data-part=content][data-placement=top-start],[data-scope=date-picker][data-part=content][data-placement=top]{align-self:flex-end}[data-scope=date-picker][data-part=content][data-placement=bottom-end],[data-scope=date-picker][data-part=content][data-placement=bottom-start],[data-scope=date-picker][data-part=content][data-placement=bottom]{align-self:flex-start}.datepicker-module__datePickerButton___QTu6X svg path{fill:var(--color-primary-charcoal)}.datepicker-module__datePickerTrigger___-pLav{background-color:var(--color-bg-white);border:none;color:var(--color-primary-charcoal);font-weight:600;padding:0}.datepicker-module__datePickerInput___nG3Pj{background-color:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:2px;color:var(--color-primary-charcoal);font-family:ProximaNova,Arial,sans-serif;font-size:var(--fontsize-body);line-height:var(--lineheight-body);outline:none;padding:.5rem 1rem;width:100%;&:hover{background-color:var(--color-primary-soft-charcoal)}}.datepicker-module__datePickerInput___nG3Pj:focus-visible{outline:none;&.focusedByKeyboard{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}}.datepicker-module__datePickerInput___nG3Pj[data-state=open]{border:1px solid var(--color-bg-primary-charcoal)}.datepicker-module__datePickerControl___J7h-X{align-items:center;display:flex;justify-content:space-between;padding:.5rem .75rem}.datepicker-module__datePickerBody___noLH-{padding:.5rem 1rem 1rem}.datepicker-module__datePickerCalendarDays___bb4ow,.datepicker-module__datePickerDate___41T2G{border:none;color:var(--color-primary-charcoal);text-align:center}.datepicker-module__datePickerDate___41T2G{border-radius:2px;font-size:var(--fontsize-body-small);height:2rem;line-height:var(--lineheight-body-small);padding:0;width:2.5rem}.datepicker-module__datePickerDate___41T2G:hover{background-color:var(--color-primary-soft-charcoal);color:var(--color-primary-charcoal);cursor:pointer}.datepicker-module__datePickerCalendarDays___bb4ow{color:var(--color-bg-inactive-charcoal);font-size:.85rem;font-weight:400;width:14.285%}.datepicker-module__highlightedDate___bYggA{font-weight:600;position:relative}.datepicker-module__hiddenDate___91QCZ{visibility:hidden}.datepicker-module__highlightedDate___bYggA:after{background-color:var(--color-primary-red);border-radius:50%;bottom:4px;content:"";height:4px;left:50%;position:absolute;transform:translate(-50%,50%);width:4px}.datepicker-module__disabledDate___SxXLp,.datepicker-module__outsideMonthDate___6Pc5Q{color:var(--color-bg-grey);cursor:not-allowed;opacity:.5;pointer-events:none}.datepicker-module__tableCellTrigger___l3vh5{align-items:center;display:flex;height:100%;justify-content:center;width:100%}[data-scope=date-picker][data-part=content][data-state=open]{position:relative;z-index:var(--z-index-modal)}.datepicker-module__customRange___DAQjV{color:var(--color-bg-inactive-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body);text-align:center}.datepicker-module__rangeTriggers___Am1lD{background-color:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:100px;color:var(--color-primary-light-charcoal);cursor:pointer;font-size:.875rem;padding:.5rem 1rem}.datepicker-module__customRanges___6lf6j{border-bottom:1px solid var(--color-primary-pale-charcoal);display:flex;gap:.5rem;justify-content:center;padding:.5rem .75rem}.datepicker-module__rangeEnd___MVw-K,.datepicker-module__rangeEnd___MVw-K:hover,.datepicker-module__rangeStart___eFyKb,.datepicker-module__rangeStart___eFyKb:hover,.datepicker-module__selectedDate___7gQvH,.datepicker-module__selectedDate___7gQvH:hover{background-color:var(--color-primary-red);border-radius:2px;color:var(--color-bg-white);font-weight:600}.datepicker-module__rangeBetween___ZHeKx,.datepicker-module__rangeBetween___ZHeKx:hover{background-color:var(\r\n    --color-secondary-burgundy-badge\r\n  );color:var(--color-primary-charcoal);font-weight:400}.datepicker-module__clickable___m1-wW,.datepicker-module__datePickerButton___QTu6X,.datepicker-module__datePickerCalendarDays___bb4ow,.datepicker-module__datePickerDate___41T2G:not(.datepicker-module__disabledDate___SxXLp),.datepicker-module__hover___8zdPO:hover{cursor:pointer}[data-scope=date-picker][data-part=table]{width:100%}[data-scope=date-picker][data-part=table-cell][aria-disabled=true]{color:var(--color-bg-grey);cursor:not-allowed;opacity:.5;pointer-events:none}[data-scope=date-picker][data-part=table-header]{padding:.5rem}.datepicker-module__rangeSelected___rDJnD{border:1px solid var(--color-primary-charcoal)}.datepicker-module__actions___h-Lbo{border-top:1px solid var(--color-primary-pale-charcoal);display:flex;justify-content:space-between}.datepicker-module__resetButton___73r1F{border-radius:0;color:var(--color-bg-inactive-charcoal);cursor:pointer;padding:1rem;text-align:left}');var ui=function(){var e=d.useDatePickerContext().getMonthSelectProps().defaultValue,t=c.useTranslation("datetime");return a.createElement(d.DatePicker.ViewTrigger,{className:"datepicker-module__datePickerTrigger___-pLav"},(0,t.t)("month-"+e))},pi=function(e){var o,n,l,i=e.placeholder,s=void 0===i?"":i,_=e.dataTestId,m=void 0===_?"uikit-customDatePicker":_,u=e.ariaLabel,p=e.ariaDescribedBy,g=e.ariaLabelledBy,h=e.tabIndex,f=e.selectionMode,b=void 0===f?"single":f,v=e.defaultOpen,w=void 0!==v&&v,y=e.closeOnSelect,x=void 0!==y&&y,E=e.enableRange,C=void 0===E||E,k=e.childrenTrigger,T=e.childrenInput,I=e.onChange,z=e.shouldReset,N=void 0!==z&&z,S=e.onRangeChange,B=e.calendarId,L=e.value,O=void 0===L?"":L,A=e.startDate,D=void 0===A?"":A,j=e.endDate,M=void 0===j?"":j,P=e.includeReset,H=void 0!==P&&P,R=e.onReset,V=e.placement,F=e.disableToday,W=void 0!==F&&F,U=e.triggerClassName,q=c.useTranslation("datetime").t,K=c.useTranslation("global").t,G=gl().focusClass,Y=t.useRef(null),J=function(e){var t;return t=e?"string"==typeof e?new Date(e):e:new Date,isNaN(t.getTime())&&(t=new Date),t.toISOString().split("T")[0]},Z=d.useDatePicker({selectionMode:b,id:B,defaultOpen:w,defaultValue:D&&M?[J(D),J(M)]:O?[J(O)]:void 0,closeOnSelect:x,positioning:{placement:V},onOpenChange:function(e){if(!e.open){var t,a=null==(t=Y.current)?void 0:t.querySelector("#trigger-input-date-picker");a&&a.focus()}}}),X=new Date;X.setHours(0,0,0,0);var Q=X.toISOString().split("T")[0],$=Math.floor(Z.weeks.length/2),ee=Math.floor(Z.weeks[$].length/2),te=Z.weeks[$][ee],ae=te.month,re=te.year,oe=function(e){var t=new Date(e.year,e.month-1,e.day);return t.setHours(0,0,0,0),t.toISOString().split("T")[0]===Q},ne=function(e){var t=new Date(e.year,e.month-1,e.day);t.setHours(0,0,0,0);var a=t.toISOString().split("T")[0]===Q,r=e.month!==ae||e.year!==re;return"range"===b?r:!!r||!(!W||!a)||t<X&&!a};t.useEffect((function(){null==S||S(null==Z?void 0:Z.value.map((function(e){return new Date(e.year,e.month-1,e.day)})))}),[Z.value]),t.useEffect((function(){if(Z.value.length>1){var e=new Date(Z.value[0].year,Z.value[0].month-1,Z.value[0].day),t=new Date(Z.value[Z.value.length-1].year,Z.value[Z.value.length-1].month-1,Z.value[Z.value.length-1].day);we({start:e,end:t})}}),[Z.value]);var le=t.useState(D?new Date(D):null),ie=le[0],ce=le[1],de=t.useState(M?new Date(M):null),se=de[0],_e=de[1],me=t.useState(null),ue=me[0],pe=me[1],ge=t.useState(""),he=ge[0],fe=ge[1],be=t.useState({start:D?new Date(D):void 0,end:M?new Date(M):void 0}),ve=be[0],we=be[1];t.useEffect((function(){N&&(ce(null),_e(null),we({}),fe(""),pe(null),Z.clearValue())}),[N]);var ye=function(){null==R||R(),ce(null),_e(null),we({}),fe(""),pe(null),Z.clearValue()},xe=function(e){e.target.value.trim()||ye()};return a.createElement(d.DatePicker.RootProvider,{value:Z,"data-testid":m,tabIndex:h,ref:Y},a.createElement(d.DatePicker.Control,{className:r("datepicker-module__control___Jd0ph",U)},void 0!==k?a.createElement(d.DatePicker.Trigger,{asChild:!0,"aria-label":u,"aria-describedby":p,"aria-labelledby":g,id:"trigger-input-date-picker"},k):void 0!==T?a.createElement(d.DatePicker.Input,{id:"trigger-input-date-picker",onChange:xe,onClick:function(){Z.open||Z.setOpen(!0)},onKeyDown:function(e){"Enter"!==e.key||Z.open||(e.preventDefault(),Z.setOpen(!0))},asChild:!0},T):a.createElement(d.DatePicker.Input,{id:"trigger-input-date-picker",className:r("datepicker-module__datePickerInput___nG3Pj",G),placeholder:""+s,onChange:xe,onClick:function(){Z.open||Z.setOpen(!0)},onKeyDown:function(e){"Enter"!==e.key||Z.open||(e.preventDefault(),Z.setOpen(!0))}})),a.createElement(d.DatePicker.Positioner,null,a.createElement(d.DatePicker.Content,null,a.createElement(d.DatePicker.View,{view:"day",className:"datepicker-module__datePicker___s3de9"},a.createElement(a.Fragment,null,C&&a.createElement("div",{className:"datepicker-module__customRanges___6lf6j"},a.createElement("button",{onClick:function(){ce(null),_e(null),pe(null),fe("today");var e=wl("today"),t=e.startOfRange,a=e.endOfRange;null==S||S([t,a]),we({start:t,end:a}),Z.clearValue(),Z.selectToday(),Z.setOpen(!1)},className:r("datepicker-module__rangeTriggers___Am1lD",(o={},o["datepicker-module__rangeSelected___rDJnD"]="today"===he,o))},q("today")),a.createElement(d.DatePicker.PresetTrigger,{asChild:!0,value:"last7Days",onClick:function(){ce(null),_e(null),pe(null),fe("last7Days");var e=wl("last7Days");we({start:e.startOfRange,end:e.endOfRange})}},a.createElement("button",{className:r("datepicker-module__rangeTriggers___Am1lD",(n={},n["datepicker-module__rangeSelected___rDJnD"]="last7Days"===he,n))},q("last-7-days"))),a.createElement(d.DatePicker.PresetTrigger,{asChild:!0,value:"last30Days",onClick:function(){ce(null),_e(null),pe(null),fe("last30Days");var e=wl("last30Days");we({start:e.startOfRange,end:e.endOfRange})}},a.createElement("button",{className:r("datepicker-module__rangeTriggers___Am1lD",(l={},l["datepicker-module__rangeSelected___rDJnD"]="last30Days"===he,l))},q("last-30-days")))),a.createElement("div",{className:"datepicker-module__datePickerBody___noLH-"},a.createElement(d.DatePicker.Label,{className:"datepicker-module__datePickerLabel___cSRsu"},"Date Picker"),"range"===b&&a.createElement("div",{className:"datepicker-module__customRange___DAQjV"},a.createElement("span",null,q("custom-range"))),a.createElement(d.DatePicker.ViewControl,{className:"datepicker-module__datePickerControl___J7h-X"},a.createElement(d.DatePicker.PrevTrigger,{className:"datepicker-module__datePickerButton___QTu6X","aria-label":q("previous-month")},a.createElement(nl,{iconName:"chevron-left",size:20})),a.createElement(ui,null),a.createElement(d.DatePicker.NextTrigger,{className:"datepicker-module__datePickerButton___QTu6X","aria-label":q("next-month")},a.createElement(nl,{iconName:"chevron-right",size:20}))),a.createElement(d.DatePicker.Table,{className:"datepicker-module__datePickerCalendar___tNtJw"},a.createElement(d.DatePicker.TableHead,{className:"datepicker-module__datePickerCalendarDayRow___bnF1b"},a.createElement(d.DatePicker.TableRow,null,Z.weekDays.map((function(e,t){return a.createElement(d.DatePicker.TableHeader,{className:"datepicker-module__datePickerCalendarDays___bb4ow",key:t},q("weekday-short-"+e.long.toLowerCase()))})))),a.createElement(d.DatePicker.TableBody,null,Z.weeks.map("single"==b?function(e,t){return a.createElement(d.DatePicker.TableRow,{className:"datepicker-module__datePickerCalendarRow___Y8ISP",key:t},e.map((function(e,t){var o;return a.createElement(d.DatePicker.TableCell,{key:t,value:e,className:r("datepicker-module__datePickerDate___41T2G",(o={},o["datepicker-module__selectedDate___7gQvH"]=Z.valueAsString[0]===e.toString(),o["datepicker-module__highlightedDate___bYggA"]=oe(e),o["datepicker-module__disabledDate___SxXLp"]=ne(e),o["datepicker-module__hiddenDate___91QCZ"]=e.month!==ae,o))},a.createElement(d.DatePicker.TableCellTrigger,{className:"datepicker-module__tableCellTrigger___l3vh5","aria-disabled":ne(e),onClick:function(){null==I||I(e)}},e.day))})))}:function(e,t){return a.createElement(d.DatePicker.TableRow,{className:"datepicker-module__datePickerCalendarRow___Y8ISP",key:t},e.map((function(e,t){var o,n=new Date(e.year,e.month-1,e.day);n.setHours(0,0,0,0);var l=n.getTime()===X.getTime(),i=ne(e),c="range"===b,s=c&&ie&&n.getTime()===ie.getTime(),_=c&&se&&n.getTime()===se.getTime(),m=c&&ie&&se&&n>ie&&n<se,u=!1;c&&ie&&ue&&!se&&(u=n>ie&&n<ue||n<ie&&n>ue);var p=c&&ue&&!se&&n.getTime()===ue.getTime(),g=!c&&ie&&n.getTime()===ie.getTime();return a.createElement(d.DatePicker.TableCell,{key:t,value:e,className:r("datepicker-module__datePickerDate___41T2G",(o={},o["datepicker-module__highlightedDate___bYggA"]=l,o["datepicker-module__disabledDate___SxXLp"]=i,o["datepicker-module__rangeStart___eFyKb"]=s||g||ve.start&&n.getTime()===ve.start.getTime(),o["datepicker-module__rangeEnd___MVw-K"]=_||p||ve.end&&n.getTime()===ve.end.getTime(),o["datepicker-module__rangeBetween___ZHeKx"]=m||u||ve.start&&ve.end&&n>ve.start&&n<ve.end,o["datepicker-module__hiddenDate___91QCZ"]=e.month!==ae,o))},a.createElement(d.DatePicker.TableCellTrigger,{className:"datepicker-module__tableCellTrigger___l3vh5","aria-disabled":i,onClick:function(){i||function(e){var t=new Date(e.year,e.month-1,e.day);t.setHours(0,0,0,0),!ie||ie&&se?(ce(t),_e(null),pe(null),we({start:t,end:void 0})):(_e(t),we({start:ie,end:t}))}(e),null==I||I(e)},onMouseEnter:function(){return function(e){if(ie&&!se){var t=new Date(e.year,e.month-1,e.day);t.setHours(0,0,0,0),pe(t)}}(e)}},e.day))})))})))),H&&a.createElement("div",{className:"datepicker-module__actions___h-Lbo"},a.createElement("button",{className:"datepicker-module__resetButton___73r1F",onClick:ye},K("clear-filter"))))))))},gi={textContainer:"documentUpload-module__textContainer___JfO4Z",title:"documentUpload-module__title___KDbcJ",titleAsterisk:"documentUpload-module__titleAsterisk___QkhSZ",subtitle:"documentUpload-module__subtitle___rNX0c",dropzoneContainer:"documentUpload-module__dropzoneContainer___06wiX",dropzoneContainerError:"documentUpload-module__dropzoneContainerError___k4Z9A",dropdownText:"documentUpload-module__dropdownText___Ce6xu",dropdownTextBlue:"documentUpload-module__dropdownTextBlue___mj5nC",documentIcon:"documentUpload-module__documentIcon___2YP9M",pendingIcon:"documentUpload-module__pendingIcon___I8YaQ",nameContainer:"documentUpload-module__nameContainer___zJ9-B",toastWrapper:"documentUpload-module__toastWrapper___0hov0",noDocumentsContainer:"documentUpload-module__noDocumentsContainer___jrAuD",noDocumentsInfo:"documentUpload-module__noDocumentsInfo___jle6j",errorTextContainer:"documentUpload-module__errorTextContainer___vFkny",errorText:"documentUpload-module__errorText___5xgFV",labels:"documentUpload-module__labels___8KCRg"};S("[data-scope=file-upload][data-part=root]{background-color:var(--color-primary-white);border:1px solid var(--color-border-pale-grey);border-radius:2px;display:grid;height:100%;width:100%}.documentUpload-module__textContainer___JfO4Z{display:flex;flex-direction:column;gap:.5rem;padding:1rem}.documentUpload-module__title___KDbcJ{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body);margin:0}.documentUpload-module__titleAsterisk___QkhSZ{color:var(--color-secondary-burgundy);margin-left:.25rem}.documentUpload-module__subtitle___rNX0c{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small);margin:0}.documentUpload-module__dropzoneContainer___06wiX{padding:0 1rem 1rem}.documentUpload-module__dropzoneContainerError___k4Z9A{[data-scope=file-upload][data-part=dropzone]{border-color:var(--color-secondary-burgundy)}}[data-scope=file-upload][data-part=root]{height:auto}[data-scope=file-upload][data-part=dropzone]{align-items:center;background-color:#fafafa;border-style:solid;border:1px dashed var(--color-bg-primary-pale-charcoal);border-radius:2px;color:var(--color-bg-inactive-charcoal);display:grid;justify-items:center;padding:1rem 1.5rem;transition:all .5s ease}[data-scope=file-upload][data-part=dropzone]:hover{background-color:var(--color-bg-light-grey)}[data-scope=file-upload][data-part=dropzone][data-dragging]{background-color:var(--color-bg-light-cobalt-transparent);border-color:var(--color-secondary-cobalt)}[data-scope=file-upload][data-part=dropzone]:hover{cursor:pointer}.documentUpload-module__dropdownText___Ce6xu{font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body)}.documentUpload-module__dropdownTextBlue___mj5nC{color:var(--color-secondary-cobalt)}[data-scope=file-upload][data-part=item-group]{margin:0;padding:0}[data-scope=file-upload][data-part=item]{align-items:center;border-top:1px solid var(--color-border-pale-grey);border-top-left-radius:2px;border-top-right-radius:2px;column-gap:.75rem;display:grid;grid-template-columns:auto 1fr auto auto;padding:.75rem 1rem}[data-scope=file-upload][data-part=item-name]{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);line-height:var(--fontsize-body);word-break:break-word}[data-scope=file-upload][data-part=item-delete-trigger]{background-color:transparent;border:none;border-radius:50%;cursor:pointer;margin:0;outline:none;padding:.5rem;transition:background-color .25s ease-in-out;width:auto;svg{color:var(--color-primary-charcoal)}}[data-scope=file-upload][data-part=item-delete-trigger]:hover{background-color:var(--color-bg-light-grey);cursor:pointer}.documentUpload-module__documentIcon___2YP9M{align-items:center;background-color:var(--color-bg-light-grey);border-radius:2px;color:var(--color-primary-slate);display:grid;justify-items:center;padding:.5rem}.documentUpload-module__pendingIcon___I8YaQ{align-items:center;display:flex;height:2rem;justify-content:center;width:2rem}.documentUpload-module__nameContainer___zJ9-B{color:var(--color-bg-inactive-charcoal);font-size:var(--fontsize-body-small);justify-self:end;line-height:var(--fontsize-body-small)}.documentUpload-module__toastWrapper___0hov0{bottom:1rem;min-width:358px;position:fixed;right:1.5rem;transform:translate(-50%)}.documentUpload-module__noDocumentsContainer___jrAuD{border-top:1px solid var(--color-bg-primary-pale-charcoal);padding:1rem}.documentUpload-module__noDocumentsInfo___jle6j{background-color:#fafafa;border-radius:2px;color:var(--color-primary-inactive-charcoal);font-size:var(--fontsize-body);line-height:var(--lineheight-body);padding:1rem 1.5rem;text-align:center}.documentUpload-module__errorTextContainer___vFkny{align-items:center;color:var(--color-secondary-burgundy);display:flex;flex-direction:row;font-size:var(--fontsize-body-small);gap:.5rem;line-height:var(--lineheight-body-small);padding:.5rem 0}.documentUpload-module__errorText___5xgFV{text-wrap:nowrap;font-size:var(--fontsize-body-small);line-height:var(--lineheight-body);margin:0}@media (min-width:768px){.documentUpload-module__toastWrapper___0hov0{bottom:128px;left:default;transform:none}.documentUpload-module__labels___8KCRg{grid-template-columns:1fr auto}}");var hi={toaster:"toaster-module__toaster___ldRvX",toast:"toaster-module__toast___amRbV",success:"toaster-module__success___b-uVw",error:"toaster-module__error___vYYFp",info:"toaster-module__info___oyzsU",warning:"toaster-module__warning___L40Rl",toastContent:"toaster-module__toastContent___eaghb",toastTitle:"toaster-module__toastTitle___lkHWy",toastDescription:"toaster-module__toastDescription___zKUo5",toastIcon:"toaster-module__toastIcon___pz4LS",toastCTA:"toaster-module__toastCTA___Qafx9",closeButton:"toaster-module__closeButton___rCZ8I",slideInRight:"toaster-module__slideInRight___-LFzO",slideOutRight:"toaster-module__slideOutRight___YJpsM",bulletPoints:"toaster-module__bulletPoints___nPPVQ"};S('.toaster-module__toaster___ldRvX{bottom:10px;position:fixed;right:16px;z-index:9999}.toaster-module__toast___amRbV{align-items:center;border:1px solid;border-radius:2px;box-shadow:0 4px 12px rgba(0,0,0,.15);box-sizing:border-box;color:var(--color-primary-charcoal);cursor:default;display:flex;gap:1rem;max-width:475px;min-width:475px;opacity:1;padding:.65rem 1rem;pointer-events:auto;position:relative!important;transform:translateX(0);transition:all .3s cubic-bezier(.4,0,.2,1);width:100%;word-break:break-word}[data-scope=toast][data-part=group]{bottom:16px;display:flex;flex-direction:column-reverse;gap:16px;max-height:calc(100vh - 32px);overflow:hidden;padding:.5rem;pointer-events:none;position:fixed;right:36px!important;z-index:10000}.toaster-module__toast___amRbV:last-child{z-index:2}.toaster-module__toast___amRbV:focus{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}.toaster-module__toast___amRbV.toaster-module__success___b-uVw{background-color:var(--color-bg-light-emerald);border-color:var(--color-secondary-emerald)}.toaster-module__toast___amRbV.toaster-module__error___vYYFp{background-color:var(--color-bg-light-burgundy);border-color:var(--color-secondary-burgundy)}.toaster-module__toast___amRbV.toaster-module__info___oyzsU,.toaster-module__toast___amRbV.toaster-module__warning___L40Rl{background-color:var(--color-bg-light-cobalt);border-color:var(--color-secondary-cobalt)}.toaster-module__toastContent___eaghb{display:flex;flex:1;flex-direction:column;gap:.15rem;min-width:0}.toaster-module__toastTitle___lkHWy{font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.toaster-module__toastDescription___zKUo5,.toaster-module__toastTitle___lkHWy{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);line-height:var(--lineheight-body);margin:0}.toaster-module__toastDescription___zKUo5{word-wrap:break-word;overflow-wrap:break-word}.toaster-module__toastIcon___pz4LS{align-items:center;display:flex;flex-shrink:0;height:18px;justify-content:center;width:18px}.toaster-module__toastCTA___Qafx9{background:none;border:none;color:var(--color-secondary-cobalt);cursor:pointer;flex-shrink:0;font-size:inherit;font-weight:700;line-height:inherit;padding:0;text-decoration:none}.toaster-module__toastCTA___Qafx9:hover{text-decoration:underline}.toaster-module__toastCTA___Qafx9:focus-visible{border-radius:2px;outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}.toaster-module__closeButton___rCZ8I{align-items:center;background:none;border:none;border-radius:4px;color:var(--color-primary-charcoal);cursor:pointer;display:flex;flex-shrink:0;height:20px;justify-content:center;padding:4px;width:20px}.toaster-module__closeButton___rCZ8I:hover{background-color:rgba(0,0,0,.1)}.toaster-module__closeButton___rCZ8I:focus-visible{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}.toaster-module__toast___amRbV[data-state=open]{animation:toaster-module__slideInRight___-LFzO .3s cubic-bezier(.4,0,.2,1) forwards}.toaster-module__toast___amRbV[data-state=closed]{animation:toaster-module__slideOutRight___YJpsM .3s cubic-bezier(.4,0,.2,1) forwards}@keyframes toaster-module__slideInRight___-LFzO{0%{opacity:0;transform:translateX(100%)}to{opacity:1;transform:translateX(0)}}@keyframes toaster-module__slideOutRight___YJpsM{0%{opacity:1;transform:translateX(0)}to{opacity:0;transform:translateX(100%)}}.toaster-module__toast___amRbV:hover{box-shadow:0 6px 20px rgba(0,0,0,.2);transform:translateX(-4px)}.toaster-module__bulletPoints___nPPVQ{list-style:none;margin:0;padding:0}.toaster-module__bulletPoints___nPPVQ li{margin-bottom:4px;padding-left:16px;position:relative}.toaster-module__bulletPoints___nPPVQ li:before{color:var(--color-primary-charcoal);content:"•";left:0;position:absolute}');var fi,bi=d.createToaster({id:"bcp-toaster",placement:"bottom-end",overlap:!1,gap:16,max:5,duration:5e3}),vi=new Map,wi=function(){return{create:function(e){var t=bi.create({title:e.title,description:e.description,type:e.type||"info",duration:5e3,meta:{errorMessageWithLineBreaks:e.errorMessageWithLineBreaks,replaceLineBreaksWithBulletPoints:e.replaceLineBreaksWithBulletPoints}});return e.withCTA&&t&&vi.set(t,{label:e.ctaText||"Action",onClick:e.onCTAClick||function(){}}),t},dismiss:function(e){vi.delete(e),bi.dismiss(e)},dismissAll:function(){vi.clear(),bi.dismiss()}}};(fi=exports.ChildType||(exports.ChildType={})).FOLDER="folder",fi.FILE="file";var yi={treeViewRoot:"treeView-module__treeViewRoot___UVvqK",branch:"treeView-module__branch___WZhT4",focusableBranch:"treeView-module__focusableBranch___HxXDX",indicator:"treeView-module__indicator___m4vFa",disabledLink:"treeView-module__disabledLink___gp59s",readOnly:"treeView-module__readOnly___rRg7A",treeViewSelected:"treeView-module__treeViewSelected___7N9A6",icons:"treeView-module__icons___GLy-j",dropdown:"treeView-module__dropdown___wCP3g","rotation-counter-clockwise":"treeView-module__rotation-counter-clockwise___hacnd",dropdownRotated:"treeView-module__dropdownRotated___9HFKj","rotation-clockwise":"treeView-module__rotation-clockwise___01IP-",folder:"treeView-module__folder___q5IO-",highlighted:"treeView-module__highlighted___jZGF9",labelParent:"treeView-module__labelParent___LMWgy",loading:"treeView-module__loading___B725f",restrictedAndReadonly:"treeView-module__restrictedAndReadonly___p3cQh"};S("@keyframes treeView-module__rotation-clockwise___01IP-{0%{transform:rotate(0deg)}to{transform:rotate(90deg)}}@keyframes treeView-module__rotation-counter-clockwise___hacnd{0%{transform:rotate(90deg)}to{transform:rotate(0deg)}}.treeView-module__treeViewRoot___UVvqK ul{list-style-type:none;margin:0;padding-inline-start:.75rem;width:100%}.treeView-module__treeViewRoot___UVvqK ul li{margin-bottom:5px}.treeView-module__branch___WZhT4{padding:0;text-align:left;width:100%}.treeView-module__focusableBranch___HxXDX:focus-visible{outline:none;.treeView-module__indicator___m4vFa{border-radius:3px;outline:2px solid var(--color-secondary-cobalt);outline-offset:-3px}}.treeView-module__indicator___m4vFa{align-content:center;align-items:center;display:flex;font-size:14px;gap:.25rem;height:44px;line-height:20px;margin-bottom:0;padding-left:8px;padding-right:8px}.treeView-module__disabledLink___gp59s{cursor:not-allowed;opacity:.5;pointer-events:none}.treeView-module__readOnly___rRg7A{padding-left:2rem}.treeView-module__indicator___m4vFa svg{vertical-align:middle}.treeView-module__indicator___m4vFa:hover{background-color:#f2f2f2;cursor:pointer;max-width:210px;min-width:-webkit-fill-available;width:fit-content}.treeView-module__treeViewSelected___7N9A6{background-color:#f2f2f2}.treeView-module__indicator___m4vFa:focus-visible{background-color:#f2f2f2}.treeView-module__icons___GLy-j,.treeView-module__icons___GLy-j .treeView-module__dropdown___wCP3g{align-items:center;display:flex}.treeView-module__icons___GLy-j .treeView-module__dropdown___wCP3g{animation:treeView-module__rotation-counter-clockwise___hacnd .25s linear;color:#5b6e7f}.treeView-module__icons___GLy-j .treeView-module__dropdownRotated___9HFKj{animation:treeView-module__rotation-clockwise___01IP- .25s linear;color:#5b6e7f;display:inline-block;transform:rotate(90deg)}.treeView-module__icons___GLy-j .treeView-module__folder___q5IO-{display:inline-block;margin-left:8px;margin-right:8px}.treeView-module__icons___GLy-j .treeView-module__dropdown___wCP3g:focus-visible{transform:rotate(90deg)}.treeView-module__highlighted___jZGF9{background-color:#f2f2f2;font-weight:600;min-width:-webkit-fill-available;width:fit-content}.treeView-module__labelParent___LMWgy{align-content:center;align-items:center;color:var(--color-primary-light-charcoal);display:flex;gap:.75rem;height:100%;justify-content:space-between;width:100%;span{align-items:center;display:flex;height:100%;white-space:nowrap}span:hover{background-color:#f2f2f2}}.treeView-module__loading___B725f{align-items:center;display:flex;justify-content:center}.treeView-module__restrictedAndReadonly___p3cQh{display:flex;gap:8px;padding-right:4px}");var xi=function(e,t){return t?e+"?"+new URLSearchParams({path:t}).toString():e},Ei=function(e){var r=e.baseUrl,o=e.dataTestId,n=void 0===o?"uikit-customTreeView":o,l=e.ariaLabel,i=e.ariaDescribedBy,c=e.ariaLabelledBy,_=e.isLoading,m=void 0!==_&&_,u=e.updateIsRestricted,p=e.updateFolderItemId,g=e.id,h=e.items,f=t.useState([]),b=f[0],v=f[1],w=s.useLocation();t.useEffect((function(){var e=h.filter((function(e){return"folder"===e.type&&e.descendants&&e.descendants.length>0&&e.descendants.some((function(e){return"folder"===e.type&&!e.deletionInProgress}))})).map((function(e){return e.id}));v((function(t){var a=[].concat(t);return e.forEach((function(e){a.includes(e)||a.push(e)})),a}))}),[h]),t.useEffect((function(){var e=(new URLSearchParams(w.search).get("path")||"").split("/").filter(Boolean),t=[],a=h;e.forEach((function(e){var r=a.find((function(t){return t.name===e}));r&&(t.push(r.id),a=r.descendants||[])})),v((function(e){var a=[].concat(e);return t.forEach((function(e){a.includes(e)||a.push(e)})),a}))}),[w.search,h]);var y=function(e){b.includes(e)?v(b.filter((function(t){return t!=e}))):v([].concat(b,[e]))};return a.createElement("div",{"data-testid":n},a.createElement(d.TreeView.Root,{className:yi.treeViewRoot,"aria-label":l,"aria-labelledby":c,"aria-describedby":i,id:g,expandOnClick:!1,expandedValue:b},a.createElement(d.TreeView.Tree,null,h.filter((function(e){return"folder"===e.type})).map((function(e,t){return a.createElement(Ci,{key:e.id+"-"+t,id:e.id,name:e.name,baseUrl:r,path:"/"+e.name,isLoading:m,updateIsRestricted:u,restricted:e.restricted,updateFolderItemId:p,listItemId:e.listItemId,type:e.type,descendants:e.descendants,displayName:e.displayName,readonly:e.readonly||yl("/"+e.name),deletionInProgress:e.deletionInProgress,changeExpandedValues:y,openTab:b.indexOf(e.id)>=0})})))))},Ci=function(e){var o,n,l,i,_,m=e.id,u=e.name,p=e.baseUrl,g=e.path,h=e.isLoading,f=e.updateIsRestricted,b=e.restricted,v=e.updateFolderItemId,w=e.listItemId,y=void 0===w?0:w,x=e.type,E=e.descendants,C=e.displayName,k=e.readonly,T=e.changeExpandedValues,I=e.openTab,z=e.deletionInProgress,N=s.useSearchParams()[0],S=c.useTranslation("documents").t,B=s.useNavigate(),L=t.useState(!1),O=L[0],A=L[1];t.useEffect((function(){if(E){var e=E.filter((function(e){return e.type==exports.ChildType.FOLDER&&!e.deletionInProgress}));A(e.length>0)}}),[E]);var D=Array.from(N.values()),j=(null!=(o=null==(n=D[D.length-1])?void 0:n.split("/").pop())?o:"")===u,M=function(e){h?e.preventDefault():(B(xi(p,g)),f(b),v(y))};return z?null:a.createElement(d.TreeView.Branch,{value:m},a.createElement(d.TreeView.BranchControl,{asChild:!0},a.createElement("button",{onClick:function(e){return M(e)},onKeyDown:function(e){"Enter"===e.key&&M(e),("ArrowRight"===e.key&&!I&&O||"ArrowLeft"===e.key&&I)&&T(m)},className:r(yi.branch,(l={},l[yi.focusableBranch]=!h,l[yi.disabledLink]=h&&!j,l)),"aria-label":u},a.createElement("div",{className:r(yi.indicator,(i={},i[yi.treeViewSelected]=j,i))},x==exports.ChildType.FOLDER&&a.createElement("div",{className:yi.icons},O&&a.createElement("div",{className:I?yi.dropdownRotated:yi.dropdown,onClick:function(e){e.stopPropagation(),T(m)}},a.createElement(nl,{iconName:"chevron-right",height:12,width:12})),a.createElement("div",{className:yi.folder,onClick:function(e){e.stopPropagation(),M(e)}},a.createElement(nl,{iconName:"folder-icon-filled"}))),a.createElement("div",{className:yi.labelParent},a.createElement(d.TreeView.BranchText,{className:r((_={},_[yi.label]=!E,_))},C),a.createElement("div",{className:b&&k?yi.restrictedAndReadonly:""},b&&a.createElement(_i,{message:"Restricted",inputId:m+"-restricted",direction:"down",openDelay:100,closeDelay:100},a.createElement(nl,{iconName:"lock-icon",size:12})),k&&a.createElement(_i,{message:S("read-only"),inputId:m+"-readonly",direction:"down",openDelay:100,closeDelay:100},a.createElement(nl,{iconName:"read-only",size:12}))))))),a.createElement(d.TreeView.BranchContent,null,h&&j?a.createElement("div",{className:yi.loading},a.createElement(dl,{isLoading:!0})):E&&E.length>0?E.filter((function(e){return"folder"===e.type})).map((function(e){var t=g+"/"+e.name;return a.createElement(Ci,{key:e.id,name:e.name,displayName:e.displayName,id:e.id,descendants:e.descendants,type:e.type,baseUrl:p,path:t,dataTestId:"uikit-customTreeViewChild",readonly:e.readonly||yl(t),restricted:e.restricted,isLoading:h,deletionInProgress:e.deletionInProgress,updateIsRestricted:f,updateFolderItemId:v,listItemId:y,changeExpandedValues:T,openTab:I})})):j&&h&&a.createElement("div",{className:yi.loading},a.createElement(dl,{isLoading:!0}))))},ki={parentDiv:"documentsNav-module__parentDiv___MYuMp",inputParent:"documentsNav-module__inputParent___q-oup",input:"documentsNav-module__input___-3mmX",labelParent:"documentsNav-module__labelParent___692E7",label:"documentsNav-module__label___YIMGl",docNavButton:"documentsNav-module__docNavButton___SqqF6",documentsTitleLink:"documentsNav-module__documentsTitleLink___vHXwO",documentsHighlight:"documentsNav-module__documentsHighlight___9v8TI",folders:"documentsNav-module__folders___mrTZ0",spinnerContainer:"documentsNav-module__spinnerContainer___-DxxC"};S(".documentsNav-module__parentDiv___MYuMp{background-color:#fff;display:flex;flex-direction:column;height:100%;min-height:0}.documentsNav-module__inputParent___q-oup{margin:0 1rem;padding-top:16px}.documentsNav-module__inputParent___q-oup [data-forspacing=forSearchIcon]{top:12px}.documentsNav-module__input___-3mmX{border-radius:100px;font-size:.875rem;height:40px;letter-spacing:-.1px;padding:0 1.75rem 0 1rem;text-overflow:ellipsis}.documentsNav-module__labelParent___692E7{margin:0 1rem}.documentsNav-module__label___YIMGl{color:#666;font-family:var(--primary-font-family);font-size:var(--fontsize-body-label);font-weight:600;line-height:var(--lineheight-body-label);text-transform:uppercase}.documentsNav-module__docNavButton___SqqF6{cursor:pointer;flex:0 1 auto;height:44px;margin:1rem}.documentsNav-module__documentsTitleLink___vHXwO{align-items:center;border-radius:2px;color:var(--color-primary-light-charcoal);display:flex;font-size:var(--fontsize-body-small);font-weight:400;gap:.5rem;padding:.85rem .5rem;width:100%;&:hover{color:initial}&:focus-visible{outline-color:var(--color-primary-pale-charcoal);outline-width:.25rem}}.documentsNav-module__documentsHighlight___9v8TI,.documentsNav-module__documentsTitleLink___vHXwO:hover{background-color:#f2f2f2}.documentsNav-module__folders___mrTZ0{flex:1 1 auto;margin-right:16px;min-height:0;overflow:auto}.documentsNav-module__folders___mrTZ0 li{display:inline}.documentsNav-module__spinnerContainer___-DxxC{align-items:center;display:flex;height:100%;justify-content:center}");var Ti,Ii=function(e){var r=e.count,o=e.isLoading,n=void 0!==o&&o,l=e.announcementMessage,i=e.countKey,d=void 0===i?"result-count":i,s=e.isLoadingKey,_=void 0===s?"loading":s,m=e.mode,u=void 0===m?"polite":m,p=e.searchTerm,g=void 0===p?"":p,h=t.useRef(null),f=c.useTranslation("global").t,b=t.useState(g),v=b[0],w=b[1];t.useEffect((function(){var e=h.current;if(e){l||(e.innerHTML="");var t=requestAnimationFrame((function(){e&&l&&(e.innerHTML=""+l)}));return function(){return cancelAnimationFrame(t)}}}),[h,l]),t.useEffect((function(){g&&(n?y(f(_,{term:v})):v&&y(f(d,{count:r,term:v})))}),[n,g,v]),t.useEffect((function(){if(g){var e=setTimeout((function(){return w(g)}),2e3);return function(){return clearTimeout(e)}}}),[g]);var y=function(e){var t=h.current;t&&setTimeout((function(){t.innerHTML=""+e}),600)};return a.createElement("div",{role:"status","aria-live":u,className:"sr-only","aria-atomic":"true",ref:h}," ")},zi={drawerWrapper:"drawer-module__drawerWrapper___YDIN6",overlay:"drawer-module__overlay___860-5",drawer:"drawer-module__drawer___5ARuA",large:"drawer-module__large___dv-LS",drawerHeader:"drawer-module__drawerHeader___nCPYX",drawerHeaderLeft:"drawer-module__drawerHeaderLeft___S5EJ2",avatar:"drawer-module__avatar___Kb-Yo",backButton:"drawer-module__backButton___aJHBC",drawerHeaderContent:"drawer-module__drawerHeaderContent___DUV-6",drawerTitle:"drawer-module__drawerTitle___oZmm9",drawerPretitle:"drawer-module__drawerPretitle___z5d-y",drawerSubtitle:"drawer-module__drawerSubtitle___ZWPz2",drawerContent:"drawer-module__drawerContent___EvGma",drawerFooter:"drawer-module__drawerFooter___qzUcs",headerButtons:"drawer-module__headerButtons___7ADKH",copyButton:"drawer-module__copyButton___-VTIy",drawerHeaderTitleOnly:"drawer-module__drawerHeaderTitleOnly___Rfbs-"};S(".drawer-module__drawerWrapper___YDIN6{align-items:end;display:grid;height:100%;justify-content:flex-end;left:0;position:fixed;top:0;width:100%;z-index:var(--z-index-drawer)}.drawer-module__overlay___860-5{background-color:#000;height:100%;left:0;opacity:.5;position:absolute;top:0;width:100%}.drawer-module__drawer___5ARuA{align-content:space-between;background-color:var(--color-bg-white);box-shadow:-4px 0 20px 0 rgba(0,0,0,.1);display:grid;grid-template-rows:auto 1fr auto;height:100vh;max-width:100%;min-width:32.5rem;pointer-events:auto}.drawer-module__drawer___5ARuA.drawer-module__large___dv-LS{width:42rem}.drawer-module__drawerHeader___nCPYX{align-items:center;border-bottom:1px solid var(--color-primary-pale-charcoal);color:var(--color-primary-charcoal);display:grid;grid-template-columns:1fr auto;min-height:5rem;outline:none;overflow:hidden;padding:1rem;.drawer-module__drawerHeaderLeft___S5EJ2{display:flex;flex-direction:row;gap:.75rem;.drawer-module__avatar___Kb-Yo{background-color:var(--color-secondary-ocean);border-radius:50%;color:var(--color-bg-white);font-size:var(--fontsize-h5);font-weight:600;height:56px;line-height:var(--lineheight-h5);margin-right:.75rem;text-align:center;width:56px}.drawer-module__backButton___aJHBC{align-items:center;align-self:center;border-radius:50%;cursor:pointer;display:flex;flex-direction:row;height:2rem;justify-content:center;margin-right:.25rem;padding:0;transition:background-color .2s;width:2rem;>svg{flex-shrink:0}&:hover{background-color:var(--color-bg-light-grey)}}}}.drawer-module__drawerHeaderContent___DUV-6{align-content:center;width:100%}.drawer-module__drawerTitle___oZmm9{align-items:center;display:flex;font-size:var(--fontsize-h4);font-weight:600;height:2.5rem;line-height:2rem;margin:0}.drawer-module__drawerPretitle___z5d-y{align-items:center;color:var(--color-primary-light-charcoal);display:flex;font-size:var(--fontsize-body-small);height:2.5rem;line-height:var(--lineheight-body-small)}.drawer-module__drawerSubtitle___ZWPz2{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);line-height:var(--lineheight-body)}.drawer-module__drawerContent___EvGma{height:100%;overflow:auto;width:100%}.drawer-module__drawerFooter___qzUcs{border-top:1px solid var(--color-bg-primary-pale-charcoal);column-gap:1rem;display:grid;grid-template-columns:1fr 1fr;justify-content:end;padding:1rem}.drawer-module__headerButtons___7ADKH{align-items:center;display:flex;gap:.5rem;position:absolute;right:24px;top:24px}.drawer-module__copyButton___-VTIy:hover{cursor:pointer}@media (min-width:769px){.drawer-module__drawerHeader___nCPYX{padding:1.5rem 1.5rem 1rem}.drawer-module__drawerHeaderTitleOnly___Rfbs-{padding:1.5rem 1.5rem 1.25rem}.drawer-module__drawerFooter___qzUcs{grid-template-columns:auto auto;padding:1rem 2rem}}@media (max-width:768px){.drawer-module__backButton___aJHBC{display:none!important}.drawer-module__drawer___5ARuA,.drawer-module__drawer___5ARuA.drawer-module__large___dv-LS{min-width:unset;width:100vw}.drawer-module__drawerContent___EvGma{width:100vw}}"),(Ti=exports.DrawerSizeEnum||(exports.DrawerSizeEnum={}))[Ti.Default=0]="Default",Ti[Ti.Large=1]="Large",S(".dropdownMenu-module__dropdownMenuWrapper___Rlao7{[data-scope=menu][data-part=trigger]{align-items:center;border-radius:50%;cursor:pointer;display:flex;height:2rem;justify-content:center;transition:background-color .2s;width:2rem}[data-scope=menu][data-part=trigger]:hover{background-color:var(--color-bg-light-grey)}[data-scope=menu][data-part=trigger]:focus-visible{border:0;margin:0;outline:2px solid var(--color-secondary-cobalt);outline-offset:-3px}}.dropdownMenu-module__dropdownMenuPositioner___hus-7{z-index:var(--z-index-dropdown-menu)!important;[data-scope=menu][data-part=content]{border:1px solid var(--color-bg-grey);box-shadow:0 4px 4px 0 rgba(0,0,0,.25);display:block;min-width:15rem;outline:none;padding:0;position:static!important}[data-scope=menu][data-part=content][data-state=open]{display:block}[data-scope=menu][data-part=content][data-state=closed]{display:none}[data-scope=menu][data-part=item]{text-wrap-mode:nowrap;align-items:center;color:var(--color-primary-charcoal);cursor:pointer;display:flex;flex-direction:row;gap:1rem;height:auto;justify-content:start;padding:1rem;transition:background-color .3s linear;width:auto;svg{path{fill:var(--color-primary-charcoal)}}}[data-scope=menu][data-part=item]:first-of-type{border-top-left-radius:2px;border-top-right-radius:2px}[data-scope=menu][data-part=item]:last-of-type{border-bottom:none;border-bottom-left-radius:2px;border-bottom-right-radius:2px}[data-scope=menu][data-part=item]{&:not([data-disabled]):hover{background-color:var(--color-bg-light-grey)}&[data-disabled]{color:var(--color-bg-inactive-charcoal);cursor:not-allowed}}[data-scope=menu][data-part=content][data-state=closed]{opacity:0;transition:opacity .2s linear,visibility .2s linear}[data-scope=menu][data-part=content][data-state=open]{background-color:var(--color-bg-white);opacity:1;transition:opacity .2s linear,visibility .2s linear}[data-scope=menu][data-part=item].dropdownMenu-module__critical___HETIB{border-top:1px solid var(--color-primary-pale-charcoal);color:var(--color-secondary-burgundy);svg{path{fill:var(--color-secondary-burgundy)}}}}");var Ni,Si=function(e){var o=e.items,n=e.id,l=e.defaultOpen,i=void 0!==l&&l,c=e.onChange,s=e.closeOnSelect,_=void 0===s||s,m=e.iconName,u=void 0===m?"ellipses-icon":m,p=e.dataTestId,g=void 0===p?"uikit-dropdownMenu":p,h=e.role,f=e.ariaLabel,b=e.ariaDescribedBy,v=e.ariaLabelledBy,w=e.tabIndex,y=t.useState(!1),x=y[0],E=y[1],C=t.useRef(null),k=function(e){c(e)};return t.useEffect((function(){if(C.current){var e=C.current.closest(".dropdown-menu-container"),t=function(){E(!1)};if(e)return e.addEventListener("scroll",t),function(){e.removeEventListener("scroll",t)}}}),[]),a.createElement("div",{className:"dropdownMenu-module__dropdownMenuWrapper___Rlao7",ref:C,"data-testid":g,role:h,"aria-describedby":b,"aria-labelledby":v,tabIndex:w},a.createElement(d.Menu.Root,{id:n,defaultOpen:i,closeOnSelect:_,onOpenChange:function(e){E(e.open)},open:x,positioning:{placement:"bottom-end",fitViewport:!0,strategy:"fixed"}},a.createElement(d.Menu.Trigger,{"aria-label":f,onClick:function(e){return e.stopPropagation()}},a.createElement(nl,{iconName:u,size:20})),a.createElement(d.Menu.Positioner,{className:"dropdownMenu-module__dropdownMenuPositioner___hus-7"},a.createElement(d.Menu.Content,null,o.map((function(e,t){var o;return a.createElement(d.Menu.Item,{className:r((o={},o["dropdownMenu-module__critical___HETIB"]=e.isCritical,o)),key:t+"-"+n,value:e.value+"-"+n,"aria-label":e.ariaLabel?e.ariaLabel:e.label,id:e.value+"-"+n,onClick:function(t){e.isDisabled||(k(e),e.criticalHandler&&e.criticalHandler(t.target))},onTouchStart:function(){e.isDisabled||(k(e),e.criticalHandler&&e.criticalHandler())},disabled:e.isDisabled},e.iconName&&a.createElement(nl,{iconName:e.iconName,altText:e.iconName}),e.label)}))))))};S(".dropdownInput-module__dropdownWrapper___JoTSR{display:flex;flex-direction:column;position:relative}.dropdownInput-module__dropdownLabel___zBMUz{color:var(--color-primary-charcoal);display:block;font-size:var(--fontsize-default);font-weight:600;line-height:24px;margin-bottom:1rem}.dropdownInput-module__dropdownSelection___jf6DO{align-items:baseline;display:flex;flex-direction:column}.dropdownInput-module__dropdownPlaceholder___iIzvp{color:var(--color-bg-inactive-charcoal)}.dropdownInput-module__dropdownPlaceholder___iIzvp[data-focus]{font-size:var(--fontsize-body-small);left:1rem;line-height:var(--lineheight-body-small);position:absolute;top:.25rem}.dropdownInput-module__dropdownControl___6BhvZ{position:relative}.dropdownInput-module__dropdownControl___6BhvZ.dropdownInput-module__error___0HWK0{border:1px solid var(--color-primary-red)}.dropdownInput-module__input___zjlg-{height:3.5rem;padding-right:1.725rem;text-overflow:ellipsis;white-space:nowrap}.dropdownInput-module__inputSpinner___uHl1z{padding-right:2rem}.dropdownInput-module__inputError___2AU4-,.dropdownInput-module__inputError___2AU4-:focus-visible{border:none}.dropdownInput-module__dropdownIndicator___30FqC{align-items:center;cursor:pointer;display:flex;justify-content:center;position:absolute;right:1rem;top:50%;transform:translateY(-50%)}.dropdownInput-module__dropdownIndicator___30FqC.dropdownInput-module__disabled___OhoVW{pointer-events:none}.dropdownInput-module__dropdownTrigger___mk9de{align-items:center;border:0;display:flex;flex-direction:row;justify-content:space-between;padding:0;position:relative;width:100%;z-index:var(--z-index-dropdown-list-in-table)}.dropdownInput-module__arrowUp___U2y-A{transform:translateY(-50%) rotate(180deg)}@keyframes dropdownInput-module__fadeIn___5eDGh{0%{opacity:0}to{opacity:1}}.dropdownInput-module__dropdownItemsWrapper___Gxlcs{position:relative;z-index:var(--z-index-dropdown-list-in-table)}.dropdownInput-module__dropDownContent___1p3ql{border:1px solid var(--color-primary-pale-charcoal);border-radius:2px;box-shadow:0 4px 4px 0 rgba(0,0,0,.25);margin:0;max-height:15.625rem;overflow:hidden;padding:0;&.dropdownInput-module__largeContent___mSd-I{width:20.3rem}}.dropdownInput-module__dropdownItemsWrapper___Gxlcs.dropdownInput-module__hidden___lynPo{pointer-events:none;visibility:hidden}.dropdownInput-module__dropdownPositioner___HY2Uz.dropdownInput-module__hidden___lynPo{opacity:0;transition:opacity .2s,visibility .2s;visibility:hidden}.dropdownInput-module__dropdownPositioner___HY2Uz.dropdownInput-module__visible___K5xYr{animation:dropdownInput-module__fadeIn___5eDGh .2s}.dropdownInput-module__dropdownItem___reXWR{align-items:flex-start;background-color:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);box-sizing:border-box;color:var(--color-primary-charcoal);cursor:pointer;display:flex;flex:1 0 0;flex-direction:column;gap:.5rem;justify-content:center;min-height:48px;padding:0 1rem;transition:background-color .2s linear}.dropdownInput-module__noResults___oZ3Ue{background-color:var(--color-bg-light-grey);color:var(--color-primary-inactive-charcoal);padding:1rem;pointer-events:none}.dropdownInput-module__dropdownItem___reXWR.dropdownInput-module__itemWithDescription___Z-l5m{border:none;padding:.5rem 1rem;.dropdownInput-module__labelWithDescription___gVxUE{font-size:var(--fontsize-body);font-weight:700;line-height:var(--lineheight-h6)}.dropdownInput-module__itemDescription___o25kT{margin-bottom:.5rem}}.dropdownInput-module__dropdownItem___reXWR.dropdownInput-module__itemWithoutBorder___T919K{border:none}.dropdownInput-module__ctaOnNoResults___kG84s{color:var(--color-secondary-cobalt);font-weight:700;padding:1rem}.dropdownInput-module__customOption___RZlBO.dropdownInput-module__isSelected___4l0CX,.dropdownInput-module__dropdownItem___reXWR.dropdownInput-module__isSelected___4l0CX,.dropdownInput-module__dropdownItem___reXWR:hover,.dropdownInput-module__dropdownItem___reXWR[data-highlighted]{background-color:var(--color-bg-light-grey);transition:background-color .2s linear}.dropdownInput-module__customOption___RZlBO button{width:100%}.dropdownInput-module__titleContainer___sUKwW{align-items:center;display:flex;gap:.5rem}.dropdownInput-module__itemDescription___o25kT{display:block;margin-left:1.5rem}.dropdownInput-module__chipContainer___OT9-Q{background-color:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:2px;cursor:pointer;line-height:24px;padding:.938rem;text-align:left;width:100%;&.dropdownInput-module__selected___p1Crd{border:1px solid var(--color-primary-charcoal)}&:hover{background-color:var(--color-primary-soft-charcoal)}}.dropdownInput-module__itemChip___f7sNi{align-items:center;border:1px solid var(--color-primary-pale-charcoal);border-radius:6.25rem;display:flex;flex-direction:row;gap:.5rem;padding:.125rem .5rem;width:fit-content;&.dropdownInput-module__selectedChip___scxlN{background-color:#fff;left:1rem;position:absolute;top:50%;transform:translateY(-50%)}}.dropdownInput-module__errorMessage___hTG5p{align-items:center;color:var(--color-secondary-burgundy);display:flex;flex-direction:row;font-size:var(--fontsize-body-small);gap:.5rem;margin-top:.75rem;vertical-align:center}.dropdownInput-module__renderTop___lpFm8{top:-325px!important}.dropdownInput-module__isRequired___TWQ5z{color:var(--color-primary-red);font-size:var(--fontsize-body)}.dropdownInput-module__labelContainer___k4PuR{display:flex;gap:3px}@media (min-width:769px){.dropdownInput-module__renderTop___lpFm8{top:-308px!important}}.dropdownInput-module__dropdownScrollContainer___tfTUO{max-height:15rem;overflow-y:auto}.dropdownInput-module__fixedCTAContainer___hMBKd{background-color:var(--color-bg-white);border-top:1px solid var(--color-primary-pale-charcoal);bottom:0;padding:.5rem;position:sticky;z-index:1}.dropdownInput-module__customOption___RZlBO{background:none;border:none;cursor:pointer;padding:0;text-align:left;width:100%}"),(Ni=exports.TextButtonSizeEnum||(exports.TextButtonSizeEnum={})).large="large",Ni.medium="medium",Ni.small="small",Ni.inline="inline";var Bi={textButton:"textButton-module__textButton___9U0dQ",large:"textButton-module__large___VQs4j",medium:"textButton-module__medium___V2Rwv",small:"textButton-module__small___8HHgs",inline:"textButton-module__inline___Veuxb",iconWrapper:"textButton-module__iconWrapper___RF0W3"};S(".textButton-module__textButton___9U0dQ{align-items:center;background-color:transparent;border:none;color:var(--color-secondary-cobalt);cursor:pointer;display:flex;flex-direction:row;font-style:normal;font-weight:600;text-align:center}.textButton-module__textButton___9U0dQ:disabled,.textButton-module__textButton___9U0dQ:disabled:hover{color:var(--color-bg-inactive-charcoal);cursor:not-allowed;text-decoration:none;path{fill:var(--color-bg-inactive-charcoal)}}.textButton-module__textButton___9U0dQ:focus-visible{outline:2px solid var(--color-secondary-cobalt)}.textButton-module__textButton___9U0dQ.textButton-module__large___VQs4j,.textButton-module__textButton___9U0dQ.textButton-module__medium___V2Rwv{gap:.5rem}.textButton-module__textButton___9U0dQ.textButton-module__small___8HHgs{gap:.25rem}.textButton-module__textButton___9U0dQ.textButton-module__large___VQs4j{font-size:var(--fontsize-body)}.textButton-module__textButton___9U0dQ.textButton-module__medium___V2Rwv{font-size:var(--fontsize-body-small)}.textButton-module__textButton___9U0dQ.textButton-module__small___8HHgs{font-size:var(--fontsize-body-xsmall)}.textButton-module__textButton___9U0dQ.textButton-module__inline___Veuxb{font-size:var(--fontsize-body-small);padding:0}.textButton-module__textButton___9U0dQ:hover{text-decoration:underline}.textButton-module__iconWrapper___RF0W3{align-items:center;display:flex;height:100%;justify-content:center}");var Li=function(e){var t,o=e.id,n=e.size,l=e.onClick,i=e.label,c=e.disabled,d=void 0!==c&&c,s=e.includeChevron,_=void 0===s||s,m=e.iconName,u=void 0===m?"chevron":m,p=e.customIconSize,g=e.dataTestId,h=void 0===g?"uikit-textButton":g,f=e.role,b=e.ariaLabel,v=e.ariaDescribedBy,w=e.ariaLabelledBy,y=e.tabIndex,x=e.className;switch(n){case exports.TextButtonSizeEnum.large:t=20;break;case exports.TextButtonSizeEnum.medium:t=16;break;case exports.TextButtonSizeEnum.small:t=12;break;case exports.TextButtonSizeEnum.inline:default:t=16}return a.createElement("button",{disabled:d,id:o,className:r(Bi.textButton,Bi[n],n===exports.TextButtonSizeEnum.inline&&Bi.inline,x),onClick:l,"data-testid":h,role:f,"aria-label":b,"aria-describedby":v,"aria-labelledby":w,tabIndex:y},i,_&&a.createElement("div",{className:r(Bi.iconWrapper,Bi[n],d&&Bi.disabled)},a.createElement(nl,{iconName:u,altText:"chevron",size:null!=p?p:t})))},Oi=function(e){var o,n,l,i,s=e.items,_=void 0===s?[]:s,m=e.id,u=void 0===m?"":m,p=e.label,g=e.placeholder,h=e.defaultOpen,f=void 0!==h&&h,b=e.showNoResultsMessage,v=e.noResultsMessage,w=e.showCTA,y=void 0!==w&&w,x=e.ctaText,E=e.disabled,C=void 0!==E&&E,k=e.onSelectionChange,T=void 0===k?function(){}:k,I=e.error,z=void 0!==I&&I,N=e.onCTAClicked,S=void 0===N?function(){}:N,B=e.errorMessage,L=e.defaultSelection,O=void 0===L?null:L,A=e.dataTestId,D=void 0===A?"uikit-dropdownInput":A,j=e.ariaLabel,M=void 0===j?"":j,P=e.required,H=e.withIcon,R=void 0!==H&&H,V=e.floatingLabelEnabled,F=void 0===V||V,W=e.limitDropdownWidth,U=void 0===W||W,q=e.showAsChip,K=void 0!==q&&q,G=e.onInputChange,Y=e.renderTop,J=void 0!==Y&&Y,Z=e.enableSearch,X=void 0===Z||Z,Q=e.updatingSpinner,$=void 0!==Q&&Q,ee=e.thin,te=void 0!==ee&&ee,ae=e.leftIconPaddingLeft,re=e.dropdownPortalClass,oe=e.showRequiredIndicator,ne=void 0!==oe&&oe,le=e.truncateSelectedText,ie=void 0===le?20:le,ce=c.useTranslation("dropdown").t,de=t.useState(f),se=de[0],_e=de[1],me=t.useState((null==O?void 0:O.label)||""),ue=me[0],pe=me[1],ge=t.useState(O),he=ge[0],fe=ge[1],be=t.useState(!1),ve=be[0],we=be[1],ye=t.useRef(null),xe=!X||K,Ee=t.useMemo((function(){return O}),[O]),Ce=t.useRef(!1),ke=t.useMemo((function(){return ve?_.filter((function(e){var t;return null==(t=e.label)?void 0:t.toLowerCase().includes(ue.toLowerCase())})):_}),[_,ue,ve]);t.useEffect((function(){var e=function(e){var t=ye.current;se&&t&&!t.contains(e.target)&&_e(!1)};return window.addEventListener("scroll",e,!0),function(){return window.removeEventListener("scroll",e,!0)}}),[se,ye]),t.useEffect((function(){Ee&&!Ce.current&&(Ie(Ee),Ce.current=!0)}),[Ee]),t.useEffect((function(){null!=he&&he.label&&pe(Te(he.label))}),[he]);var Te=function(e){return e.length<=ie?e:e.substring(0,ie-3)+"..."},Ie=function(e){var t;e.translationKey&&!e.label||(fe(e),pe(Te(null!=(t=e.label)?t:"")),_e(!1),we(!1),T(e))},ze=function(e){if("new-option"===e)S();else{var t=ke.find((function(t){return t.value===e}));t&&Ie(t)}},Ne=d.useCombobox({id:u,items:y?[].concat(ke.map((function(e){return""+e.value})),["new-option"]):ke.map((function(e){return""+e.value})),open:se,onOpenChange:function(e){_e(e.open)},closeOnSelect:!0,openOnClick:!0,onValueChange:function(e){return ze(e.value[0])},disabled:C,positioning:{gutter:0,strategy:"fixed",fitViewport:!0,overlap:!0,flip:J,placement:"bottom-end",sameWidth:U},loopFocus:!0,allowCustomValue:y}),Se="string"==typeof(null==O?void 0:O.value)?O.value:null==O?void 0:O.label,Be=(null==Se?void 0:Se.replace(/[\s_]/g,"").toLowerCase())+"-icon";return a.createElement(d.Field.Root,{invalid:z},a.createElement(d.Combobox.RootProvider,{value:Ne,unmountOnExit:!0},p&&a.createElement("div",{className:"dropdownInput-module__labelContainer___k4PuR"},a.createElement(d.Combobox.Label,{className:r("dropdownInput-module__dropdownLabel___zBMUz"),onClick:function(e){return e.preventDefault()}},p),P&&a.createElement("span",{className:"dropdownInput-module__isRequired___TWQ5z"},"*")),a.createElement(d.Combobox.Control,{"data-testid":D,className:r("dropdownInput-module__dropdownControl___6BhvZ",(o={},o["dropdownInput-module__error___0HWK0"]=z,o))},a.createElement(d.Combobox.Input,{readOnly:xe,asChild:!0},a.createElement(d.Field.Input,{asChild:!0},a.createElement(Yl,{autoComplete:"off",required:P,showRequiredIndicator:ne,inputId:u,className:r("dropdown-input-field","dropdownInput-module__input___zjlg-",(n={},n["dropdownInput-module__inputError___2AU4-"]=z,n["dropdownInput-module__inputSpinner___uHl1z"]=$,n)),"aria-label":M,leftIconPaddingLeft:ae,value:ue,placeholder:g,onValueChange:function(e){pe(e),function(e){pe(e),we(e.length>0),e.trim()!==(null==he?void 0:he.label)&&fe(null)}(e),null==G||G(e)},disabled:C,enableSearch:X&&!K,withLeftIcon:R,leftIconName:Be,floatingLabelEnabled:F&&!K,thin:te,error:z,errorMessage:"",onClick:function(){se&&_e(!1)},onKeyDown:function(e){xe&&(se||"Enter"!==e.key&&" "!==e.key?" "===e.key&&(Ne.highlightedValue?ze(Ne.highlightedValue):_e(!1),e.preventDefault()):(_e(!0),e.preventDefault()))}}))),he&&K&&a.createElement(d.Combobox.Trigger,{asChild:!0},a.createElement("div",{className:r("dropdownInput-module__itemChip___f7sNi","dropdownInput-module__selectedChip___scxlN"),role:"button"},a.createElement(nl,{iconName:he.icon}),a.createElement("span",null,ue))),a.createElement(d.Combobox.Trigger,{asChild:!0},a.createElement("div",{className:r("dropdownInput-module__dropdownIndicator___30FqC",(l={},l["dropdownInput-module__arrowUp___U2y-A"]=se,l["dropdownInput-module__disabled___OhoVW"]=C,l)),role:"button"},$?a.createElement(dl,{isLoading:!0,background:"rgba(0,0,0,0.4)",type:exports.SpinnerType.DROPDOWN}):a.createElement(nl,{iconName:"dropdown-arrow-down"})))),a.createElement(d.Presence,{present:Ne.open},a.createElement(d.Combobox.Positioner,{className:r("dropdownInput-module__dropdownPositioner___HY2Uz",re),style:{zIndex:9999},ref:ye},a.createElement(d.Combobox.Content,{className:r("dropdownInput-module__dropDownContent___1p3ql",(i={},i["dropdownInput-module__largeContent___mSd-I"]=!U,i))},a.createElement("div",{className:"dropdownInput-module__dropdownScrollContainer___tfTUO"},ke.map((function(e){var t,o,n,l,i,c,s,_,m;return a.createElement(d.Combobox.Item,{key:""+e.value,item:e,className:r("dropdownInput-module__dropdownItem___reXWR","dropdown-item",(o={},o["dropdownInput-module__itemWithDescription___Z-l5m"]=!(null==(t=e.description)||!t.length),o["dropdownInput-module__itemWithoutBorder___T919K"]=K,o)),style:null!=e&&e.icon&&!e.useCustomStyle?{padding:"1rem",height:"auto"}:void 0},a.createElement(d.Combobox.ItemText,null,K?a.createElement("div",{className:"dropdownInput-module__itemChip___f7sNi"},a.createElement(nl,{iconName:e.icon}),a.createElement("span",null,e.label)):a.createElement(a.Fragment,null,a.createElement("div",{className:"dropdownInput-module__titleContainer___sUKwW"},(null==e?void 0:e.icon)&&a.createElement(nl,{iconName:e.icon}),a.createElement("span",{className:r((i={},i["dropdownInput-module__labelWithDescription___gVxUE"]=!(null==(n=e.description)||!n.length),i.labelWithDescription=!(null==(l=e.description)||!l.length),i))},e.label)),(null==(c=e.description)?void 0:c.length)&&a.createElement("div",{className:r("dropdownInput-module__itemDescription___o25kT","item-description")},a.createElement("span",{className:r((m={},m["dropdownInput-module__labelWithDescription___gVxUE"]=!(null==(s=e.description)||!s.length),m.labelWithDescription=!(null==(_=e.description)||!_.length),m))},e.label)))),a.createElement(d.Combobox.ItemIndicator,null))})),0===ke.length&&b&&a.createElement(d.Combobox.Item,{className:"dropdownInput-module__noResults___oZ3Ue",item:"no-results","aria-disabled":"true"},a.createElement(d.Combobox.ItemText,null,null!=v?v:ce("no-results")))),y&&a.createElement("div",{className:"dropdownInput-module__fixedCTAContainer___hMBKd"},a.createElement(d.Combobox.Item,{className:r("dropdownInput-module__customOption___RZlBO","dropdownInput-module__isSelected___4l0CX"),item:"new-option"},a.createElement(d.Combobox.ItemText,null,a.createElement(Li,{id:"CTA",label:null!=x?x:"",size:exports.TextButtonSizeEnum.large,onClick:function(){},iconName:"add-icon",customIconSize:12})))))))),a.createElement(d.Field.ErrorText,{id:u+"-error-message"},B&&B.length>0&&a.createElement("span",{className:"dropdownInput-module__errorMessage___hTG5p"},a.createElement(nl,{iconName:"error-message-icon",size:20}),a.createElement("span",null,B))))};S(".editProjectStages-module__container___hI2Tq{align-items:center;background:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:2px;display:flex;flex-direction:column;gap:.75rem;padding:.5rem 1rem}.editProjectStages-module__firstStage___v5K44{align-items:center;background:var(--color-bg-white);display:flex;gap:.75rem;justify-content:space-between;width:100%}.editProjectStages-module__expandButton___n4qqb{background:none;border:none;cursor:pointer;padding:0;svg{path{fill:var(--color-primary-inactive-charcoal)}}}.editProjectStages-module__details___-iDvU{align-items:flex-start;display:flex;flex-direction:column;width:100%}.editProjectStages-module__stageName___5etl4{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);font-weight:700}.editProjectStages-module__subStages___-zUPF{color:var(--color-primary-inactive-charcoal);font-size:var(--fontsize-body-xsmall)}.editProjectStages-module__statusDropdown___4eCZ-{width:100%;.dropdown-input-field{font-size:.875rem;height:2.25rem;padding:.5rem 2.5rem .5rem 1rem}.input-left-icon{left:1rem;top:.65rem}}.editProjectStages-module__statusDropdownWrapper___ren5n{.dropdown-item{gap:0;padding:.75rem 1rem}.item-description.item-description{font-size:.875rem;letter-spacing:-.1px;line-height:20px;margin-bottom:0}.labelWithDescription{font-weight:400}}.editProjectStages-module__subStageContainer___RSi83{padding:.5rem 0;width:100%}.editProjectStages-module__subStage___I3mw4{align-items:flex-start;background:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:4px;display:flex;margin-bottom:.5rem;min-height:48px;padding:.75rem;position:relative}.editProjectStages-module__subStage___I3mw4>button:last-child{flex:0 0 auto;margin-left:auto}.editProjectStages-module__substageNameField___1W2VW,.editProjectStages-module__substageStatusField___mJPvf,.editProjectStages-module__substageeDateField___1pdNj{display:block;height:auto;min-height:2.5rem;min-width:0;padding-left:.75rem;position:relative}.editProjectStages-module__substageNameField___1W2VW .dropdown-input-field.error,.editProjectStages-module__substageNameField___1W2VW .input-field.error,.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field.error,.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__editProjectDateInput___geE4i.editProjectStages-module__errorState___3O5HD{border-color:var(--color-primary-red)!important}.editProjectStages-module__substageNameField___1W2VW .error-icon,.editProjectStages-module__substageNameField___1W2VW .error-message,.editProjectStages-module__substageStatusField___mJPvf .error-icon,.editProjectStages-module__substageStatusField___mJPvf .error-message{display:none!important}.editProjectStages-module__substageNameField___1W2VW .input-field,.editProjectStages-module__substageNameField___1W2VW .input-wrapper,.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field,.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__editProjectDateInput___geE4i{align-items:center;box-sizing:border-box!important;display:flex;height:2.5rem!important;margin-bottom:0;max-height:2.5rem!important;min-height:2.5rem!important;vertical-align:top}.editProjectStages-module__substageNameField___1W2VW .dropdown-input-field,.editProjectStages-module__substageNameField___1W2VW .dropdown-wrapper,.editProjectStages-module__substageNameField___1W2VW [data-scope=select],.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field,.editProjectStages-module__substageStatusField___mJPvf .dropdown-wrapper,.editProjectStages-module__substageStatusField___mJPvf [data-scope=select]{height:2.5rem!important;min-height:2.5rem!important}.editProjectStages-module__substageNameField___1W2VW .dropdown-trigger,.editProjectStages-module__substageNameField___1W2VW .trigger,.editProjectStages-module__substageNameField___1W2VW [data-part=trigger],.editProjectStages-module__substageStatusField___mJPvf .dropdown-trigger,.editProjectStages-module__substageStatusField___mJPvf .trigger,.editProjectStages-module__substageStatusField___mJPvf [data-part=trigger]{align-items:center!important;box-sizing:border-box!important;display:flex!important;height:2.5rem!important;min-height:2.5rem!important}.editProjectStages-module__substageNameField___1W2VW .input-wrapper,.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-wrapper{font-size:.875rem!important;height:2.5rem!important;margin-bottom:0;max-height:2.5rem!important}.editProjectStages-module__substageNameField___1W2VW *{font-size:.875rem!important;line-height:1.25rem!important}.editProjectStages-module__substageNameField___1W2VW input{height:2.5rem!important;max-height:2.5rem!important;min-height:2.5rem!important}.editProjectStages-module__substageNameField___1W2VW .dropdown-input-field,.editProjectStages-module__substageNameField___1W2VW .input-field,.editProjectStages-module__substageNameField___1W2VW input{align-items:center!important;align-self:stretch!important;border-radius:2px!important;box-sizing:border-box!important;display:flex!important;flex:1 0 0!important;font-size:.875rem!important;gap:6px!important;line-height:1.25rem!important;padding:8px 12px!important}.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field{align-items:center!important;align-self:stretch!important;border-radius:2px!important;box-sizing:border-box!important;display:flex!important;flex:1 0 0!important;font-size:.875rem!important;gap:6px!important;line-height:1.25rem!important;padding:8px 2.2rem!important}.editProjectStages-module__substageStatusField___mJPvf .input-left-icon{left:1rem;top:.75rem}.editProjectStages-module__substageNameField___1W2VW .input-field,.editProjectStages-module__substageNameField___1W2VW .input-field input,.editProjectStages-module__substageNameField___1W2VW .input-wrapper input{font-family:inherit!important;font-size:.875rem!important;line-height:1.25rem!important}.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__editProjectDateInput___geE4i{align-items:center!important;align-self:stretch!important;border-radius:2px!important;box-sizing:border-box!important;display:flex!important;flex:1 0 0!important;font-size:.875rem!important;line-height:1.25rem!important;padding:8px 12px!important}.editProjectStages-module__substageNameField___1W2VW>:first-child,.editProjectStages-module__substageStatusField___mJPvf>:first-child,.editProjectStages-module__substageeDateField___1pdNj>:first-child{align-items:center;margin-bottom:0}.editProjectStages-module__substageNameField___1W2VW .editProjectStages-module__inlineErrorMessage___yy2Uc,.editProjectStages-module__substageStatusField___mJPvf .editProjectStages-module__inlineErrorMessage___yy2Uc,.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__inlineErrorMessage___yy2Uc{left:0;margin-bottom:0;margin-top:.25rem;position:relative;right:0;top:.25rem}.editProjectStages-module__inlineErrorMessage___yy2Uc{align-items:center;color:var(--color-primary-red);display:flex;flex-shrink:0;font-family:var(--primary-font-family);font-size:.875rem;font-weight:400;gap:.5rem;line-height:1.2;margin-bottom:0;margin-top:.65rem;min-height:1rem;overflow:hidden;position:relative;text-align:left;text-overflow:ellipsis;white-space:nowrap}.editProjectStages-module__subStageName___3-mEM{font-size:var(--fontsize-body-small);font-weight:700}.editProjectStages-module__deleteButton___zFJaK{align-items:center;background:none;border:none;border-radius:4px;cursor:pointer;display:flex;height:32px;justify-content:center;padding:.5rem;width:32px;&:hover{background-color:var(--color-bg-light-grey)}svg{height:16px;width:16px;path{fill:var(--color-primary-red)}}}.editProjectStages-module__addSubstageContainer___8Bh8e{display:inline-block;position:relative;z-index:1001}.editProjectStages-module__addSubstageButton___YeUVB{align-items:center;background:none;border:none;color:var(--color-secondary-cobalt);cursor:pointer;display:flex;font-size:.875rem;font-weight:600;gap:.5rem;&:hover{text-decoration:underline}svg{height:12px;width:12px;path{fill:var(--color-secondary-cobalt)}}}.editProjectStages-module__addSubstageDropdown___5DXN6{background:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:4px;box-shadow:0 4px 16px rgba(0,0,0,.15);max-width:250px;min-width:200px;overflow:visible;pointer-events:auto;position:fixed;z-index:99999}.editProjectStages-module__dropdownOption___lGUm-{background:none;border:none;color:var(--color-primary-charcoal);cursor:pointer;display:block;font-size:.875rem;padding:.75rem 1rem;pointer-events:auto;text-align:left;user-select:none;width:100%;&:hover{background-color:var(--color-bg-light-grey)}&:first-child{border-top-left-radius:4px;border-top-right-radius:4px}&:last-child{border-bottom-left-radius:4px;border-bottom-right-radius:4px}}.editProjectStages-module__dateTrigger___XUi2v{background:var(--color-bg-white)}.editProjectStages-module__editProjectDateInput___geE4i{align-items:center;border:1px solid var(--color-border-pale-grey);border-radius:2px;box-sizing:border-box;color:var(--color-primary-charcoal);display:flex;font-family:inherit;font-size:var(--fontsize-body);height:2.5rem;line-height:var(--lineheight-body);padding:.5rem .625rem .5rem 1rem;width:100%;&[data-state=open]{border-color:var(--color-primary-charcoal)}}.editProjectStages-module__datePlaceholder___6NSKk{align-self:center;font-family:var(--primary-font-family);font-size:var(--fontsize-body-small);font-weight:400;line-height:20px;margin-left:.875rem;text-align:start;width:7.75rem}.editProjectStages-module__dropdownArrow___Kfvf7{align-content:center}.editProjectStages-module__faded___Idd7y{color:var(--color-primary-inactive-charcoal)}.editProjectStages-module__dateInputError___8Avka{border-color:var(--color-primary-red)!important}.editProjectStages-module__customSubstageRow___dp1xu{background-color:var(--color-bg-white);border:1px solid var(--color-border-pale-grey)}.editProjectStages-module__errorMessage___uh1AC{background:var(--color-bg-white);border-radius:2px;box-shadow:0 1px 3px rgba(0,0,0,.1);color:var(--color-primary-red);font-size:.75rem;left:0;margin-top:.25rem;padding:.125rem .25rem;position:absolute;top:100%;white-space:nowrap;z-index:10}.editProjectStages-module__container___hI2Tq [data-scope=date-picker][data-part=positioner]{position:fixed!important;z-index:9999!important}.editProjectStages-module__container___hI2Tq [data-scope=date-picker][data-part=content]{position:relative!important;z-index:9999!important}.editProjectStages-module__subStageContainer___RSi83 [data-scope=date-picker][data-part=positioner]{position:fixed!important;z-index:9999!important}.editProjectStages-module__subStageContainer___RSi83 [data-scope=date-picker][data-part=content]{position:relative!important;z-index:9999!important}.editProjectStages-module__subStage___I3mw4{cursor:grab;transition:all .2s ease}.editProjectStages-module__subStage___I3mw4:active{cursor:grabbing}.editProjectStages-module__subStage___I3mw4.editProjectStages-module__dragging___taGva{cursor:grabbing;opacity:.5;transform:rotate(2deg);z-index:1000}.editProjectStages-module__subStage___I3mw4.editProjectStages-module__dragOver___--I-Z{background-color:#f2f7fb;border-color:var(--color-secondary-cobalt);border-width:2px;box-shadow:0 4px 8px rgba(0,0,0,.1);transform:translateY(-2px)}.editProjectStages-module__dragHandle___bF7Zu{align-items:center;color:var(--color-primary-inactive-charcoal);cursor:grab;display:flex;flex:0 0 auto;height:42px;justify-content:center;transition:color .2s ease;width:16px}.editProjectStages-module__dragHandle___bF7Zu:hover{color:var(--color-primary-charcoal)}.editProjectStages-module__subStage___I3mw4:active .editProjectStages-module__dragHandle___bF7Zu{cursor:grabbing}");var Ai={control:"toggle-module__control___azYKw",thumb:"toggle-module__thumb___REMff",root:"toggle-module__root___V61o8"};S(".toggle-module__control___azYKw{align-items:center;background-color:var(--color-bg-grey);border:1px solid var(--color-border-grey);border-radius:9999px;box-sizing:border-box;cursor:pointer;display:inline-flex;height:1.25rem;padding:.0625rem;transition:background-color .2s ease;width:2.25rem}.toggle-module__control___azYKw:is(:checked,[data-checked],[aria-checked=true],[data-state=checked]){background-color:var(--color-primary-red);border:1px solid var(--color-primary-red)}.toggle-module__control___azYKw:is(:disabled,[data-disabled]){background-color:var(--color-bg-white);border:1px solid var(--color-border-grey);cursor:not-allowed;&[data-state=checked],[aria-checked=true],[data-checked],[data-state=checked]{background-color:var(--color-primary-red-disabled);border:1px solid transparent}}.toggle-module__thumb___REMff{background-color:var(--color-bg-white);border-radius:9999px;height:1rem;transition:transform .2s cubic-bezier(.2,0,0,1);width:1rem}.toggle-module__thumb___REMff:is(:checked,[data-checked],[aria-checked=true],[data-state=checked]){transform:translateX(100%)}.toggle-module__thumb___REMff:is(:disabled,[data-disabled]){background-color:var(--color-bg-grey);&[data-state=checked],[aria-checked=true],[data-checked],[data-state=checked]{background-color:var(--color-bg-light-grey)}}.toggle-module__root___V61o8{display:grid;padding:1px;position:relative}.toggle-module__root___V61o8:is(:focus-visible,[data-focus]){border:2px solid var(--color-secondary-cobalt);border-radius:100px;margin:-2px}");var Di=function(e){var r=e.isDefaultChecked,o=e.onCheckedChange,n=e.isDisabled,l=e.dataTestId,i=void 0===l?"uikit-toggle":l,c=e.ariaLabel,s=e.ariaDescribedBy,_=e.ariaLabelledBy,m=e.tabIndex,u=t.useState(r),p=u[0],g=u[1];return t.useEffect((function(){g(r)}),[r]),a.createElement(d.Switch.Root,{className:Ai.root,checked:p,onCheckedChange:function(e){g(e.checked),o&&o(e.checked)},disabled:n,"data-testid":i,tabIndex:m},a.createElement(d.Switch.Control,{className:Ai.control},a.createElement(d.Switch.Thumb,{className:Ai.thumb})),a.createElement(d.Switch.Label,{className:Ai.label}),a.createElement(d.Switch.HiddenInput,{className:Ai.input,"aria-label":c,"aria-describedby":s,"aria-labelledby":_}))},ji=[{value:"upcoming",translationKey:"project-timeline-status-upcoming",icon:"upcoming-icon"},{value:"in_progress",translationKey:"project-timeline-status-in-progress",icon:"inprogress-icon"},{value:"delayed",translationKey:"project-timeline-status-delayed",icon:"delayed-icon"},{value:"complete",translationKey:"project-timeline-status-complete",icon:"complete-icon"}],Mi={inlineMessage:"inlineMessage-module__inlineMessage___aQzHi",inlineMessageText:"inlineMessage-module__inlineMessageText___lRxYi",inlineMessageContent:"inlineMessage-module__inlineMessageContent___wqxmV",inlineMessageNoTitle:"inlineMessage-module__inlineMessageNoTitle___0lvqp",inlineMessageTitle:"inlineMessage-module__inlineMessageTitle___bxmKx",warning:"inlineMessage-module__warning___zqS8v",success:"inlineMessage-module__success___ODPRZ",info:"inlineMessage-module__info___otBPt",error:"inlineMessage-module__error___PzwoK"};S(".inlineMessage-module__inlineMessage___aQzHi{align-items:center;border-left:4px solid var(--color-secondary-gold);border-radius:1px;box-shadow:0 4px 8px 0 #00000026;color:var(--color-primary-light-charcoal);display:flex;gap:1rem;padding:.75rem 1rem 1rem}p.inlineMessage-module__inlineMessageText___lRxYi{font-size:var(--fontsize-body-small)}.inlineMessage-module__inlineMessageContent___wqxmV{display:flex;flex-direction:column;gap:.5rem}.inlineMessage-module__inlineMessageNoTitle___0lvqp{padding-top:0}.inlineMessage-module__inlineMessageTitle___bxmKx{font-size:var(--fontsize-default);font-weight:600;line-height:var(--lineheight-body)}.inlineMessage-module__inlineMessageText___lRxYi,.inlineMessage-module__inlineMessageTitle___bxmKx{margin:0}.inlineMessage-module__warning___zqS8v{border-left:4px solid var(--color-secondary-gold)}.inlineMessage-module__success___ODPRZ{border-left:4px solid var(--color-secondary-jade)}.inlineMessage-module__info___otBPt{border-left:4px solid var(--color-secondary-ocean)}.inlineMessage-module__error___PzwoK{border-left:4px solid var(--color-secondary-burgundy)}");var Pi={info:"info-icon",warning:"warning-icon-solid",success:"success-checkmark",error:"error-icon"},Hi={comments:"comments-module__comments___8sM2I",header:"comments-module__header___OGCpV",commentsContainer:"comments-module__commentsContainer___KBdeL",comment:"comments-module__comment___n8j7i",commentHeader:"comments-module__commentHeader___S-oE6",buttonsContainer:"comments-module__buttonsContainer___Z0wvE",newComment:"comments-module__newComment___AnMt-",newCommentAvatar:"comments-module__newCommentAvatar___rr4AR",newCommentEditor:"comments-module__newCommentEditor___-deDS",commentData:"comments-module__commentData___YluMV",commentContainer:"comments-module__commentContainer___dlnZV",author:"comments-module__author___dbcY7",timestamp:"comments-module__timestamp___vyeng",title:"comments-module__title___mo-8H",commentText:"comments-module__commentText___hdr1J",noCommentsContainer:"comments-module__noCommentsContainer___Koujq",noCommentsSection:"comments-module__noCommentsSection___j8e3-",noCommentsText:"comments-module__noCommentsText___sM1dK",spinner:"comments-module__spinner___Keuve",addCommentButton:"comments-module__addCommentButton___nQbK-"};S(".comments-module__comments___8sM2I{background-color:var(--color-bg-white);display:flex;flex-direction:column;max-width:100%}.comments-module__header___OGCpV{border-bottom:1px solid var(--color-primary-pale-charcoal);border-top:1px solid var(--color-primary-pale-charcoal);outline:none;padding:.75rem 1.5rem}.comments-module__commentsContainer___KBdeL{display:flex;flex-direction:column;gap:1.5rem;padding:1.5rem 0}.comments-module__comment___n8j7i{align-items:flex-start;display:flex;gap:1rem;padding:0 2rem}.comments-module__commentHeader___S-oE6{align-items:center;display:flex;gap:1rem;justify-content:start}.comments-module__buttonsContainer___Z0wvE{display:flex;gap:.5rem;margin-left:auto}.comments-module__newComment___AnMt-{align-items:flex-start;display:flex;gap:1rem;padding:1.5rem 2rem}.comments-module__newCommentAvatar___rr4AR{flex-shrink:0}.comments-module__newCommentEditor___-deDS{display:flex;flex-direction:column;flex-grow:1;min-width:0}.comments-module__commentData___YluMV{margin-top:0;width:100%}.comments-module__commentContainer___dlnZV{display:flex;flex-direction:column;gap:.5rem}.comments-module__author___dbcY7{color:var(--color-primary-light-charcoal);font-weight:600}.comments-module__author___dbcY7,.comments-module__timestamp___vyeng{font-size:var(--fontsize-body-small);line-height:var(--lineheight-body);margin:0}.comments-module__timestamp___vyeng{color:var(--color-bg-inactive-charcoal);font-weight:400}.comments-module__title___mo-8H{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body)}.comments-module__commentText___hdr1J{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body);white-space:preserve-breaks}.comments-module__noCommentsContainer___Koujq{padding:1rem}.comments-module__noCommentsSection___j8e3-{align-items:center;background-color:var(--color-primary-soft-charcoal);border-radius:2px;display:flex;flex-direction:column;gap:.5rem;justify-content:center;padding:1.5rem;svg{color:var(--color-bg-primary)}}.comments-module__noCommentsText___sM1dK{color:var(--color-primary-inactive-charcoal);font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body)}.comments-module__spinner___Keuve{align-items:center;display:flex;justify-content:center;padding:1.5rem 2rem}.comments-module__addCommentButton___nQbK-{button{&:hover{background-color:var(--color-primary-slate)}}}");var Ri="toolbar-module__button___7QhCD",Vi="toolbar-module__active___Be41P";S(".toolbar-module__toolbar___bWE5w{background-color:var(--color-bg-white);border-bottom:1px solid var(--color-border-grey);display:flex;gap:.5rem;padding:.75rem}.toolbar-module__button___7QhCD{background-color:transparent;border:1px solid var(--color-border-grey);border-radius:4px;cursor:pointer;font-family:var(--primary-font-family);padding:.5rem}.toolbar-module__button___7QhCD.toolbar-module__active___Be41P,.toolbar-module__button___7QhCD:hover{background-color:var(--color-bg-primary-pale-charcoal);border-color:var(--color-border-primary-charcoal)}");var Fi=function(e){var r=e.onModeToggle,o=e.dataTestId,n=void 0===o?"uikit-toolbar":o,l=e.role,i=e.ariaLabel,c=void 0===i?"Toolbar for WYSIWYG Editor":i,d=e.ariaDescribedBy,s=e.ariaLabelledBy,_=e.tabIndex,m=z.useLexicalComposerContext()[0],u=t.useState(!1),p=u[0],g=u[1],h=t.useState(!1),f=h[0],b=h[1],v=t.useState(!1),x=v[0],C=v[1],k=t.useState(!1),T=k[0],I=k[1],N=t.useCallback((function(){var e=y.$getSelection();y.$isRangeSelection(e)&&(g(e.hasFormat("bold")),b(e.hasFormat("italic")),C(e.hasFormat("underline")))}),[]);t.useEffect((function(){return m.registerUpdateListener((function(e){e.editorState.read(N)}))}),[m,N]);var S=function(e){m.dispatchCommand(y.FORMAT_TEXT_COMMAND,e)};return a.createElement("div",{className:"toolbar-module__toolbar___bWE5w","data-testid":n,role:l,"aria-label":c,"aria-describedby":d,"aria-labelledby":s,tabIndex:_},a.createElement("button",{onClick:function(){return S("bold")},className:Ri+" "+(p?Vi:"")},"Bold"),a.createElement("button",{onClick:function(){return S("italic")},className:Ri+" "+(f?Vi:"")},"Italic"),a.createElement("button",{onClick:function(){return S("underline")},className:Ri+" "+(x?Vi:"")},"Underline"),a.createElement("button",{onClick:function(){m.update((function(){var e=y.$getSelection();if(y.$isRangeSelection(e))for(var t=e.anchor.getNode().getParent();t;){if("list"===t.getType()&&t instanceof E.ListNode&&"bullet"===t.getListType())return void m.dispatchCommand(E.REMOVE_LIST_COMMAND,void 0);t=t.getParent()}m.dispatchCommand(E.INSERT_UNORDERED_LIST_COMMAND,void 0)}))},className:Ri},"Bullet List"),a.createElement("button",{onClick:function(){T&&m.update((function(){var e=(new DOMParser).parseFromString(m.getEditorState().read((function(){return w.$generateHtmlFromNodes(m)})),"text/html"),t=y.$getRoot();t.clear();var a=w.$generateNodesFromDOM(m,e);t.append.apply(t,a)})),I((function(e){var t=!e;return r(t),t}))},className:Ri+" "+(T?Vi:"")},T?"Switch to Visual":"Switch to HTML"))};S(":root{--footer-actions-height:2.5rem}.wysywygeditor-module__wrapper___wR5YK{display:flex;flex-direction:column;gap:.5rem}.wysywygeditor-module__editorContainer___uR1nw{background-color:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:4px;padding:.5rem;position:relative;width:100%;&:hover{background-color:var(--color-primary-soft-charcoal)}}.wysywygeditor-module__editorContainer--hasContent___SLNRm{background-color:var(--color-bg-white);border:1px solid var(--color-primary-charcoal)}.wysywygeditor-module__editorContainer___uR1nw:focus-within{border:1px solid var(--color-primary-charcoal);&.focusedByKeyboard{outline:2px solid var(--color-secondary-cobalt)}}.wysywygeditor-module__editorContainer--error___SDjzK,.wysywygeditor-module__editorContainer--error___SDjzK:focus-within{border:1px solid var(--color-secondary-burgundy)}.wysywygeditor-module__contentEditable___H2Gda{font-size:var(--fontsize-body);max-height:calc(20rem - var(--footer-actions-height));min-height:1.5rem;outline:none;overflow:auto}.wysywygeditor-module__contentEditable___H2Gda.wysywygeditor-module__smallText___TbgXK{font-size:var(--fontsize-body-small)}.wysywygeditor-module__placeholder___rMn1Z{color:var(--color-primary-inactive-charcoal);font-size:var(--fontsize-body-small);left:.5rem;line-height:var(--lineheight-body);pointer-events:none;position:absolute;top:.5rem}.wysywygeditor-module__htmlEditor___qzpoE{background-color:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:4px;box-sizing:border-box;color:var(--color-primary-charcoal);font-family:monospace;font-size:var(--fontsize-body);height:200px;line-height:1.5;outline:none;padding:.75rem;resize:none;width:100%}.wysywygeditor-module__htmlEditor___qzpoE:focus{outline:none;&.focusedByKeyboard{border-color:var(--color-secondary-cobalt);box-shadow:0 0 3px rgba(0,119,204,.8)}}.wysywygeditor-module__footerActions___kbybp{display:flex;gap:1rem;height:var(--footer-actions-height);justify-content:end}.wysywygeditor-module__characterCount___JXPxz{color:var(--color-primary-inactive-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small);margin-top:.5rem}.wysywygeditor-module__characterCountError___huEaM{color:var(--color-secondary-burgundy)}");var Wi={placeholder:"editor-placeholder",paragraph:"editor-paragraph",list:{ul:"editor-list-ul",listitem:"editor-listitem"},text:{bold:"editor-text-bold",italic:"editor-text-italic",underline:"editor-text-underline"}};S(".editor-placeholder{color:#999;font-style:italic;pointer-events:none}.editor-paragraph{color:var(--color-primary-light-charcoal);font-size:inherit;line-height:var(--lineheight-body);margin:0}.editor-list-ul{list-style-type:disc;margin-left:20px}.editor-listitem{margin-bottom:.5rem}.editor-text-bold{font-weight:700}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.mention-at{border-radius:4px;padding:2px 4px}.mention-at,.mention-at-focused{background-color:var(--color-bg-primary-pale-charcoal);color:var(--color-bg-primary-charcoal)}.mention-hash{border-radius:4px;padding:2px 4px}.mention-hash,.mention-hash-focused{background-color:#e81a3b3b;color:var(--color-primary-red)}");var Ui={"@":"mention-at","@Focused":"mention-at-focused","#":"mention-hash","#Focused":"mention-hash-focused"};S(".mentionmenu-module__mentionsMenu___c6r4z{background:var(--color-primary-white);border:1px solid var(--color-bg-primary-pale-charcoal);border-radius:8px;box-shadow:0 4px 6px rgba(0,0,0,.1);list-style:none;margin:0;max-height:200px;overflow-y:auto;padding:8px;width:300px}.mentionmenu-module__mentionsLoading___sRo4F{color:var(--color-bg-primary-pale-charcoal);font-style:italic;padding:8px;text-align:center}.mentionmenu-module__mentionsItem___HoQiJ{font-size:var(--fontsize-body-xsmall);padding:8px 12px;transition:background-color .2s ease-in-out,color .2s ease-in-out}.mentionmenu-module__mentionsItem___HoQiJ:hover{background-color:var(--color-bg-primary-pale-charcoal);color:#000}.mentionmenu-module__mentionsItemSelected___XEwGm{background-color:var(--color-secondary-cobalt);color:var(--color-primary-white)}");var qi=["loading"];function Ki(e){var t=e.loading,r=rt(e,qi);return a.createElement("ul",Object.assign({className:"mentionmenu-module__mentionsMenu___c6r4z"},r),t&&a.createElement("li",{className:"mentionmenu-module__mentionsLoading___sRo4F"},"Loading..."),r.children)}var Gi=["selected","item"],Yi=t.forwardRef((function(e,t){var r=e.selected,o=e.item,n=rt(e,Gi),l=o.displayValue;return a.createElement("li",Object.assign({className:"mentionmenu-module__mentionsItem___HoQiJ "+(r?"mentionmenu-module__mentionsItemSelected___XEwGm":"")},n,{ref:t}),l)}));Yi.displayName="CustomMenuItem";var Ji=function(e){var t=e.value,r=void 0===t?"":t,o=e.onChange,n=e.onContentSave,l=e.showToolbar,i=void 0!==l&&l,c=e.footerBtnConfig,d=e.dataTestId,s=void 0===d?"uikit-WYSIWYGEditor":d,_=e.role,u=e.ariaLabel,p=e.ariaDescribedBy,g=e.ariaLabelledBy,h=e.tabIndex,f=e.placeholder,b=e.textSize,v=e.onFocus,x=e.onBlur,z=e.maxCharacterCount,S=at({},Wi,{beautifulMentions:Ui});return a.createElement("div",{"data-testid":s,role:_,tabIndex:h},a.createElement(m.LexicalComposer,{initialConfig:{namespace:"UniversalEditor",theme:S,onError:function(e){return console.error("Lexical Error:",e)},nodes:[y.TextNode,E.ListNode,E.ListItemNode,C.HorizontalRuleNode,k.HeadingNode,k.QuoteNode,T.CodeNode,I.LinkNode,I.AutoLinkNode,N.BeautifulMentionNode],editorState:function(e){if(r){r="<div>"+r.replaceAll("\n","<br/>")+"</div>";var t=(new DOMParser).parseFromString(r,"text/html"),a=y.$getRoot();a.clear();var o=w.$generateNodesFromDOM(e,t);a.append.apply(a,o)}}},"aria-describedby":p,"aria-labelledby":g},a.createElement(Zi,{ariaLabel:u,onChange:o,onContentSave:n,showToolbar:i,footerBtnConfig:c,placeholder:f,value:r,textSize:b,onFocus:v,onBlur:x,maxCharacterCount:z})))},Zi=function(e){var o,n,l,i=e.onChange,c=e.showToolbar,d=e.footerBtnConfig,s=e.placeholder,_=e.value,m=e.textSize,E=e.maxCharacterCount,C=e.onFocus,k=e.onBlur,T=e.ariaLabel,I=z.useLexicalComposerContext()[0],S=t.useState(!1),B=S[0],L=S[1],O=t.useState(""),A=O[0],D=O[1],j=t.useState(!1),M=j[0],P=j[1],H=t.useState(0),R=H[0],V=H[1],F=gl().focusClass;t.useEffect((function(){I.registerCommand(y.FOCUS_COMMAND,(function(){return P(!0),!1}),y.COMMAND_PRIORITY_LOW)}),[]),t.useEffect((function(){I.registerCommand(y.BLUR_COMMAND,(function(){return P(!1),!1}),y.COMMAND_PRIORITY_LOW)}),[]),t.useEffect((function(){M&&C&&C(),!M&&k&&k()}),[M]);var W=a.createElement("div",{className:"wysywygeditor-module__placeholder___rMn1Z"},s);return a.createElement("div",null,a.createElement("div",{className:"wysywygeditor-module__wrapper___wR5YK"},c&&a.createElement(Fi,{onModeToggle:function(e){L(e)}}),a.createElement("div",{className:r("wysywygeditor-module__editorContainer___uR1nw",F,(o={},o["wysywygeditor-module__editorContainer--hasContent___SLNRm"]=_,o["wysywygeditor-module__editorContainer--error___SDjzK"]=_&&E&&_.length>E,o))},B?a.createElement("textarea",{className:r("wysywygeditor-module__htmlEditor___qzpoE",F),value:A,onChange:function(e){D(e.target.value)}}):a.createElement(a.Fragment,null,a.createElement(u.RichTextPlugin,{contentEditable:a.createElement(p.ContentEditable,{className:r("wysywygeditor-module__contentEditable___H2Gda",(n={},n["wysywygeditor-module__smallText___TbgXK"]="small"===m,n)),"aria-placeholder":s||"",ariaLabel:T,placeholder:W}),ErrorBoundary:g.LexicalErrorBoundary}),a.createElement(h.OnChangePlugin,{onChange:function(e){e.read((function(){var e=y.$getRoot().getTextContent().replace(/\n\n/g,"\n");V(e.length),void 0!==E&&e.length<=E&&i&&i(e),D(w.$generateHtmlFromNodes(I,null))}))}}),a.createElement(f.HistoryPlugin,null),a.createElement(b.ListPlugin,null),a.createElement(N.BeautifulMentionsPlugin,{items:{"@":["Anton","Boris","Catherine","Dmitri","Elena","Felix","Gina"],"#":["Action Item","Task","Project","Date"]},menuComponent:Ki,menuItemComponent:Yi}),a.createElement(v.MarkdownShortcutPlugin,null),void 0!==E&&a.createElement((function(e){var a=e.maxCharacterLimit,r=z.useLexicalComposerContext()[0],o=["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown"];return t.useEffect((function(){return x.mergeRegister(r.registerCommand(y.KEY_DOWN_COMMAND,(function(e){return r.getEditorState().read((function(){var t=y.$getRoot().getTextContent().length>=a&&!o.includes(e.key);return t&&e.preventDefault(),t}))}),y.COMMAND_PRIORITY_HIGH),r.registerCommand(y.PASTE_COMMAND,(function(e){var t,o,n=null!=(t=null==(o=e.clipboardData)?void 0:o.getData("text/plain"))?t:"";return r.getEditorState().read((function(){var t=y.$getRoot().getTextContent().length+n.length>a;return t&&e.preventDefault(),t}))}),y.COMMAND_PRIORITY_HIGH))}),[r,a]),null}),{maxCharacterLimit:E})),a.createElement("div",{className:"wysywygeditor-module__footerActions___kbybp"},(null==d?void 0:d.secondaryButton)&&a.createElement("div",{className:"wysywygeditor-module__footerActions___kbybp"},d.secondaryButton),(null==d?void 0:d.primaryButton)&&a.createElement("div",{className:"wysywygeditor-module__footerActions___kbybp"},d.primaryButton)))),void 0!==E&&(M||R>0)&&a.createElement("div",{className:r("wysywygeditor-module__characterCount___JXPxz",(l={},l["wysywygeditor-module__characterCountError___huEaM"]=R>E,l))},R," / ",E))},Xi=function(e){var r=e.text,o=e.createdOn,n=e.createdBy,l=e.canEdit,i=e.onCommentDelete,d=e.id,s=e.taskId,_=e.dataTestId,m=void 0===_?"uikit-comment":_,u=e.role,p=e.ariaLabel,g=void 0===p?"Comment":p,h=e.ariaDescribedBy,f=e.ariaLabelledBy,b=e.tabIndex,v=e.onCommentUpdate,w=t.useState(!1),y=w[0],x=w[1],E=t.useState(r),C=E[0],k=E[1],T=Rl(exports.ThemeColor.SecondaryBurgundy),I=c.useTranslation("global"),z=I.t,N=I.i18n;return a.createElement("div",{className:Hi.comment,"data-testid":m,role:u,"aria-label":g,"aria-describedby":h,"aria-labelledby":f,tabIndex:b},a.createElement("div",{className:Hi.avatarWrapper},a.createElement(Vl,{type:"monogram",initials:El(null==n?void 0:n.name),avatarSize:"small",fontSize:"x-small"})),a.createElement("div",{className:Hi.commentData},y?a.createElement(Ji,{onChange:function(e){k(e)},onContentSave:function(){},value:C,maxCharacterCount:1e3,textSize:"small",footerBtnConfig:{secondaryButton:a.createElement(_l,{id:"tertiary-btn",type:exports.ButtonTypeEnum.tertiary,size:exports.ButtonSizeEnum.small,label:z("cancel"),withRightIcon:!1,onClick:function(){x(!1)}}),primaryButton:a.createElement(_l,{id:"primary-btn",type:exports.ButtonTypeEnum.primary,size:exports.ButtonSizeEnum.small,label:z("save"),onClick:function(){C&&v(d,s,C)},withRightIcon:!1,disabled:!C||C.length>1e3})}}):a.createElement("div",{className:Hi.commentContainer},a.createElement("div",{className:Hi.commentHeader},a.createElement("p",{className:Hi.author},null==n?void 0:n.name),a.createElement("p",{className:Hi.timestamp},bl(o,N.language,!0,!0)),l&&a.createElement("div",{className:Hi.buttonsContainer},a.createElement(mi,{ariaLabel:"Edit button",size:exports.CircleButtonIconSizeEnum.small,icon:a.createElement(nl,{altText:"edit button",size:16,iconName:"edit-icon"}),inputId:"circle-button",onClick:function(){x(!0)}}),a.createElement(mi,{ariaLabel:"Delete button",size:exports.CircleButtonIconSizeEnum.small,icon:a.createElement(nl,{altText:"delete button",iconName:"delete-icon",color:null!=T?T:"",size:16}),inputId:"circle-button",onClick:function(){i(d,""+s)}}))),a.createElement("div",{className:Hi.commentText,dangerouslySetInnerHTML:{__html:r}}))))};S(".inviteUserCard-module__card___luRb0{align-items:center;border-bottom:1px solid var(--color-border-light-grey);cursor:pointer;display:flex;gap:.75rem;padding:.75rem 1rem;transition:background-color .2s}.inviteUserCard-module__card___luRb0:first-of-type{border-top-left-radius:4px;border-top-right-radius:4px}.inviteUserCard-module__card___luRb0:last-of-type{border-bottom-left-radius:4px;border-bottom-right-radius:4px}.inviteUserCard-module__card___luRb0:hover{background-color:var(--color-bg-light-grey)}.inviteUserCard-module__userInfo___qoTl9{display:flex;flex-direction:column;font-size:var(--fontsize-body-small);line-height:var(--lineheight-body)}.inviteUserCard-module__userName___9uzgQ{color:var(--color-primary-charcoal);font-weight:600}.inviteUserCard-module__userEmail___6tbGe{color:var(--color-primary-inactive-charcoal)}.inviteUserCard-module__unassignedUser___A2wXc{background-color:var(--color-bg-grey);border-radius:50%;padding:.25rem}");var Qi=function(e){var t,o=e.name,n=e.email,l=e.onSelect,i=e.dataTestId,c=void 0===i?"uikit-inviteUserCard":i,d=e.role,s=e.ariaDescribedBy,_=e.ariaLabelledBy,m=e.tabIndex,u=e.isNewUser;return a.createElement("div",{className:r("inviteUserCard-module__card___luRb0",e.className),onMouseDown:l,"data-testid":c,role:d,"aria-describedby":s,"aria-labelledby":_,tabIndex:m},a.createElement("div",{className:r((t={},t["inviteUserCard-module__unassignedUser___A2wXc"]=u,t))},a.createElement(Vl,Object.assign({initials:El(o),avatarSize:"small",fontSize:"x-small",type:u?"icon":"monogram"},u?{iconName:"person-icon"}:{}))),a.createElement("div",{className:"inviteUserCard-module__userInfo___qoTl9"},o&&a.createElement("span",{className:"inviteUserCard-module__userName___9uzgQ"},o),n&&a.createElement("span",{className:"inviteUserCard-module__userEmail___6tbGe"},n)))},$i={link:"menuitem-module__link___mNcXf",active:"menuitem-module__active___O2srh",longLabel:"menuitem-module__longLabel___HsARv"};S(".menuitem-module__link___mNcXf{align-items:center;border-radius:2px;color:var(--color-bg-white);display:flex;font-size:14px;gap:.75rem;padding:.75rem;text-decoration:none;transition:background-color .2s;width:100%;&:hover{background-color:var(--color-primary-light-charcoal);color:var(--color-bg-white)}&:focus-visible{outline-color:var(--color-outline-dark-bg);outline-offset:-3px}}.menuitem-module__link___mNcXf i{font-size:18px;margin-right:8px}.menuitem-module__active___O2srh{background-color:var(--color-primary-light-charcoal);border-left:1px solid #e81a3b;border-radius:2px;color:var(--color-bg-white)}.menuitem-module__longLabel___HsARv{hyphens:auto;white-space:normal;word-break:break-word}");var ec,tc,ac,rc,oc={modalWrapper:"modal-module__modalWrapper___HEHwS",overlay:"modal-module__overlay___PR8ec",truncateTitle:"modal-module__truncateTitle___12esQ",modal:"modal-module__modal___u3COr",modalContent:"modal-module__modalContent___22CDu",modalHeader:"modal-module__modalHeader___TxZbn","modalHeader--withBorder":"modal-module__modalHeader--withBorder___B5pEv",modalHeaderTextContainer:"modal-module__modalHeaderTextContainer___0lMRZ",modalTitleContainer:"modal-module__modalTitleContainer___GKoJq",modalTitle:"modal-module__modalTitle___J9EuI",padTitle:"modal-module__padTitle___KJreS",modalSubtitle:"modal-module__modalSubtitle___9xEUS",informationContainer:"modal-module__informationContainer___xMFW5","informationContainer--error":"modal-module__informationContainer--error___38TFm",informationText:"modal-module__informationText___A-SAd","informationText--error":"modal-module__informationText--error___ttidD",slottedContent:"modal-module__slottedContent___y-QlK",sectionedContent:"modal-module__sectionedContent___eW8uD",overflow:"modal-module__overflow___qSCWP",scroll:"modal-module__scroll___lvg-6",modalFooter:"modal-module__modalFooter___478rR","modalFooter--start":"modal-module__modalFooter--start___-q0-Q","modalFooter--end":"modal-module__modalFooter--end___B5Qoq","modalFooter--between":"modal-module__modalFooter--between___WNRak",infoButton:"modal-module__infoButton___HZTCB",DocModal:"modal-module__DocModal___oNx31",chipStyling:"modal-module__chipStyling___S9Fr5",bottomBorder:"modal-module__bottomBorder___xGiMk",manageAccess:"modal-module__manageAccess___t3qxY",newFolderButton:"modal-module__newFolderButton___QwE1O",slottedContentNoHeaderBorder:"modal-module__slottedContentNoHeaderBorder___2Wr9y",modalLarge:"modal-module__modalLarge___JKgcE",modalMedium:"modal-module__modalMedium___XefdF",modalSmall:"modal-module__modalSmall___pj6qT",modalMini:"modal-module__modalMini___7LNvT",closeModalButton:"modal-module__closeModalButton___tBdl7"};S(".modal-module__modalWrapper___HEHwS{align-items:end;display:flex;height:100%;justify-content:center;left:0;position:fixed;top:0;width:100%;z-index:var(--z-index-modal)}.modal-module__overlay___PR8ec{background-color:#000;height:100%;left:0;opacity:.5;position:absolute;top:0;width:100%}.modal-module__truncateTitle___12esQ{word-wrap:normal;hyphens:none;max-width:400px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:normal}.modal-module__modal___u3COr{background-color:var(--color-bg-white);border-top-left-radius:1.5rem;border-top-right-radius:1.5rem;box-shadow:0 4px 4px 0 #00000040;max-height:80%;position:relative;width:100%;z-index:1}.modal-module__modalContent___22CDu{display:flex;flex-direction:column;height:65vh;min-height:0}.modal-module__modalHeader___TxZbn{align-items:center;display:grid;padding:1.5rem 1.5rem 0}.modal-module__modalHeader--withBorder___B5pEv{border-bottom:1px solid var(--color-border-pale-grey);padding-bottom:1rem}.modal-module__modalHeaderTextContainer___0lMRZ{display:flex;flex-direction:column;gap:.5rem}.modal-module__modalTitleContainer___GKoJq{align-items:center;display:flex;flex:1;min-width:0}.modal-module__modalTitle___J9EuI{word-wrap:break-word;color:var(--color-primary-charcoal);font-size:var(--fontsize-h4);font-weight:600;hyphens:auto;line-height:var(--lineheight-h4);margin:0;outline:none;word-break:break-word}.modal-module__padTitle___KJreS{padding-right:3rem}.modal-module__modalSubtitle___9xEUS{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body);font-weight:400;line-height:var(--lineheight-body)}.modal-module__informationContainer___xMFW5{align-items:center;color:var(--color-bg-inactive-charcoal);column-gap:.5rem;display:grid;grid-template-columns:auto auto;justify-content:start}.modal-module__informationContainer--error___38TFm{color:var(--color-secondary-burgundy)}.modal-module__informationText___A-SAd{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small)}.modal-module__informationText--error___ttidD{color:var(--color-secondary-burgundy)}.modal-module__slottedContent___y-QlK{flex:1;height:100%;max-height:25rem;min-height:0;padding:1.5rem}.modal-module__sectionedContent___eW8uD{max-height:2.75rem;padding:.75rem}.modal-module__overflow___qSCWP{overflow:auto}.modal-module__scroll___lvg-6{max-height:20rem;min-height:20rem;overflow-y:auto}.modal-module__modalFooter___478rR{background-color:var(--color-bg-white);border-top:1px solid var(--color-border-pale-grey);display:flex;flex-direction:row-reverse;gap:1rem;padding:1.5rem 2rem;position:relative}.modal-module__modalFooter--start___-q0-Q{justify-content:start}.modal-module__modalFooter--end___B5Qoq{justify-content:end}.modal-module__modalFooter--between___WNRak{justify-content:space-between;button{width:100%}}.modal-module__infoButton___HZTCB{background-color:transparent;border:none;border-radius:50px;display:flex}.modal-module__infoButton___HZTCB:hover{background-color:var(--color-primary-pale-charcoal);cursor:pointer}.modal-module__DocModal___oNx31{display:flex;flex-direction:row-reverse;position:relative;.modal-module__modalHeader___TxZbn{padding:16px 16px 0}.modal-module__modalTitle___J9EuI{color:var(--color-primary-charcoal);font-family:var(--primary-font-family);font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body)}.modal-module__modalSubtitle___9xEUS{color:var(--color-bg-inactive-charcoal);font-family:var(--fontsize-body-small);font-size:var(--fontsize-body-xsmall);font-weight:600;line-height:var(--lineheight-body)}.modal-module__informationContainer___xMFW5{border-bottom:1px solid var(--color-border-pale-grey);color:var(--color-primary-charcoal);width:268px}.modal-module__informationText___A-SAd{padding-bottom:12px}.modal-module__slottedContent___y-QlK{padding:12px 0 8px 16px}.modal-module__chipStyling___S9Fr5{padding-bottom:12px;padding-top:8px;width:fit-content}.modal-module__bottomBorder___xGiMk{border-bottom:1px solid var(--color-border-pale-grey);height:1px;margin-bottom:12px;width:268px}.modal-module__overlay___PR8ec{display:flex;height:0;width:0}.modal-module__modalWrapper___HEHwS{display:flex;flex-direction:row-reverse;height:auto;left:auto;position:absolute;top:auto;transform:translate(35px,26px);width:auto}.modal-module__manageAccess___t3qxY{color:var(--color-secondary-cobalt);font-size:var(--fontsize-body-small);font-weight:600;padding-top:8px;width:fit-content}.modal-module__manageAccess___t3qxY:hover{cursor:pointer}}.modal-module__newFolderButton___QwE1O{align-items:center;color:var(--color-secondary-cobalt);cursor:pointer;display:flex;font-weight:600;gap:.5rem;left:30px;padding:0;position:absolute;top:40px;&:hover{text-decoration:underline}}.modal-module__slottedContentNoHeaderBorder___2Wr9y{padding:.5rem 1.5rem 1.5rem}@media (min-width:769px){.modal-module__modalWrapper___HEHwS{align-items:center}.modal-module__modal___u3COr{border-radius:2px;margin:0 2rem;max-height:50rem}.modal-module__modalHeader___TxZbn{padding:2rem 2rem 0}.modal-module__modalHeader--withBorder___B5pEv{padding-bottom:1.5rem}.modal-module__modalContent___22CDu{display:grid;height:auto;min-height:0}.modal-module__modalLarge___JKgcE{max-width:100%;width:49rem}.modal-module__modalMedium___XefdF{max-width:100%;width:37.5rem}.modal-module__modalSmall___pj6qT{max-width:100%;width:35rem}.modal-module__modalMini___7LNvT{max-width:100%;width:18.75rem}.modal-module__slottedContent___y-QlK{padding:1rem 1.5rem}.modal-module__slottedContentNoHeaderBorder___2Wr9y{padding:1rem 2rem 2rem}.modal-module__modalFooter___478rR{border-bottom-left-radius:2px;border-bottom-right-radius:2px;padding:1.5rem 2rem}.modal-module__modalFooter--between___WNRak{button{width:auto}}}.modal-module__closeModalButton___tBdl7{position:absolute;right:2rem;top:2rem}"),(ec=exports.FooterButtonAlignment||(exports.FooterButtonAlignment={})).START="start",ec.END="end",ec.BETWEEN="between",(tc=exports.ModalSize||(exports.ModalSize={})).SMALL="Small",tc.MEDIUM="Medium",tc.LARGE="Large",tc.MINI="Mini",(ac=exports.InformationTextType||(exports.InformationTextType={})).INFO="info",ac.ERROR="error",(rc=exports.InfoModalType||(exports.InfoModalType={})).DOCUMENT="Document",rc.FOLDER="Folder";var nc,lc,ic=function(e){var o,n,l,i=e.id,s=e.title,_=e.subtitle,m=e.informationText,u=e.informationTextType,p=void 0===u?exports.InformationTextType.INFO:u,g=e.size,h=void 0===g?exports.ModalSize.LARGE:g,f=e.includeHeaderBorder,b=void 0===f||f,v=e.isVisible,w=e.hide,y=e.primaryBtnConfig,x=e.secondaryBtnConfig,E=e.footerBtnAlignment,C=e.slottedContainerRef,k=e.children,T=e.allowOverflow,I=void 0===T||T,z=e.closeButton,N=void 0===z||z,S=e.isScrollable,B=void 0!==S&&S,L=e.minHeight,O=e.dataTestId,A=void 0===O?"uikit-modal":O,D=e.ariaLabel,j=void 0===D?"":D,M=e.addFooterButton,P=void 0!==M&&M,H=e.addFooterButtonMethod,R=e.isClient,V=void 0!==R&&R,F=e.truncateTitle,W=void 0!==F&&F,U=e.customHeaderContent,q=e.returnFocusRef,K=e.wrapperClassName,G=e.slottedContainerClassName,Y=e.closeOnInteractOutside,J=void 0===Y||Y,Z=y&&y.label&&y.onClick,X=x&&x.label&&x.onClick,Q=Z||X,$=c.useTranslation("documents").t,ee=c.useTranslation("global").t,te=t.useRef(null);return a.createElement(d.Dialog.Root,{lazyMount:!0,unmountOnExit:!0,"data-testid":A,restoreFocus:!0,id:i,"aria-label":j,open:v,onOpenChange:function(e){e.open||null==w||w()},finalFocusEl:function(){var e;return null!=(e=null==q?void 0:q.current)?e:null},initialFocusEl:function(){return te.current},closeOnInteractOutside:J},a.createElement(d.Portal,null,a.createElement(d.Dialog.Positioner,{className:r(oc.modalWrapper,K)},a.createElement(d.Dialog.Backdrop,{className:oc.overlay}),a.createElement(d.Dialog.Content,{asChild:!0},a.createElement("div",{className:r(oc.modal,oc["modal"+h])},a.createElement("div",{className:oc.modalContent},a.createElement("div",{className:r(oc.modalHeader,(o={},o[oc["modalHeader--withBorder"]]=b,o))},a.createElement("div",{className:oc.modalHeaderTextContainer},a.createElement("div",{className:oc.modalTitleContainer},s&&a.createElement(d.Dialog.Title,{asChild:!0,ref:te},a.createElement("h2",{className:r(oc.modalTitle,(n={},n[oc.truncateTitle]=W,n[oc.padTitle]=N,n)),tabIndex:-1},s))),_&&a.createElement("div",{className:oc.modalSubtitle},_),m&&a.createElement("div",{className:r(oc.informationContainer,oc["informationContainer--"+p])},p===exports.InformationTextType.ERROR&&a.createElement(nl,{iconName:"warning-icon",altText:"warning-icon"}),a.createElement("div",{className:r(oc.informationText,oc["informationText--"+p])},m))),U),k&&a.createElement("div",{ref:C,className:r(G,b?oc.slottedContent:oc.slottedContentNoHeaderBorder,(l={},l[oc.overflow]=I,l[oc.scroll]=B,l)),style:L?{minHeight:L}:{}},k)),Q&&a.createElement("div",{className:r(oc.modalFooter,oc["modalFooter--"+E])},P&&!V&&a.createElement("button",{className:oc.newFolderButton,onClick:H},$("create-new-subfolder"),a.createElement(nl,{iconName:"add-icon"})),Z&&(null==y?void 0:y.label)&&a.createElement(_l,Object.assign({},y)),X&&(null==x?void 0:x.label)&&a.createElement(_l,Object.assign({},x))),N&&a.createElement(d.Dialog.CloseTrigger,{asChild:!0},a.createElement(mi,{icon:a.createElement(nl,{iconName:"dismiss-icon-thicker",altText:"Dismiss modal button",size:20}),inputId:"close-modal-button",ariaLabel:ee("close-modal"),className:oc.closeModalButton})))))))},cc=function(e){var t=e.inputDate,r=e.personName,o=e.chipIcon,n=e.chipName,l=e.chipSection;return a.createElement("div",null,a.createElement("div",{className:oc.modalSubtitle},e.header),l?a.createElement("div",{className:oc.chipStyling},a.createElement(Fl,{text:n,type:exports.ChipType.LOCATION,iconName:o})):a.createElement("div",{className:oc.informationText},t," • ",r))};(lc=exports.NotificationBadgeType||(exports.NotificationBadgeType={})).ACTION_ITEM="ActionItem",lc.DELIVERABLE="Deliverable",lc.PROJECT_HEALTH_STATUS="ProjectHealthStatus",lc.GENERAL="General";var dc=((nc={})[exports.NotificationBadgeType.ACTION_ITEM]="var(--color-secondary-cobalt-transparent)",nc[exports.NotificationBadgeType.DELIVERABLE]="var(--color-primary-red-transparent)",nc[exports.NotificationBadgeType.PROJECT_HEALTH_STATUS]="var(--color-secondary-gold-transparent)",nc[exports.NotificationBadgeType.GENERAL]="var(--color-bg-light-grey)",nc);S(".notificationTypeBadge-module__notificationTypeBadge___AuyV5{align-items:center;border-radius:100px;padding:.125rem .5rem;width:fit-content}.notificationTypeBadge-module__notificationTypeBadgeText___fFRXj{color:var(--color-primary-light-charcoal);font-family:var(--primary-font-family);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small)}"),S('.progressBar-module__progressContainer___ibF48{display:flex;gap:8px}.progressBar-module__progressSection___rY7dm{background-color:var(--color-primary-pale-charcoal);height:.25rem}.progressBar-module__merged___I2yWf{background-color:var(--color-primary-red);height:.25rem;margin-left:-8px}.progressBar-module__merged___I2yWf:first-of-type{margin-left:0}@media (min-width:769px){.progressBar-module__progressSection___rY7dm{height:.5rem;width:3rem}.progressBar-module__active___ESJ6E{background-color:var(--color-primary-red);position:relative;&+.progressBar-module__active___ESJ6E{&:before{background-color:var(--color-primary-red);content:"";height:8px;left:-8px;position:absolute;top:0;width:8px}}}}'),S(".progressCompletion-module__container___zzhpF{background-color:var(--color-bg-white);border:1px solid #e0e0e0;border-radius:4px;font-family:var(--primary-font-family);padding:1rem}.progressCompletion-module__header___jRfpe{align-items:center;display:flex;justify-content:space-between;margin-bottom:.5rem}.progressCompletion-module__title___SEWuS{align-items:center;color:var(--color-primary-charcoal);display:flex;font-size:1.25rem;font-weight:600;gap:.5rem;line-height:var(--lineheight-body);margin:0}.progressCompletion-module__infoIcon___0Vmh-{cursor:pointer;font-size:.875rem}.progressCompletion-module__editContainer___p5VeB{align-items:center;color:var(--color-secondary-cobalt);cursor:pointer;display:flex;font-weight:600;gap:.5rem;padding:0;&:hover{text-decoration:underline}}.progressCompletion-module__lastUpdatedContainer___couy8{color:var(--color-primary-light-charcoal);margin:1rem 0 2rem}.progressCompletion-module__progressWrapper___N8-H6{align-items:center;display:flex;justify-content:space-between;margin-bottom:.5rem}.progressCompletion-module__percentage___EIx-P{font-size:1.875rem;font-weight:600;margin-right:.5rem}.progressCompletion-module__progressBarContainer___HcbzG{background-color:var(--color-bg-primary-pale-charcoal);border-radius:4px;flex-grow:1;float:left;height:12px;margin-top:21px;overflow:hidden;position:relative;width:100%}.progressCompletion-module__progressBarContainerModal___ShD9C{width:80%}.progressCompletion-module__progressBar___gAcCv{background-color:var(--color-secondary-cobalt);height:100%;left:0;position:absolute;top:0}.progressCompletion-module__endDateContainer___tQFOg{color:var(--color-primary-inactive-charcoal);font-size:1rem;text-align:right}.progressCompletion-module__endDate___7jqiQ{font-weight:600}.progressCompletion-module__modalChildren___D1wRq{display:flex;gap:1.5rem}.progressCompletion-module__inputWrapper___lTTvh{align-items:center;display:flex;gap:.5rem;width:20%}.progressCompletion-module__successToast___nak0U{bottom:1rem;position:absolute;right:2rem}");var sc,_c,mc,uc,pc,gc,hc,fc,bc={fieldRoot:"searchDropdown-module__fieldRoot___-mAUi",userSelectInput:"searchDropdown-module__userSelectInput___8M-uF",useMaxWidth:"searchDropdown-module__useMaxWidth___3HJ30",userSelectInputError:"searchDropdown-module__userSelectInputError___JL6zZ",selectedUsers:"searchDropdown-module__selectedUsers___zPv1Q",label:"searchDropdown-module__label___n0OSj",unassignedUser:"searchDropdown-module__unassignedUser___m3BqN",input:"searchDropdown-module__input___9VFCR",dropdown:"searchDropdown-module__dropdown___7YvLu",dropdownItem:"searchDropdown-module__dropdownItem___jNiDy",noResults:"searchDropdown-module__noResults___a1PnC",errorTextContainer:"searchDropdown-module__errorTextContainer___iDysJ",errorText:"searchDropdown-module__errorText___x3kug"};S(".searchDropdown-module__fieldRoot___-mAUi{width:100%}.searchDropdown-module__userSelectInput___8M-uF{align-items:center;background-color:var(--color-bg-white);border:.0625rem solid var(--color-border-pale-grey);border-radius:2px;display:flex;padding:.5rem 1rem;position:relative;transition:background-color .2s;&::placeholder{color:var(--color-bg-inactive-charcoal)}&:hover{background-color:var(--color-primary-soft-charcoal)}&:focus-within{outline:none;&.focusedByKeyboard{outline:2px solid var(--color-secondary-cobalt)}}}.searchDropdown-module__useMaxWidth___3HJ30{width:100%}.searchDropdown-module__userSelectInput___8M-uF:focus-visible,.searchDropdown-module__userSelectInput___8M-uF[data-state=focus]{border:1px solid var(--color-primary-charcoal);outline:none;&.focusedByKeyboard{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}}.searchDropdown-module__userSelectInputError___JL6zZ,.searchDropdown-module__userSelectInputError___JL6zZ[data-state=focus]{border-color:var(--color-secondary-burgundy)}.searchDropdown-module__selectedUsers___zPv1Q{display:flex;flex-wrap:wrap;gap:.5rem;max-height:7.5rem;overflow-y:auto;width:100%}.searchDropdown-module__label___n0OSj{align-items:center;display:flex;flex-direction:row;flex-grow:1;gap:.5rem;min-width:1rem}.searchDropdown-module__unassignedUser___m3BqN{background-color:var(--color-bg-grey);border-radius:50%;padding:.25rem}.searchDropdown-module__input___9VFCR{background-color:transparent;border:none;color:var(--color-primary-charcoal);font-family:var(--primary-font-family);font-size:var(--fontsize-body);line-height:var(--lineheight-body);min-width:100%;outline:none;padding:0;width:0}.searchDropdown-module__dropdown___7YvLu{background-color:var(--color-bg-white);border-radius:.25rem;box-shadow:0 .25rem .5rem rgba(0,0,0,.1);left:0;list-style:none;max-height:11.75rem;overflow-y:auto;padding:0;position:absolute;top:100%;width:100%;z-index:10}.searchDropdown-module__dropdownItem___jNiDy{color:var(--color-primary-charcoal);cursor:pointer;font-size:1rem;transition:background-color .2s;&[data-highlighted]{background-color:var(--color-bg-light-grey)}}.searchDropdown-module__dropdownItem___jNiDy:hover{background-color:var(--color-bg-light-grey)}.searchDropdown-module__noResults___a1PnC{color:var(--color-primary-charcoal);font-size:1rem;padding:.5rem;text-align:center}.searchDropdown-module__errorTextContainer___iDysJ{align-items:center;color:var(--color-secondary-burgundy);display:flex;flex-direction:row;font-size:var(--fontsize-body-small);gap:.5rem;line-height:var(--lineheight-body-small);margin-top:.75rem}.searchDropdown-module__errorText___x3kug{font-size:var(--fontsize-body-small);line-height:var(--lineheight-body);margin:0}"),(sc=exports.UserStatus||(exports.UserStatus={})).Active="active",sc.Inactive="inactive",(_c=exports.Language||(exports.Language={})).English="english",_c.French="french",(mc=exports.Department||(exports.Department={})).Other="other",mc.Administration="administration",(uc=exports.EmailFrequency||(exports.EmailFrequency={})).None="none",uc.Weekly="weekly",(pc=exports.NotificationType||(exports.NotificationType={})).ActionItemAssigned="action_item_assigned",pc.ActionItemStatusUpdated="action_item_status_updated",pc.ActionItemCommentAdded="action_item_comment_added",pc.ProjectInvite="project_invite",pc.HealthStatusUpdate="health_status_update",pc.FinalDeliveryUploaded="final_delivery_uploaded",(gc=exports.BusinessDomain||(exports.BusinessDomain={})).EconomicResiliency="economic_resiliency",gc.AritificialIntelligence="aritificial_intelligence",gc.InvestmentStrategyAndPlanning="investment_strategy_and_planning",gc.SecurityVulnerabilitiesAndBreaches="security_vulnerabilities_and_breaches",gc.DigitalOrItTransformation="digital_or_it_transformation",gc.FraudProtection="fraud_protection",gc.MergersAndAcquisitions="mergers_and_acquisitions",gc.SupplyChains="supply_chains",gc.OfficeThreeSixFive="office_three_six_five",gc.Other="other",S(".sectionBanner-module__sectionBanner___MkOWU{background-color:var(--color-bg-light-grey);border-radius:2px;color:var(--color-primary-charcoal);font-size:var(--fontsize-body-eyebrow);font-weight:600;letter-spacing:.1rem;line-height:var(--lineheight-body-eyebrow);padding:.5rem 1.5rem;text-transform:uppercase;width:100%}.sectionBanner-module__sectionBanner--highlighted___RAuTJ{background-color:#0062b81a}"),(hc=exports.BackgroundColor||(exports.BackgroundColor={})).WHITE="white",hc.CHARCOAL="charcoal",(fc=exports.IconColor||(exports.IconColor={})).BURGUNDY="burgundy",fc.EMERALD="emerald",fc.OCEAN="ocean",fc.GOLD="gold",fc.JADE="jade";var vc={selectionCard:"selectionCard-module__selectionCard___OSNPH","selectionCard--charcoal":"selectionCard-module__selectionCard--charcoal___EZjWG",title:"selectionCard-module__title___3VDlJ",subtitle:"selectionCard-module__subtitle___7gg3q",leftIconContainer:"selectionCard-module__leftIconContainer___ENTXb",textContainer:"selectionCard-module__textContainer___0BThx",rightIconContainer:"selectionCard-module__rightIconContainer___p69yQ",selectionCardDisabled:"selectionCard-module__selectionCardDisabled___yTmaG",burgundy:"selectionCard-module__burgundy___RT7RF",emerald:"selectionCard-module__emerald___cQ4s-",ocean:"selectionCard-module__ocean___hoLQm",gold:"selectionCard-module__gold___v7SCz",jade:"selectionCard-module__jade___x2qfD"};S(".selectionCard-module__selectionCard___OSNPH{background-color:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:2px;cursor:pointer;display:grid;grid-template-columns:auto 1fr auto;justify-items:start;margin:0;padding:1.5rem 1rem;transition:background-color .5s ease;width:100%}.selectionCard-module__selectionCard--charcoal___EZjWG,.selectionCard-module__selectionCard___OSNPH:hover{background-color:var(--color-bg-light-grey)}.selectionCard-module__selectionCard--charcoal___EZjWG:hover{background-color:var(--color-bg-white)}.selectionCard-module__title___3VDlJ{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body);text-align:left}.selectionCard-module__subtitle___7gg3q{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small);margin-top:.5rem;text-align:start}.selectionCard-module__leftIconContainer___ENTXb{height:1.5rem;margin-right:1rem;width:1.5rem;svg{height:1.25rem;width:1.25rem}}.selectionCard-module__textContainer___0BThx{display:grid;justify-items:start}.selectionCard-module__rightIconContainer___p69yQ{align-self:center;color:var(--color-secondary-cobalt);height:1.5rem;justify-self:end;margin-left:1rem;width:1.5rem;svg{height:1.25rem;width:1.25rem}}.selectionCard-module__selectionCardDisabled___yTmaG{background:var(--color-bg-light-grey);cursor:not-allowed;.selectionCard-module__subtitle___7gg3q,.selectionCard-module__title___3VDlJ{color:var(--color-primary-inactive-charcoal)}}.selectionCard-module__burgundy___RT7RF{color:var(--color-secondary-burgundy)}.selectionCard-module__emerald___cQ4s-{color:var(--color-secondary-emerald)}.selectionCard-module__ocean___hoLQm{color:var(--color-secondary-ocean)}.selectionCard-module__gold___v7SCz{color:var(--color-secondary-gold)}.selectionCard-module__jade___x2qfD{color:var(--color-secondary-jade)}");var wc,yc,xc,Ec,Cc,kc,Tc,Ic,zc={splitter:"splitter-module__splitter___xXqus",panel:"splitter-module__panel___iNFgO",resizeTrigger:"splitter-module__resizeTrigger___WsF1E"};S('.splitter-module__splitter___xXqus{display:flex;height:100%;width:100%}.splitter-module__panel___iNFgO{height:100%;overflow:auto;padding:0}.splitter-module__resizeTrigger___WsF1E{background-color:#e5e5e5;border:0;border-radius:0;cursor:col-resize;padding:0;position:relative;transition:background-color .2s ease;width:2px}.splitter-module__resizeTrigger___WsF1E:active,.splitter-module__resizeTrigger___WsF1E:hover{background-color:var(--color-primary-charcoal)}.splitter-module__resizeTrigger___WsF1E:after{background:transparent;bottom:0;content:"";left:-2px;position:absolute;right:-2px;top:0}.splitter-module__resizeTrigger___WsF1E:hover:after{background-color:rgba(var(--color-primary-charcoal-rgb),.1)}'),(kc=exports.StatusBadgeType||(exports.StatusBadgeType={})).PROJECT="Project",kc.ACTION_ITEM="ActionItem",(Tc=exports.ProjectHealthStatusType||(exports.ProjectHealthStatusType={})).ONTRACK="OnTrack",Tc.ATTENTION="NeedsAttention",Tc.ATRISK="AtRisk",(Ic=exports.ActionItemStatusType||(exports.ActionItemStatusType={})).TODO="to_do",Ic.INREVIEW="in_review",Ic.DECLINED="declined",Ic.COMPLETE="complete";var Nc=function(e){switch(e.toUpperCase()){case"TO_DO":return exports.ActionItemStatusType.TODO;case"IN_REVIEW":return exports.ActionItemStatusType.INREVIEW;case"DECLINED":return exports.ActionItemStatusType.DECLINED;case"COMPLETE":return exports.ActionItemStatusType.COMPLETE;default:throw new Error("Unknown action item status type: "+e)}},Sc=function(e){switch(e.toUpperCase()){case"ONTRACK":return exports.ProjectHealthStatusType.ONTRACK;case"NEEDSATTENTION":case"ATTENTION":return exports.ProjectHealthStatusType.ATTENTION;case"ATRISK":return exports.ProjectHealthStatusType.ATRISK;default:throw new Error("Unknown project health status type: "+e)}},Bc=((wc={})[exports.ActionItemStatusType.TODO]="To do",wc[exports.ActionItemStatusType.INREVIEW]="In review",wc[exports.ActionItemStatusType.DECLINED]="Declined",wc[exports.ActionItemStatusType.COMPLETE]="Complete",wc[exports.ProjectHealthStatusType.ONTRACK]="On track",wc[exports.ProjectHealthStatusType.ATTENTION]="Needs attention",wc[exports.ProjectHealthStatusType.ATRISK]="At risk",wc),Lc=((yc={})[exports.ActionItemStatusType.TODO]="To Do",yc[exports.ActionItemStatusType.INREVIEW]="In Review",yc[exports.ActionItemStatusType.DECLINED]="Declined",yc[exports.ActionItemStatusType.COMPLETE]="Complete",yc[exports.ProjectHealthStatusType.ONTRACK]="On Track",yc[exports.ProjectHealthStatusType.ATTENTION]="Needs Attention",yc[exports.ProjectHealthStatusType.ATRISK]="At Risk",yc),Oc=((xc={})[exports.ActionItemStatusType.TODO]="circle",xc[exports.ActionItemStatusType.INREVIEW]="circle-filled",xc[exports.ActionItemStatusType.DECLINED]="circle-filled",xc[exports.ActionItemStatusType.COMPLETE]="checkmark-circle-filled",xc[exports.ProjectHealthStatusType.ONTRACK]="circle-filled",xc[exports.ProjectHealthStatusType.ATTENTION]="circle-filled",xc[exports.ProjectHealthStatusType.ATRISK]="circle-filled",xc),Ac=((Ec={})[exports.ActionItemStatusType.TODO]="var(--color-primary-slate)",Ec[exports.ActionItemStatusType.INREVIEW]="var(--color-secondary-ocean)",Ec[exports.ActionItemStatusType.DECLINED]="var(--color-secondary-gold)",Ec[exports.ActionItemStatusType.COMPLETE]="var(--color-secondary-emerald)",Ec[exports.ProjectHealthStatusType.ONTRACK]="var(--color-secondary-emerald)",Ec[exports.ProjectHealthStatusType.ATTENTION]="var(--color-secondary-gold)",Ec[exports.ProjectHealthStatusType.ATRISK]="var(--color-secondary-burgundy)",Ec),Dc=((Cc={})[exports.ActionItemStatusType.TODO]="var(--color-primary-slate)",Cc[exports.ActionItemStatusType.INREVIEW]="var(--color-secondary-ocean)",Cc[exports.ActionItemStatusType.DECLINED]="var(--color-secondary-gold)",Cc[exports.ActionItemStatusType.COMPLETE]="var(--color-secondary-emerald)",Cc[exports.ProjectHealthStatusType.ONTRACK]="var(--color-secondary-emerald-badge)",Cc[exports.ProjectHealthStatusType.ATTENTION]="var(--color-secondary-gold-badge)",Cc[exports.ProjectHealthStatusType.ATRISK]="var(--color-secondary-burgundy-badge)",Cc);S('.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm .statusBadge-module__statusBadge___GhPoU,.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm [data-scope=menu][data-part=trigger],.statusBadge-module__statusBadge___GhPoU{align-items:center;background-color:var(--color-primary-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:100px;color:var(--color-primary-slate);column-gap:.5rem;display:grid;font-size:var(--fontsize-body-small);grid-template-columns:auto 1fr auto;justify-content:start;line-height:var(--lineheight-body-small);padding:.125rem .5rem .125rem .25rem;position:relative;transition:.1s ease-out;width:max-content;&.statusBadge-module__clickable___NtlLj{transition:background-color .2s;&:hover{background-color:var(--color-bg-light-grey)}&:focus-visible{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}}}.statusBadge-module__statusBadge___GhPoU[disabled]{cursor:unset}.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm div[data-part=positioner][id="menu:actionItemBadge:popper"]{position:relative!important;transform:translate3d(0,2px,0)!important}.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm div[id="menu:actionItemBadge:content"][data-state=open][aria-labelledby="menu:actionItemBadge:trigger"]{z-index:var(--z-index-chip-dropdown)}.statusBadge-module__statusBadgeText___MwNbg{color:var(--color-primary-charcoal);font-family:ProximaNova,Arial,sans-serif}.statusBadge-module__statusIconContainer___-nt0J{padding:.25rem}.statusBadge-module__itemContainer___UF5lo[data-scope=menu][data-part=item]{align-items:flex-start;display:flex;justify-content:flex-start;padding:1rem;width:100%}.statusBadge-module__itemContainer___UF5lo:hover{background-color:var(--color-bg-light-grey);cursor:pointer}.statusBadge-module__itemTextContainer___ZRjXa{display:flex;flex-direction:column;gap:.5rem;height:40px;width:max-content}.statusBadge-module__itemLabel___R-RHf{font-size:var(--fontsize-body)}.statusBadge-module__itemText___vErL5{color:var(--color-primary-inactive-charcoal);font-size:var(--fontsize-body-small)}[data-scope=menu][data-part=positioner]{display:contents}[data-scope=menu][data-part=content]{border:1px solid var(--color-border-grey);box-shadow:0 4px 4px 0 rgba(0,0,0,.25);display:block;outline:none;padding:0;position:relative;position:absolute;z-index:var(--z-index-dropdown-list)}[data-scope=menu][data-part=content][data-state=closed]{opacity:0;transition:opacity .2s linear,visibility .2s linear;visibility:hidden}[data-scope=menu][data-part=content][data-state=open]{background-color:var(--color-bg-white);opacity:1;transition:opacity .2s linear,visibility .2s linear;visibility:visible}[data-scope=menu][data-part=item]{align-items:center;align-self:stretch;color:var(--color-primary-charcoal);cursor:pointer;display:flex;font-family:ProximaNova,Arial,sans-serif;font-size:var(--fontsize-body);font-style:normal;font-weight:400;justify-content:space-between;line-height:var(--lineheight-body);padding:0 1rem}[data-scope=menu][data-part=item].statusBadge-module__labelWithIcon___MQBH5{border:none;padding-left:8px}[data-scope=menu][data-part=item].statusBadge-module__critical___DcpfC{color:var(--color-secondary-burgundy)}.statusBadge-module__withIcon___zYtDG{border:transparent;display:flex;flex-direction:row}.statusBadge-module__withIcon___zYtDG:hover{background-color:var(--color-primary-pale-charcoal);cursor:pointer;[data-scope=menu][data-part=item]{background-color:var(--color-primary-pale-charcoal)}}.statusBadge-module__forIcon___dZSzw{display:flex;flex-direction:column;margin-top:.75rem}.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm>[id="menu:actionItemBadge:popper"][aria-expanded=true]{display:none}.statusBadge-module__statusIconContainer___-nt0J{border-radius:50%;height:1rem;opacity:0;position:relative;width:1rem}.statusBadge-module__statusIcon___odKXG{left:7px;position:absolute;top:7px}.statusBadge-module__statusIconLarge___OUxGn{left:4px;top:4px}.statusBadge-module__statusIconContainerWithPulse___J-TTm{animation:statusBadge-module__pulse___dx6ck 2.75s infinite;border-radius:50%;height:1rem;opacity:0;position:relative;width:1rem}@keyframes statusBadge-module__pulse___dx6ck{0%{opacity:0;transform:scale(0)}50%{opacity:1;transform:scale(1.25)}75%{opacity:0;transform:scale(1.25)}to{opacity:0;transform:scale(0)}}');var jc=["type","badgeType","items","onClick","dataTestId","role","ariaDescribedBy","ariaLabelledBy","tabIndex","isTooltip","ariaLabel"];S(".statusChip-module__statusChip___zn5qj{align-items:center;background-color:var(--color-primary-white);border:1px solid var(--color-border-pale-grey);border-radius:100px;color:var(--color-primary-slate);display:flex;flex-direction:row;font-size:var(--fontsize-body-small);gap:.25rem;justify-content:start;line-height:var(--lineheight-body-small);padding:.125rem .5rem .125rem .25rem;transition:.1s ease-out}.statusChip-module__statusChip___zn5qj:hover{background-color:var(--color-bg-light-grey);border-color:var(--color-border-light-grey);cursor:pointer}.statusChip-module__statusChipText___A4pxm{color:var(--color-primary-charcoal);font-family:ProximaNova,Arial,sans-serif}"),S(".tab-module__tab___VaffT{border-bottom:3px solid transparent;border-radius:0;color:var(--color-bg-inactive-charcoal);cursor:pointer;display:inline-block;font-size:var(--fontsize-body-small);font-weight:600;line-height:var(--lineheight-body-small);padding:.75rem .5rem;text-decoration:none}.tab-module__tab___VaffT:hover{background-color:var(--color-bg-light-grey);color:var(--color-bg-inactive-charcoal)}.tab-module__tab___VaffT:focus-visible{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}.tab-module__tab___VaffT.tab-module__active___nUiBm{border-bottom:3px solid var(--color-primary-red);color:var(--color-primary-charcoal)}");var Mc=["text","link","isActive","onTabClick","dataTestId","role","ariaLabel","ariaDescribedBy","ariaLabelledBy"],Pc=function(e){var t,o,n=e.text,l=e.link,i=e.isActive,c=e.onTabClick,d=e.dataTestId,_=void 0===d?"uikit-tab":d,m=e.role,u=void 0===m?"tab":m,p=e.ariaLabel,g=e.ariaDescribedBy,h=e.ariaLabelledBy,f=rt(e,Mc),b=s.useLocation(),v=i||b.pathname===l;return c?a.createElement("button",Object.assign({className:r("tab-module__tab___VaffT",(t={},t["tab-module__active___nUiBm"]=v,t)),"aria-selected":v,"aria-label":p,"data-testid":_,"aria-describedby":g,"aria-labelledby":h,role:u,tabIndex:v?0:-1},f,{onClick:c}),n):a.createElement(s.Link,Object.assign({className:r("tab-module__tab___VaffT",(o={},o["tab-module__active___nUiBm"]=v,o)),"aria-label":p,"aria-describedby":g,"aria-labelledby":h,tabIndex:v?0:-1,"aria-selected":v,to:l,role:u},f),n)},Hc={tableContainer:"table-module__tableContainer___S5x22",containerNoScroll:"table-module__containerNoScroll___Pa668",containerWithScroll:"table-module__containerWithScroll___IZpfR",table:"table-module__table___l4qQP",tableHeader:"table-module__tableHeader___kN6-K",tableCell:"table-module__tableCell___SkfZ4",tableRow:"table-module__tableRow___G2VsC",rowClick:"table-module__rowClick___pVnJK",tableHeaderCell:"table-module__tableHeaderCell___FDHwr",selectCell:"table-module__selectCell___Rbe8l",titleCell:"table-module__titleCell___nZURE",filesCell:"table-module__filesCell___QzP3M",selectHeader:"table-module__selectHeader___NE0-c",selectTableCell:"table-module__selectTableCell___IAkUv",selectTrashHeader:"table-module__selectTrashHeader___x1Cq0",selectTrashTableCell:"table-module__selectTrashTableCell___MWlSU",modifiedCell:"table-module__modifiedCell___6cy-i",actions:"table-module__actions___r7Gos",notificationActivityTableCell:"table-module__notificationActivityTableCell___fIfz6",nameCell:"table-module__nameCell___tgc6-",taskName:"table-module__taskName___KOWB5",tag:"table-module__tag___qoPrA",statusCell:"table-module__statusCell___OZAjv",statusCircle:"table-module__statusCircle___rU7aP",dueDateCell:"table-module__dueDateCell___nqiOB",assigneeCell:"table-module__assigneeCell___EMvA1",assignee:"table-module__assignee___E-7r1",avatar:"table-module__avatar___ks7Qf",initials:"table-module__initials___qV4GG",selectCellIcon:"table-module__selectCellIcon___ifaKX",sortableHeader:"table-module__sortableHeader___5tBDk",emptyMessageContainer:"table-module__emptyMessageContainer___NREXM",emptyMessageText:"table-module__emptyMessageText___JakoD",emptyMessageTitle:"table-module__emptyMessageTitle___W6KOd",emptyMessageSubMessage:"table-module__emptyMessageSubMessage___CloDq",clientTableHeader:"table-module__clientTableHeader___swCAz",clientTableCell:"table-module__clientTableCell___zV6Uc",trashActionItemHeader:"table-module__trashActionItemHeader___-OgTq",trashActionItemCell:"table-module__trashActionItemCell___2xDCo",placeholderItemRow:"table-module__placeholderItemRow___RBokn",placeholderSelectCell:"table-module__placeholderSelectCell___amf2f",skeletonItemCell:"table-module__skeletonItemCell___hWhGb",shimmer:"table-module__shimmer___4Fq2C",iconCell:"table-module__iconCell___YgnBx",placeholderItem:"table-module__placeholderItem___3zsJT",placeholderItemCell:"table-module__placeholderItemCell___TFNwd",placeholderItemText:"table-module__placeholderItemText___Mbyzh",placeholderItemLoadingText:"table-module__placeholderItemLoadingText___hg3P4",highlightImportantRow:"table-module__highlightImportantRow___okwvf",hideOnSmallScreen:"table-module__hideOnSmallScreen___ngiom",hideOnMediumScreen:"table-module__hideOnMediumScreen___ZmVav",teamMemberTableCell:"table-module__teamMemberTableCell___hYBA7"};S('.table-module__tableContainer___S5x22{background-color:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:8px;height:100%;width:100%}.table-module__containerNoScroll___Pa668{overflow:hidden}.table-module__containerWithScroll___IZpfR{overflow:auto}.table-module__table___l4qQP{border:none;border-collapse:collapse;flex:1;margin:0;width:100%}.table-module__table___l4qQP thead{background-color:#f5f5f5;position:sticky;top:0;z-index:var(--z-index-table-header)}.table-module__table___l4qQP thead:before{background-color:#f5f5f5;content:"";display:block;height:2px;left:0;position:absolute;top:-1px;width:100%}.table-module__tableCell___SkfZ4,.table-module__tableHeader___kN6-K{border:none;padding:.75rem}.table-module__tableRow___G2VsC{border-bottom:1px solid var(--color-primary-pale-charcoal);[data-scope=menu][data-part=trigger]{background-color:transparent}[data-scope=menu][data-part=trigger]:hover{background-color:var(--color-bg-light-grey)}}.table-module__tableRow___G2VsC:hover{background-color:var(--color-primary-soft-charcoal)}.table-module__rowClick___pVnJK{cursor:pointer;&:focus-visible{outline:2px solid var(--color-secondary-cobalt);outline-offset:-2px}}.table-module__tableHeader___kN6-K{background-color:#f5f5f5;border-bottom:1px solid var(--color-primary-pale-charcoal);color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-xsmall);padding:.75rem;text-align:left;text-transform:uppercase;white-space:nowrap}.table-module__filesCell___QzP3M,.table-module__selectCell___Rbe8l,.table-module__tableHeaderCell___FDHwr,.table-module__titleCell___nZURE{align-items:center;display:flex;font-family:var(--primary-font-family);gap:.5rem}.table-module__tableHeaderCell___FDHwr button{border:0;border-radius:0;color:var(--color-primary-light-charcoal);font-weight:600;margin:-.75rem;padding:.75rem;transition:background-color .2s;width:-webkit-fill-available;&:focus-visible{outline:2px solid var(--color-secondary-cobalt);outline-offset:-3px}&:hover{background-color:var(--color-primary-pale-charcoal)}}.table-module__selectHeader___NE0-c,.table-module__selectTableCell___IAkUv,.table-module__selectTrashHeader___x1Cq0,.table-module__selectTrashTableCell___MWlSU{width:0}.table-module__modifiedCell___6cy-i{align-items:center;color:var(--color-primary-light-charcoal);display:flex;gap:.5rem;justify-content:flex-start;white-space:nowrap}.table-module__actions___r7Gos{align-items:center;display:flex;gap:1rem;justify-content:space-between;margin-left:auto}.table-module__notificationActivityTableCell___fIfz6{height:3.75rem}.table-module__nameCell___tgc6-{align-items:center;display:flex;flex:1;gap:1rem;margin-left:-8px}.table-module__taskName___KOWB5{color:var(--color-primary-light-charcoal);font-weight:700}.table-module__tag___qoPrA{background:#f0f0f0;border-radius:4px;color:var(--color-primary-charcoal);display:inline-block;font-size:var(--fontsize-body-xsmall);padding:2px 8px}.table-module__statusCell___OZAjv{align-items:center;display:flex}.table-module__statusCircle___rU7aP{background:var(--color-secondary-cobalt);border-radius:50%;height:10px;margin-right:8px}.table-module__dueDateCell___nqiOB,.table-module__modifiedCell___6cy-i{color:var(--color-primary-light-charcoal)}.table-module__assigneeCell___EMvA1{display:flex;gap:4px}.table-module__assignee___E-7r1,.table-module__avatar___ks7Qf,.table-module__initials___qV4GG{align-items:center;border-radius:50%;display:flex;font-size:var(--fontsize-body-xsmall);font-weight:700;height:30px;justify-content:center}.table-module__assignee___E-7r1{background:var(--color-secondary-cobalt);color:#fff}.table-module__initials___qV4GG{background:#888;color:#fff;font-size:.85rem}.table-module__selectCell___Rbe8l{align-items:center;display:flex;gap:.75rem}.table-module__titleCell___nZURE{color:var(--color-primary-light-charcoal);flex-grow:1;font-weight:600}.table-module__filesCell___QzP3M{color:var(--color-bg-inactive-charcoal);font-weight:400}.table-module__selectCellIcon___ifaKX{background-color:var(--color-bg-light-grey);padding:.5rem}.table-module__sortableHeader___5tBDk{align-items:center;display:flex;gap:.5rem}.table-module__emptyMessageContainer___NREXM{align-items:center;border-radius:2px;color:var(--color-bg-inactive-charcoal);display:flex;flex-direction:column;gap:.5rem;height:90%;justify-content:center;margin:1rem;padding:2rem}.table-module__emptyMessageText___JakoD{align-items:center;display:flex;flex-direction:column;gap:.25rem}.table-module__emptyMessageTitle___W6KOd{font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-body)}.table-module__emptyMessageSubMessage___CloDq{font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small)}.table-module__clientTableHeader___swCAz{gap:.5rem}.table-module__clientTableCell___zV6Uc{padding:1.125rem .75rem}.table-module__trashActionItemCell___2xDCo,.table-module__trashActionItemHeader___-OgTq{display:none}.table-module__placeholderItemRow___RBokn{border-bottom:1px solid var(--color-primary-pale-charcoal);cursor:not-allowed}.table-module__placeholderSelectCell___amf2f{pointer-events:none}.table-module__skeletonItemCell___hWhGb{background-color:var(--color-bg-light-grey);border-radius:4px;height:1rem;overflow:hidden;position:relative;width:80%}.table-module__skeletonItemCell___hWhGb:after{animation:table-module__shimmer___4Fq2C 1.2s linear infinite;background-image:linear-gradient(90deg,hsla(0,0%,95%,0),hsla(0,0%,100%,.35) 50%,hsla(0,0%,95%,0));background-repeat:no-repeat;background-size:200% 100%;content:"";height:100%;left:0;position:absolute;top:0;width:80%}.table-module__iconCell___YgnBx{display:flex;flex-direction:column;justify-content:center;padding:.5rem}.table-module__placeholderItem___3zsJT{padding:.75rem}.table-module__placeholderItemCell___TFNwd{display:flex;gap:1rem}.table-module__placeholderItemText___Mbyzh{display:flex;flex-direction:column;opacity:.5}.table-module__placeholderItemLoadingText___hg3P4{font-size:var(--fontsize-body-small)}.table-module__highlightImportantRow___okwvf{background-color:var(--color-bg-warning-yellow-translucent)}@media (min-width:1280px){.table-module__trashActionItemCell___2xDCo,.table-module__trashActionItemHeader___-OgTq{display:table-cell}}.table-module__hideOnMediumScreen___ZmVav,.table-module__hideOnSmallScreen___ngiom{display:none}@media (min-width:768px){.table-module__hideOnSmallScreen___ngiom{display:table-cell}}@media (min-width:1024px){.table-module__hideOnMediumScreen___ZmVav{display:table-cell}}.table-module__teamMemberTableCell___hYBA7{padding:.5rem 1rem}@keyframes table-module__shimmer___4Fq2C{0%{background-position:-150% 0}to{background-position:150% 0}}');var Rc={selectHeader:Hc.selectHeader,selectTrashHeader:Hc.selectTrashHeader,teamMemberNameHeader:Hc.teamMemberNameHeader,titleDocumentsHeader:Hc.titleDocumentsHeader,titleHeader:Hc.titleHeader,trashTitleHeader:Hc.trashTitleHeader,spacerHeader:Hc.spacerHeader,modifiedHeader:Hc.modifiedHeader,actionsHeader:Hc.actionsHeader,selectTableCell:Hc.selectTableCell,selectTrashTableCell:Hc.selectTrashTableCell,teamMemberNameTableCell:Hc.teamMemberNameTableCell,titleDocumentsTableCell:Hc.titleDocumentsTableCell,titleTableCell:Hc.titleTableCell,trashTitleTableCell:Hc.trashTitleTableCell,spacerTableCell:Hc.spacerTableCell,modifiedTableCell:Hc.modifiedTableCell,actionItemTableCell:Hc.actionItemTableCell,originalLocationHeader:Hc.originalLocationHeader,originalLocationtableCell:Hc.originalLocationTableCell,documentActionsHeader:Hc.documentActionsHeader,resendHeader:Hc.resendHeader,resendTableCell:Hc.resendTableCell,titleHeaderNoAccessCol:Hc.titleHeaderNoAccessCol,titleTableCellNoAccessCol:Hc.titleTableCellNoAccessCol,timeLeftHeader:Hc.timeLeftHeader,recycleActionsHeader:Hc.recycleActionsHeader,timeLeftTableCell:Hc.timeLeftTableCell,recycleActionTableCell:Hc.recycleActionTableCell,trashActionItemHeader:Hc.trashActionItemHeader,trashActionItemCell:Hc.trashActionItemCell,originalLocations:Hc.originalLocations,notificationActivityTableHeader:Hc.notificationActivityTableHeader,notificationActivityTableCell:Hc.notificationActivityTableCell,teamMemberTableCell:Hc.teamMemberTableCell,clientTableHeader:Hc.clientTableHeader,clientTableCell:Hc.clientTableCell,hideOnSmallScreen:Hc.hideOnSmallScreen,hideOnMediumScreen:Hc.hideOnMediumScreen};S(".tabs-module__tabsContainerWrapper___jOEVs{box-sizing:border-box;padding:0 2rem;position:relative;scrollbar-width:none;white-space:nowrap;width:100%;@media (max-width:768px){overflow-x:auto}}.tabs-module__tabsContainerWrapper___jOEVs::-webkit-scrollbar{display:none}.tabs-module__tabsContainer___6Ie58{display:flex;gap:10px;min-width:max-content}");var Vc={};S("[data-scope=menu][data-part=trigger]{align-items:center;background-color:var(--color-primary-white);border:none;border-radius:2px;color:var(--color-primary-slate);column-gap:.5rem;display:grid;grid-template-columns:auto auto;justify-content:center;margin:0;outline:none;padding:.5rem;position:relative;visibility:visible}[data-scope=menu][data-part=positioner]{display:block}[data-scope=menu][data-part=content]{border-radius:2px;box-shadow:0 4px 4px 0 #00000040;padding:.5rem 0}[data-scope=menu][data-part=content]:focus-visible{outline:none}[data-scope=menu][data-part=item]{align-items:center;color:var(--color-primary-charcoal);column-gap:1rem;display:grid;font-size:var(--fontsize-body);grid-template-columns:auto 1fr;line-height:var(--fontsize-body);width:max-content}[data-scope=menu][data-part=item]:hover,[data-scope=menu][data-part=item][data-highlighted]{background-color:var(--color-bg-light-grey);cursor:pointer}"),S(".textarea-module__container___IsgwQ{display:flex;flex-direction:column;gap:.5rem;position:relative;width:100%}.textarea-module__hiddenLabel___0KxD1{display:none}.textarea-module__label___FFa15{font-size:var(--fontsize-body);font-weight:600;line-height:var(--lineheight-h6)}.textarea-module__maxLength___d-THn{color:var(--color-primary-inactive-charcoal);line-height:var(--lineheight-body);position:absolute;right:0}.textarea-module__textArea___9YNgE{background-color:var(--color-bg-white);border:1px solid var(--color-border-pale-grey);border-radius:2px;box-sizing:border-box;color:var(--color-primary-charcoal);font-family:var(--primary-font-family);font-size:var(--fontsize-body);min-height:80px;overflow:hidden;padding:.75rem 1rem;resize:none;transition:border-color .3s ease,box-shadow .3s ease,background-color .2s;&:hover{background-color:var(--color-primary-soft-charcoal)}}.textarea-module__textArea___9YNgE::placeholder{color:var(--color-primary-inactive-charcoal)}.textarea-module__textArea___9YNgE:focus-visible{border:1px solid var(--color-primary-charcoal);outline:none;&.focusedByKeyboard{border:1px solid var(--color-border-pale-grey);outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}}.textarea-module__textArea___9YNgE.textarea-module__error___PKCB3{border-color:var(--color-secondary-burgundy)}.textarea-module__textArea___9YNgE.textarea-module__success___BFQ5o{border-color:var(--color-secondary-jade)}.textarea-module__info___aGE-M{color:var(--color-primary-charcoal);display:flex;flex-direction:row;font-size:var(--fontsize-body-small);gap:3px}.textarea-module__requiredStar___vibR0{color:var(--color-primary-red);font-size:var(--fontsize-body)}.textarea-module__errorMessage___hkv1f{color:var(--color-secondary-burgundy)}"),S(".textCarousel-module__textCarousel___ZlAlw{align-items:flex-start;background-color:var(--color-bg-white);border:1px solid var(--color-border-grey);border-radius:2px;box-shadow:0 4px 24px 0 rgba(0,0,0,.16);display:flex;flex-direction:column;gap:24px;overflow:hidden;padding:47px 16px 16px;position:relative;width:450px;.textCarousel-module__didYouKnow___xX493{align-items:center;background:linear-gradient(0deg,rgba(26,99,175,.2),rgba(26,99,175,.2)),#fff;border-radius:0 0 8px 8px;display:flex;flex-direction:row;font-size:var(--fontsize-body-eyebrow);font-style:normal;font-weight:600;gap:4px;justify-content:center;left:1rem;letter-spacing:1px;line-height:var(--lineheight-body-eyebrow);padding:4px 12px;position:absolute;text-transform:uppercase;top:0}.textCarousel-module__carouselItems___hwglz{.textCarousel-module__carouselItem___l7Mt4{margin-right:1rem;.textCarousel-module__itemText___kiP2i{font-size:var(--fontsize-h5);font-weight:600;line-height:var(--lineheight-h5);margin-bottom:.5rem;margin-top:0}.textCarousel-module__itemSource___1sllB{color:var(--color-primary-slate);font-size:var(--fontsize-body-small);font-style:normal;font-weight:400;letter-spacing:-.1px;line-height:var(--lineheight-body-small)}}}.textCarousel-module__indicators___8zX73{display:flex;flex-direction:row;gap:12px;.textCarousel-module__indicator___Pxzb1{height:8px;width:24px}.textCarousel-module__indicator___Pxzb1[data-state=active]{background-color:var(--color-primary-red)}.textCarousel-module__indicator___Pxzb1[data-state=inactive]{background-color:var(--color-border-grey)}}}");var Fc={container:"projectTimeline-module__container___dw722",header:"projectTimeline-module__header___8uyuJ",editContainer:"projectTimeline-module__editContainer___xNbAf",legend:"projectTimeline-module__legend___rIekh",legendTitle:"projectTimeline-module__legendTitle___LhDC6",timeline:"projectTimeline-module__timeline___CibE-",stageWrapper:"projectTimeline-module__stageWrapper___kknp9",stage:"projectTimeline-module__stage___CV8Sp",subStageTrigger:"projectTimeline-module__subStageTrigger___h1sMH",title:"projectTimeline-module__title___29J3R",info:"projectTimeline-module__info___xB8Cw",stageTitle:"projectTimeline-module__stageTitle___3kR3O",stageSubtitle:"projectTimeline-module__stageSubtitle___qLoeQ",subStageSubtitle:"projectTimeline-module__subStageSubtitle___bDU6A",icon:"projectTimeline-module__icon___qSiod","sub-stage-icon":"projectTimeline-module__sub-stage-icon___639Tt",accordionTrigger:"projectTimeline-module__accordionTrigger___prk26",subStageIcon:"projectTimeline-module__subStageIcon___1e-Mv",finalStage:"projectTimeline-module__finalStage___O0-a5",lastUpdated:"projectTimeline-module__lastUpdated___KGAya",tooltipWrapper:"projectTimeline-module__tooltipWrapper___DoXgB",tooltipTrigger:"projectTimeline-module__tooltipTrigger___a-f4-",tooltipCard:"projectTimeline-module__tooltipCard___Zpc31",textBlock:"projectTimeline-module__textBlock___M870a",timelineStatus:"projectTimeline-module__timelineStatus___rjr5G",statusContainer:"projectTimeline-module__statusContainer___jtdhh",statusLabel:"projectTimeline-module__statusLabel___q-nsG",statusDescription:"projectTimeline-module__statusDescription___89mWR",statusIcon:"projectTimeline-module__statusIcon___0WqMX"};S('.projectTimeline-module__container___dw722{background-color:var(--color-bg-white);border:1px solid var(--color-primary-pale-charcoal);border-radius:.25rem;padding:1rem}.projectTimeline-module__header___8uyuJ{align-items:center;display:flex;gap:.5rem;justify-content:space-between}.projectTimeline-module__editContainer___xNbAf{color:var(--color-secondary-cobalt);&:hover{text-decoration:underline}}.projectTimeline-module__editContainer___xNbAf,.projectTimeline-module__legend___rIekh{align-items:center;cursor:pointer;display:flex;font-weight:600;gap:.5rem;padding:0}.projectTimeline-module__legendTitle___LhDC6{color:var(--color-secondary-cobalt);font-weight:600;text-align:end}.projectTimeline-module__legend___rIekh p{font-size:1rem;margin-top:0}.projectTimeline-module__timeline___CibE-{padding-left:.5rem;padding-right:1rem;padding-top:1.5rem}.projectTimeline-module__stageWrapper___kknp9{width:100%}.projectTimeline-module__stage___CV8Sp{display:flex}.projectTimeline-module__subStageTrigger___h1sMH{align-items:center;display:flex;justify-content:space-between;padding:.35rem .5rem;text-align:left;transition:background-color .2s;width:100%;&:hover{background-color:var(--color-primary-soft-charcoal)}}.projectTimeline-module__title___29J3R{color:var(--color-primary-charcoal);font-size:1.25rem;font-weight:600;line-height:var(--lineheight-body);margin:0}.projectTimeline-module__info___xB8Cw{display:flex;gap:1rem;width:100%}.projectTimeline-module__stageTitle___3kR3O{color:var(--color-primary-charcoal);font-weight:600}.projectTimeline-module__stageSubtitle___qLoeQ{color:var(--color-primary-inactive-charcoal);font-weight:400;margin:0}.projectTimeline-module__subStageSubtitle___bDU6A{color:var(--color-primary-inactive-charcoal);font-weight:400;margin-bottom:.5rem;margin-top:0}.projectTimeline-module__icon___qSiod,.projectTimeline-module__sub-stage-icon___639Tt{padding-top:.75rem;position:relative}.projectTimeline-module__icon___qSiod div[aria-label=Icon],.projectTimeline-module__sub-stage-icon___639Tt div[aria-label=Icon]{background-color:#fff;padding:.25rem 0}.projectTimeline-module__accordionTrigger___prk26{align-self:baseline;margin-top:1rem;padding:0}.projectTimeline-module__subStageIcon___1e-Mv{padding-top:1rem}.projectTimeline-module__finalStage___O0-a5{margin-top:.75rem;position:relative}.projectTimeline-module__finalStage___O0-a5 div[aria-label=Icon]{background-color:#fff;padding:.25rem 0}.projectTimeline-module__icon___qSiod:after{background-color:var(--color-bg-grey);content:"";height:100%;left:50%;position:absolute;transform:translateX(-50%);width:2px}.projectTimeline-module__lastUpdated___KGAya{margin-top:.5rem}.projectTimeline-module__tooltipWrapper___DoXgB{display:flex;position:relative}.projectTimeline-module__tooltipTrigger___a-f4-{display:inline-block}.projectTimeline-module__tooltipCard___Zpc31{background-color:#fff;box-shadow:0 2px 4px 0 rgba(0,0,0,.25);margin:0;position:absolute;right:-4rem;top:-.5rem;width:20rem;z-index:300}.projectTimeline-module__textBlock___M870a{margin-left:.5rem}.projectTimeline-module__timelineStatus___rjr5G{cursor:default;font-weight:600;margin:1rem 1rem .5rem}.projectTimeline-module__statusContainer___jtdhh{display:flex;padding:.75rem 1rem}.projectTimeline-module__statusContainer___jtdhh:last-child{padding-bottom:1.25rem}.projectTimeline-module__statusLabel___q-nsG{color:#333;font-size:16px;font-weight:400;letter-spacing:0;line-height:24px;vertical-align:middle}.projectTimeline-module__statusDescription___89mWR{color:#666;font-size:14px;font-weight:400;letter-spacing:-.1px;line-height:20px;vertical-align:middle}.projectTimeline-module__statusIcon___0WqMX{padding-right:.75rem;padding-top:.25rem}');var Wc=function(e){var r=e.id,o=e.title,n=e.translationKey,l=e.startDate,i=e.endDate,d=e.status,s=e.substages,_=e.isSubstage,m=void 0!==_&&_,u=e.isLastStage,p=void 0!==u&&u,g=t.useState(!1),h=g[0],f=g[1],b=d.replace(/[\s_]/g,"").toLocaleLowerCase()+"-icon",v=c.useTranslation("projectStages"),w=v.t,y=v.i18n,x=""!==n?w(n):o,E=s&&s.length>0,C=[];l&&C.push(l),i&&C.push(i);var k=new Set(C);return a.createElement("div",{className:Fc.stage,id:r},a.createElement("div",{className:Fc.info},a.createElement("span",{className:p?Fc.finalStage:m?Fc.subStageIcon:Fc.icon},a.createElement(nl,{iconName:b,altText:d})),a.createElement("div",{className:Fc.stageWrapper},E?a.createElement(a.Fragment,null,a.createElement("button",{className:Fc.subStageTrigger,onClick:function(){return f((function(e){return!e}))},id:"accordion-trigger-"+o,"aria-controls":"accordion-box-"+o,"aria-expanded":h,"aria-label":w(h?"substage-collapse":"substage-expand",{stageName:x+" ("+vl(k,y.language)+")"})},a.createElement("div",null,a.createElement("span",{className:Fc.stageTitle},x),a.createElement("p",{className:m?Fc.subStageSubtitle:Fc.stageSubtitle},vl(k,y.language))),a.createElement(nl,{iconName:h?"chevron-up":"chevron-down"})),h&&a.createElement("div",{className:Fc.subStage,"aria-labelledby":"accordion-trigger-"+o,id:"accordion-box-"+o,role:"region"},null==s?void 0:s.map((function(e,t){return a.createElement(Wc,Object.assign({key:t+"-substate"},e))})))):a.createElement("div",null,a.createElement("span",{className:Fc.stageTitle},x),a.createElement("p",{className:m?Fc.subStageSubtitle:Fc.stageSubtitle},vl(k,y.language))))))},Uc={toast:"toast-module__toast___VzLw4",show:"toast-module__show___4kh3U",hide:"toast-module__hide___wlkku",toastContainer:"toast-module__toastContainer___ezIsR",toastText:"toast-module__toastText___30bSc",toastTitle:"toast-module__toastTitle___9nL48",toastCTA:"toast-module__toastCTA___8E4yf",toastMessage:"toast-module__toastMessage___V1XKp",success:"toast-module__success___cuSIT",error:"toast-module__error___eKgSe",info:"toast-module__info___ABLCP"};S(".toast-module__toast___VzLw4{align-items:center;border-radius:2px;color:var(--color-primary-charcoal);cursor:pointer;display:flex;gap:1rem;margin-top:1rem;opacity:.9;opacity:0;padding:.65rem 1rem;transform:translateX(100%);transition:transform .3s ease-in-out,opacity .3s ease-in-out}.toast-module__toast___VzLw4.toast-module__show___4kh3U{opacity:1;transform:translateX(0)}.toast-module__toast___VzLw4.toast-module__hide___wlkku{opacity:0;transform:translateX(100%)}.toast-module__toastContainer___ezIsR{align-items:center;display:flex;gap:1rem;justify-content:space-between;width:100%}.toast-module__toastText___30bSc{display:flex;flex-direction:column;gap:.15rem}.toast-module__toastTitle___9nL48{font-weight:600}.toast-module__toastCTA___8E4yf{color:var(--color-secondary-cobalt);cursor:pointer;font-weight:700}.toast-module__toastMessage___V1XKp,.toast-module__toastTitle___9nL48{font-size:var(--fontsize-body);line-height:var(--lineheight-body);margin:0;word-break:break-word}.toast-module__toastMessage___V1XKp{display:flex;gap:1rem}.toast-module__success___cuSIT{background-color:var(--color-bg-light-emerald);border:1px solid var(--color-secondary-emerald)}.toast-module__error___eKgSe{background-color:var(--color-bg-light-burgundy);border:1px solid var(--color-secondary-burgundy)}.toast-module__info___ABLCP{background-color:var(--color-bg-light-cobalt);border:1px solid var(--color-secondary-cobalt)}@media (max-width:768px){.toast-module__toast___VzLw4.toast-module__show___4kh3U{opacity:1;transform:translateY(0)}.toast-module__toast___VzLw4.toast-module__hide___wlkku{opacity:0;transform:translateY(100%)}}@media (min-width:768px){.toast-module__toast___VzLw4{max-width:700px}}"),S(".toggleListItem-module__toggleListItem___HVP-i{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:1rem 1.5rem}.toggleListItem-module__toggleListItemWithBorder___wSssQ{border-bottom:1px solid var(--color-border-light-grey)}.toggleListItem-module__toggleListInfo___wKXno{margin-right:1rem}.toggleListItem-module__titleContainer___0Mrv7{align-items:center;color:var(--color-secondary-cobalt);display:flex;flex-direction:row;font-weight:400}.toggleListItem-module__title___I9cUl{color:var(--color-primary-charcoal);font-size:var(--fontsize-body);line-height:var(--lineheight-body);margin-right:.5rem}.toggleListItem-module__subtitle___wjFnm{color:var(--color-primary-light-charcoal);font-size:var(--fontsize-body-small);line-height:var(--lineheight-body-small)}"),S(".userSelect-module__container___MJlZe{position:relative;width:100%}.userSelect-module__container___MJlZe [data-part=input]{background-color:var(--color-primary-white);border:1px solid var(--color-bg-primary-pale-charcoal);border-radius:2px;color:var(--color-primary-charcoal);display:block;font-family:var(--primary-font-family);font-size:var(--fontsize-body);height:2.5rem;padding:.5rem 1rem;transition:border-color .2s ease-in-out,background-color .2s ease-in-out;width:100%}.userSelect-module__container___MJlZe [data-part=input]::placeholder{color:var(--color-primary-inactive-charcoal)}.userSelect-module__container___MJlZe [data-part=input]:hover{background-color:var(--color-primary-soft-charcoal)}.userSelect-module__container___MJlZe [data-part=input]:focus,.userSelect-module__container___MJlZe [data-part=input][data-state=open]{border-color:var(--color-primary-charcoal);outline:none}.userSelect-module__container___MJlZe [data-part=input]:focus-visible.focusedByKeyboard{outline:2px solid var(--color-secondary-cobalt);outline-offset:2px}.userSelect-module__container___MJlZe [data-part=control]{position:relative}.userSelect-module__container___MJlZe .userSelect-module__selectedAvatar___xvQiJ{font-weight:700;left:1rem;pointer-events:none;position:absolute;top:50%;transform:translateY(-50%)}.userSelect-module__container___MJlZe .userSelect-module__selectedAvatar___xvQiJ+[data-part=input]{padding-left:3rem}.userSelect-module__positioner___mBs40{z-index:var(--z-index-dropdown-menu)!important}.userSelect-module__content___GgJJ2{background-color:var(--color-bg-white);border-radius:2px;box-shadow:0 .25rem .5rem rgba(0,0,0,.1);max-height:12rem;overflow-x:hidden;overflow-y:auto}.userSelect-module__content___GgJJ2 [data-part=item]{align-items:center;color:var(--color-primary-charcoal);display:flex;font-size:.875rem;font-weight:600;gap:.5rem;padding:.75rem 1rem}.userSelect-module__content___GgJJ2 [data-highlighted]{background-color:var(--color-bg-light-grey);cursor:pointer}.userSelect-module__noResults___X1gAU{color:var(--color-primary-charcoal);padding:.75rem 1rem;text-align:center}.userSelect-module__visuallyHidden___69iLr{clip:rect(0 0 0 0);clip-path:inset(50%);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}"),exports.Accordion=function(e){var r=e.id,o=e.title,n=e.subtitle,l=e.children,i=e.defaultOpen,c=e.dataTestId,d=void 0===c?"uikit-accordion":c,s=e.role,_=e.ariaLabel,m=e.ariaDescribedBy,u=e.ariaLabelledBy,p=e.tabIndex,g=t.useState(void 0!==i&&i),h=g[0],f=g[1];return a.createElement("div",{className:Wl.accordion,"data-scope":"accordion","data-state":h?"open":"closed","data-testid":d,role:s,"aria-describedby":m,"aria-labelledby":u,tabIndex:p},a.createElement("button",{id:r,"aria-expanded":h,"aria-controls":r+"-content",className:Wl.accordionHeader,"data-scope":"accordion","data-part":"header","aria-label":_,onClick:function(){return f(!h)}},a.createElement("div",{className:Wl.chevronWrapper},a.createElement("div",{"data-scope":"accordion","data-part":"chevron","data-state":h?"open":"closed"},a.createElement(nl,{iconName:"chevron",altText:"chevron"}))),a.createElement("div",{className:Wl.accordionTitles},a.createElement("div",{className:Wl.title,"aria-label":o},o),n&&a.createElement("div",{className:Wl.subtitle,"aria-label":n},n))),a.createElement("div",{role:"region",id:r+"-content","data-scope":"accordion","aria-labelledby":r,"data-part":"content","data-state":h?"open":"closed"},l))},exports.ActionItemChip=function(e){return a.createElement(Fl,{text:e.text,onClickAction:e.onClickAction,type:exports.ChipType.ACTION,iconName:e.iconName})},exports.AssigneeTooltip=function(e){var t,o=e.message,n=e.direction,l=e.dataTestId,i=e.ariaLabel,c=e.withArrow,s=void 0===c||c,_=e.smallSize,m=void 0!==_&&_,u=e.wide,p=void 0!==u&&u;return a.createElement("div",{role:e.role,tabIndex:e.tabIndex,"data-testid":void 0===l?"uikit-assignee-tooltip":l},a.createElement(d.Tooltip.Root,{id:e.inputId,positioning:{placement:Ul[null!=n?n:"up"]},closeDelay:e.closeDelay,openDelay:e.openDelay,"aria-label":void 0===i?"Assgnee-Tooltip":i,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy},a.createElement(d.Tooltip.Trigger,{asChild:!0},a.createElement("span",null,e.children)),a.createElement(d.Portal,null,a.createElement(d.Tooltip.Positioner,null,o&&a.createElement(d.Tooltip.Content,{className:r("assigneeTooltip-module__tooltip___NxT7N",(t={},t["assigneeTooltip-module__smallSize___Bj-qG"]=m,t["assigneeTooltip-module__wide___Tc8XE"]=p,t))},s&&a.createElement(d.Tooltip.Arrow,{className:"assigneeTooltip-module__arrow___YD-EX"},a.createElement(d.Tooltip.ArrowTip,null)),o)))))},exports.AttachmentHistory=function(e){var t=e.attachmentHistories,o=e.dataTestId,l=void 0===o?"uikit-attachmentHistory":o,i=e.role,d=e.ariaLabel,s=void 0===d?"Attachment History":d,_=e.ariaDescribedBy,m=e.ariaLabelledBy,u=e.tabIndex,p=e.type,g=e.isPendingStatus,h=void 0!==g&&g,f=c.useTranslation().i18n,b=c.useTranslation("actionItems").t,v=t.length>0;return a.createElement("div",{className:Qe.attachmentHistoryWrapper,"data-testid":l,role:i,"aria-label":s,"aria-describedby":_,"aria-labelledby":m,tabIndex:u},a.createElement("div",{className:Qe.title},b("Signature"===p?"attachments":"uploaded-documents")),v?a.createElement("ul",{className:Qe.historyItems},t.map((function(e,t){var o;return a.createElement("li",{className:Qe.historyItem,key:t},a.createElement("div",{className:Qe.leftHandInfo},a.createElement("div",{className:Qe.leftIconWrapper},a.createElement(nl,{iconName:"documents-icon",size:16})),a.createElement("div",{className:Qe.documentName},e.name)),a.createElement("div",{className:r(Qe.rightHandInfo,Qe["rightHandInfo--"+(null==e||null==(o=e.btnConfig)?void 0:o.type)])},h?a.createElement(Fl,{text:b("Pending all signatures"),type:exports.ChipType.STATUS,textOnly:!0}):e.btnConfig?a.createElement(_l,{id:t+"-"+e.name,type:e.btnConfig.type,label:e.btnConfig.label,onClick:function(){var t;null!=(t=e.btnConfig)&&t.onClick&&e.btnConfig.onClick(e.url)},size:e.btnConfig.size,rightIconName:e.btnConfig.rightIconName,withBorder:e.btnConfig.type===exports.ButtonTypeEnum.secondary,loading:e.btnConfig.isLoading,disabled:e.btnConfig.disabled}):a.createElement(a.Fragment,null,e.author&&a.createElement("div",{className:Qe.nameContainer},a.createElement("span",null,e.author),a.createElement("span",null,e.timeCreated?" • "+bl(n.formatISO(e.timeCreated),f.language,!1,!0):"")),a.createElement("button",{className:Qe.downloadIcon,"aria-label":b("download-document",{name:e.name}),onClick:function(){return xl(e.url,e.name)}},a.createElement(nl,{iconName:"arrow-download",size:20})))))}))):a.createElement("div",{className:Qe.noDocumentsUploaded},b("upload-empty")))},exports.AutoComplete=function(e){var t=e.placeholder,r=e.inputId,o=e.value,n=e.onChange,l=e.type,i=e.dataTestId,c=void 0===i?"uikit-autoComplete":i,s=e.role,_=e.ariaLabel,m=void 0===_?"AutoComplete Input Field":_,u=e.ariaDescribedBy,p=e.ariaLabelledBy,g=e.tabIndex,h=e.isEmailValid,f=e.emailExists,b="email"===(void 0===l?"text":l),v=o.includes("@");return a.createElement(d.Field.Root,{className:"autocomplete-module__autoCompleteWrapper___QFJ8Y","data-testid":c,role:s,"aria-label":m,"aria-describedby":u,"aria-labelledby":p,tabIndex:g},a.createElement(Yl,{placeholder:t,value:o,onValueChange:n,inputId:r,error:b&&v&&(!h||!!f),errorMessage:(b&&v?h?f?"This user has already been invited.":"":"Please enter a valid email address.":"")||void 0}),Boolean(o)&&!f&&v&&a.createElement("span",{className:"autocomplete-module__checkMark___1iTW3"},a.createElement(nl,{iconName:"success-checkmark",altText:"Success Icon"})))},exports.AvatarCard=function(e){var t=e.imageUrl,r=e.name,o=e.titles,n=e.email,l=e.avatarBgColor,i=e.avatarFgColor,c=e.avatarSize,d=void 0===c?"medium":c,s=e.dataTestId,_=e.ariaLabel;return a.createElement("div",{className:Jl.avatarCard,"data-testid":void 0===s?"uikit-avatarCard":s,role:e.role,"aria-label":void 0===_?"Avatar Card":_,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy,tabIndex:e.tabIndex},a.createElement("div",{className:Jl.avatarSection},a.createElement(Vl,{imageUrl:t,initials:El(r),avatarSize:d,bgColor:l,fgColor:i,type:t?"photo":"monogram"})),a.createElement("div",{className:Jl.cardInfo},a.createElement("div",{className:Jl.name},r),o&&o.length>0&&o.map((function(e,t){return a.createElement("div",{className:Jl.cardInfoItem,key:"avatar-card-"+t},a.createElement(nl,{iconName:"briefcase-icon",altText:"title"}),a.createElement("p",null,e))})),a.createElement("div",{className:Jl.cardInfoItem},a.createElement(nl,{iconName:"mail-icon",altText:"email"}),a.createElement("p",null,n))))},exports.Button=_l,exports.ButtonDropdown=function(e){var t,o=e.triggerText,n=e.triggerIcon,l=e.items,i=e.iconColor,c=void 0===i?"charcoal":i,s=e.defaultOpen,_=e.closeOnSelect,m=e.dataTestId,u=e.ariaLabel,p=e.withTaskType,g=void 0!==p&&p,h=e.buttonType,f=void 0===h?"tertiary":h;return a.createElement("div",{className:Zl.buttonDropdownWrapper},a.createElement(d.Menu.Root,{id:e.id,defaultOpen:void 0!==s&&s,closeOnSelect:void 0===_||_,"aria-label":void 0===u?"Button Dropdown":u,"data-testid":void 0===m?"uikit-buttonDropdown":m,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy},a.createElement(d.Menu.Trigger,{className:r(Zl.defaultTrigger,(t={},t[Zl.primaryTrigger]="primary"===f,t[Zl.tertiaryTrigger]="tertiary"===f,t))},o||"New",n&&a.createElement(nl,{iconName:n,altText:n,size:12})),a.createElement(d.Menu.Content,{hidden:!1},l.map((function(e,t){var o,n=e.withIcon,l=void 0===n||n;return a.createElement(d.Menu.Item,{value:e.value,className:r(Zl.buttonDropdownItem,(o={},o[Zl.charcoalIconItem]="charcoal"===c,o)),key:t,"aria-label":e.label,onClick:function(){e.onClick()}},l&&!g&&a.createElement(nl,{iconName:e.iconName,altText:e.iconName}),g&&a.createElement(Ql,{iconName:e.iconName,backgroundColor:e.backgroundColor,width:e.width,height:e.height}),e.children&&e.children,e.label)})))))},exports.CardSelector=function(e){var r=e.selectors,o=e.activeSelect,n=e.onActiveSelectChange,l=e.dataTestId,i=void 0===l?"uikit-cardSelector":l,c=e.role,s=e.ariaLabel,_=e.ariaDescribedBy,m=e.ariaLabelledBy,u=e.tabIndex,p=e.regularRadioInput,g=void 0!==p&&p,h=t.useState(null),f=h[0],b=h[1];return t.useEffect((function(){b(o)}),[o]),a.createElement(d.RadioGroup.Root,{id:"card-selector-group",className:ei.selector,onValueChange:function(e){b(e.value),n(e.value)},"data-testid":i,role:c,"aria-label":s,"aria-describedby":_,"aria-labelledby":m,tabIndex:u},a.createElement("div",{className:g?ei.cardSelectorRegularRadio:ei.cardSelector},r.map((function(e,t){var r,o;return a.createElement($l,{key:t,title:null!=(r=e.title)?r:"Default",description:null!=(o=e.description)?o:"",isActiveSelector:f===e.title,regularRadioInput:g})}))))},exports.Checkbox=ri,exports.CheckboxCard=function(e){var r=e.id,o=e.iconName,n=e.iconSize,l=e.onChange,i=e.label,c=e.disabled,d=void 0!==c&&c,s=e.checked,_=void 0!==s&&s,m=e.size,u=void 0===m?ti.large:m,p=e.dataTestId,g=void 0===p?"uikit-checkboxCard":p,h=e.role,f=e.ariaDescribedBy,b=e.ariaLabelledBy,v=e.tabIndex,w=t.useState(_),y=w[0],x=w[1];t.useEffect((function(){_&&x(_)}),[_]);var E=function(e){e&&e.preventDefault(),d||(l(),x(!y))};return a.createElement("div",{id:r,className:"checkboxCard-module__checkboxCard___6-MvT","data-scope":"checkbox-card","data-disabled":d?"disabled":"active",onClick:function(e){return E(e)},"data-state":_?"checked":"unchecked","data-testid":g,role:h,"aria-labelledby":b,tabIndex:v},a.createElement("div",{className:"checkboxCard-module__labelWrapper___6Ed27"},a.createElement(nl,{iconName:o,size:n,altText:""}),a.createElement("span",{"aria-hidden":"true"},i)),a.createElement(ri,{className:"checkboxCard-module__checkbox___KZ8ke",id:r,disabled:d,value:_,size:u,label:i,ariaLabel:"",ariaDescribedBy:f,onChange:function(){return E()}}))},exports.CheckboxDropdownItem=ii,exports.Chip=Fl,exports.ChipDropdown=function(e){var o=e.triggerLabel,n=e.triggerLabelSecondary,l=void 0===n?"":n,i=e.triggerIconName,s=e.triggerIconSize,_=void 0===s?16:s,m=e.itemType,u=e.items,p=e.defaultOpen,g=e.id,h=e.defaultSelections,f=e.onChange,b=e.includeSearchBar,v=e.searchbarPlaceholder,w=e.onReset,y=e.chevronIconName,x=void 0===y?"chevron-down":y,E=e.includeReset,C=void 0===E?"true":E,k=e.dataTestId,T=void 0===k?"uikit-chipDropdown":k,I=e.onResetTrigger,z=void 0!==I&&I,N=e.menuLabel,S=t.useState(h?new Set(h):new Set),B=S[0],L=S[1],O=t.useState(u),A=O[0],D=O[1],j=t.useState(""),M=j[0],P=j[1],H=c.useTranslation("global").t,R=gl().focusClass,V=t.useCallback((function(e,t){return Array.from(e).some((function(e){return e.id===t.id}))}),[]),F=t.useCallback((function(e){if(P(e),0===e.length)D(u);else{var t=u.filter((function(t){return t.label.toLowerCase().includes(e.toLowerCase())}));D(t)}}),[u]),W=function(e){var t;f(e),m===exports.ChipDropdownItemType.Checkbox?(t=V(B,e)?new Set(Array.from(B).filter((function(t){return t.id!==e.id}))):new Set([].concat(Array.from(B),[e])),L(t)):L(new Set([e]))},U=t.useCallback((function(){null==w||w(),F(""),L(new Set)}),[w,F,L]);t.useEffect((function(){h&&L(new Set(h))}),[h]),t.useEffect((function(){z&&U()}),[z,U]),t.useEffect((function(){D(u)}),[u]);var q=t.useMemo((function(){return function(e,t,r,o,n){switch(e){case exports.ChipDropdownItemType.Checkbox:return t.map((function(e,t){return a.createElement(ii,Object.assign({key:t},e,{checked:V(o,e),firstInitial:"assignee-filter"===n,includeAvatar:"assignee-filter"===n&&"me"!==e.id,onClick:function(){null==e.onClick||e.onClick(e.value),r(e)}}))}));case exports.ChipDropdownItemType.Radio:return t.map((function(e,t){return a.createElement(di,Object.assign({key:t},e,{checked:o.has(e),onClick:function(){null==e.onClick||e.onClick(e.value),r(e)}}))}));default:return[]}}(m,A,W,B,g)}),[A,m,B,g]);return a.createElement("div",{className:ni.chipDropdown,"data-testid":T},a.createElement(d.Popover.Root,{defaultOpen:p,id:g},a.createElement(d.Popover.Trigger,{asChild:!0},a.createElement("button",{className:ni.trigger},i&&a.createElement("div",{className:ni.triggerIconWrapper},a.createElement(nl,{iconName:i,size:_})),a.createElement("div",{className:ni.chipDownLabel},a.createElement("span",{className:ni.chipDownLabelPrimary},o),l&&a.createElement("div",{className:ni.chipDownLabelDivider}),l&&a.createElement("span",{className:ni.chipDownLabelSecondary},l)),a.createElement("div",{className:ni.triggerChevronWrapper},a.createElement(nl,{iconName:x,altText:"chevron-down",size:_})))),a.createElement(d.Popover.Positioner,null,a.createElement(d.Popover.Content,{asChild:!0,className:ni.dropdownMenuItems},a.createElement("div",null,a.createElement(d.Popover.Title,{className:"sr-only"},N),b&&a.createElement("div",{className:ni.searchBarWrapper},a.createElement("div",{className:ni.searchBarIconWrapper},a.createElement(nl,{iconName:"search-icon",size:20})),a.createElement("input",{"aria-label":v,className:r(ni.searchBar,R),placeholder:v,value:M,onChange:function(e){return F(e.target.value)}})),a.createElement("div",{className:ni.dropDownItemsContainer},q),C&&a.createElement("button",{className:ni.resetFilter,onClick:U},H("clear-filter")))))))},exports.CircleButton=mi,exports.Comments=function(e){var r=e.comments,o=e.onCommentDelete,n=e.dataTestId,l=void 0===n?"uikit-Comments":n,i=e.role,d=e.ariaLabel,s=e.ariaDescribedBy,_=e.ariaLabelledBy,m=e.tabIndex,u=e.onCommentUpdate,p=e.onCommentCreate,g=e.userDisplayName,h=e.isCommentingDisabled,f=void 0!==h&&h,b=e.isLoading,v=e.isReady,w=e.onCommentTextChange,y=t.useRef(null),x=t.useRef(!1),E=c.useTranslation("actionItems").t,C=t.useState(""),k=C[0],T=C[1],I=t.useState(),z=I[0],N=I[1];t.useEffect((function(){v&&!x.current?x.current=!0:!b&&v&&setTimeout((function(){var e;null==(e=y.current)||e.focus()}),100)}),[b,v]);var S=function(e){T(e),w&&w(e)};return a.createElement("div",{className:Hi.comments,"data-testid":l,role:i,"aria-label":d,"aria-describedby":s,"aria-labelledby":_,tabIndex:m},a.createElement("div",{className:Hi.header,tabIndex:-1,ref:y},a.createElement("span",{className:Hi.title},E("comments")),b&&a.createElement("span",{className:"sr-only"},E("loading"))),b?a.createElement("div",{className:Hi.spinner},a.createElement(dl,{background:"#f0f0f0",isLoading:!0,message:"Loading..."})):a.createElement(a.Fragment,null,r.length>0&&a.createElement("div",{className:Hi.commentsContainer},r.map((function(e){return a.createElement(Xi,Object.assign({key:e.id},e,{onCommentDelete:o,onCommentUpdate:u}))}))),f?0===r.length?a.createElement("div",{className:Hi.noCommentsContainer},a.createElement("div",{className:Hi.noCommentsSection},a.createElement(nl,{iconName:"comment-icon",altText:"comment-icon",size:20}),a.createElement("div",{className:Hi.noCommentsText},E("no-comments")))):null:a.createElement("div",{className:Hi.newComment},a.createElement("div",{className:Hi.newCommentAvatar},a.createElement(Vl,{type:"monogram",initials:g?El(g):"",avatarSize:"small",fontSize:"x-small"})),a.createElement("div",{className:Hi.newCommentEditor},a.createElement(Ji,Object.assign({ariaLabel:E("add-a-comment"),placeholder:E("add-a-comment"),onChange:function(e){S(e)},maxCharacterCount:1e3,value:k,textSize:"small",onFocus:function(){return N(!0)},onBlur:function(){return N(!1)}},k||z?{footerBtnConfig:{primaryButton:a.createElement(mi,{ariaLabel:E("create-comment"),icon:a.createElement(nl,{iconName:"send-icon"}),inputId:"circle-button",onClick:function(){k&&k.length<=1e3&&(p(k),S(""))},toolTipText:E("create-comment"),disabled:!k||k.length>1e3,isComment:!0,className:Hi.addCommentButton})}}:{}))))))},exports.CopyClipboard=function(e){var r=e.value,o=e.showValue,n=void 0===o||o,l=e.showCopiedMessage,i=void 0===l||l,d=e.dataTestId,s=void 0===d?"uikit-copyToClipboard":d,_=e.role,m=e.ariaLabel,u=void 0===m?"Copy to Clipboard":m,p=e.ariaDescribedBy,g=e.ariaLabelledBy,h=e.tabIndex,f=e.onCopy,b=t.useState(!1),v=b[0],w=b[1],y=c.useTranslation("global").t,x=function(){var e=tt(ot().mark((function e(){return ot().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.clipboard.writeText(r);case 3:w(!0),f&&f(),setTimeout((function(){return w(!1)}),5e3),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Failed to copy!",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}();return a.createElement("div",{className:"copyclipboard-module__copyToClipboard___ohnuh","data-testid":s,role:_,"aria-describedby":p,"aria-labelledby":g,tabIndex:h},v&&i?y("copy-to-clipboard"):a.createElement(a.Fragment,null,n&&a.createElement("p",{className:"copyclipboard-module__copyToClipboardText___mP-dd"},r),a.createElement("button",{"aria-label":u,className:"copyclipboard-module__copyButton___ERldK",onClick:x,disabled:v},a.createElement(nl,{iconName:"copytoclipboard-icon",altText:"clipboard icon"}))))},exports.CustomAvatar=Vl,exports.CustomDatePicker=pi,exports.CustomTooltip=_i,exports.CustomTreeBranch=Ci,exports.CustomTreeView=Ei,exports.DEFAULT_ACCEPTED_EXTENSIONS=zl,exports.DEFAULT_MAX_FILES=5,exports.DEFAULT_MAX_FILE_SIZE=2147483648,exports.DocumentUpload=function(e){var t,o=e.existingFiles,l=void 0===o?[]:o,i=e.id,s=e.title,_=e.subtitle,m=e.subtext,u=e.username,p=e.maxFiles,g=void 0===p?20:p,h=e.onFileAccept,f=e.onFileReject,b=e.onFileDelete,v=e.showDropZone,w=void 0===v||v,y=e.showNoDocumentsBanner,x=void 0!==y&&y,E=e.dataTestId,C=void 0===E?"uikit-documentUpload":E,k=e.role,T=e.ariaLabel,I=void 0===T?"Document Upload":T,z=e.ariaDescribedBy,N=e.ariaLabelledBy,S=e.tabIndex,B=e.noDocumentsBannerText,L=e.required,O=void 0!==L&&L,A=e.children,D=e.showFormRequiredFields,j=void 0===D||D,M=c.useTranslation("actionItems"),P=M.t,H=M.i18n,R=c.useTranslation("global").t,V=c.useTranslation("documents").t,F=wi(),W=d.useFileUpload({id:i||"file-upload",validate:function(e){for(var t,a=Sl(e).errors,r=function(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return(a=a.call(e)).next.bind(a);if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return $e(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?$e(e,t):void 0}}(e))){a&&(e=a);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(a);!(t=r()).done;){var o=t.value;F.create({title:V("failed-to-upload",{fileName:e.name}),type:"error",description:o,errorMessageWithLineBreaks:!0,replaceLineBreaksWithBulletPoints:!0})}return a},onFileChange:function(e){var t=e.rejectedFiles.length>0,a=e.acceptedFiles.length>l.length&&!t,r=e.acceptedFiles.length<l.length;if(t)f&&f();else if(a&&!r){var o=e.acceptedFiles.reduce((function(e,t){var a=t.name+"-"+t.lastModified+"-"+t.size;return e[a]||(e[a]=[]),e[a].length<2?e[a].push(t):W.deleteFile(t),e}),{}),i=Object.values(o).flat(),c=i.slice(i.length-(i.length-l.length)),d=[],s=!1;if(c.forEach((function(e){if(t=e.name,l.some((function(e){return e.file.name===t})))return F.create({type:"error",title:V("failed-to-upload",{fileName:e.name}),description:V("file-already-exists"),errorMessageWithLineBreaks:!0,replaceLineBreaksWithBulletPoints:!0}),W.deleteFile(e),void(s=!0);var t,a={time:bl(n.formatISO(new Date),H.language,!1,!0),file:e,id:e.name};d.push(a)})),s||0===c.length)return;var _=[].concat(l,d);h&&h(_)}},maxFiles:g||5});return a.createElement(a.Fragment,null,a.createElement(d.FileUpload.RootProvider,{value:W,"data-testid":C,role:k,"aria-label":I,"aria-describedby":z,"aria-labelledby":N,tabIndex:S},a.createElement("div",{className:gi.textContainer},a.createElement("div",{className:gi.title},a.createElement(d.FileUpload.Label,{onClick:function(e){return e.preventDefault()}},s),O&&a.createElement("span",{className:gi.titleAsterisk},"*")),_&&a.createElement("p",{className:gi.subtitle},_),m&&a.createElement("p",{className:gi.subtitle},m),O&&j&&0===l.length&&a.createElement("div",{className:gi.errorContainer},a.createElement("div",{className:gi.errorTextContainer},a.createElement(nl,{iconName:"error-circle",altText:"Error Icon",size:20}),a.createElement("p",{className:gi.errorText},R("required-field")))),A),w&&l.length<g&&a.createElement("div",{className:r(gi.dropzoneContainer,(t={},t[gi.dropzoneContainerError]=O&&j&&0===l.length,t))},a.createElement(d.FileUpload.Dropzone,null,a.createElement(nl,{iconName:"document-add",altText:"DocumentAddIcon"}),a.createElement("div",{className:gi.dropdownText},P("drop-files-here")," ",a.createElement("span",{className:gi.dropdownTextBlue},P("browse-files")))),a.createElement(d.FileUpload.HiddenInput,null)),l.length>0?a.createElement(d.FileUpload.ItemGroup,null,l.map((function(e,t){return a.createElement(d.FileUpload.Item,{key:e.file.name+"-"+t,file:e.file},e.isPending?a.createElement("div",{className:gi.pendingIcon},a.createElement(dl,{isLoading:!0})):a.createElement("div",{className:gi.documentIcon},a.createElement(nl,{iconName:"documents-icon",altText:"DocumentPreview"})),a.createElement(d.FileUpload.ItemName,null),a.createElement("div",{className:gi.nameContainer},e.isPending?a.createElement("span",null,R("uploading"),"..."):a.createElement("span",null,u," • ",e.time)),a.createElement(d.FileUpload.ItemDeleteTrigger,{onClick:function(){return function(e){W.deleteFile(e);var t=l.filter((function(t){return t.id!==e.name}));b&&b(t)}(e.file)}},a.createElement(nl,{iconName:"dismiss-icon",altText:"dismiss-icon"})))}))):x?a.createElement("div",{className:gi.noDocumentsContainer},a.createElement("div",{className:gi.noDocumentsInfo},B||P("upload-empty"))):null))},exports.DocumentsNav=function(e){var o,n,l,i=e.id,d=e.items,_=e.projectId,m=e.clientId,u=e.dataTestId,p=void 0===u?"uikit-toast":u,g=e.role,h=e.ariaLabel,f=e.ariaDescribedBy,b=e.ariaLabelledBy,v=e.tabIndex,w=e.isLoading,y=void 0!==w&&w,x=e.updateIsRestricted,E=e.updateFolderItemId,C=e.searchResultsCount,k=c.useTranslation("documents").t,T=s.useSearchParams(),I=T[0],z=T[1],N=t.useState(""),S=N[0],B=N[1],L=t.useRef(null);t.useEffect((function(){var e;B(null!=(e=I.get("search"))?e:"")}),[I]);var O=s.useLocation().pathname.split("/").filter(Boolean).pop(),A=I.get("path"),D="documents"===O&&(!A||"/"===A),j="trash"===O,M="/client/"+m+"/project/"+_+"/documents";return a.createElement("div",{className:ki.parentDiv},a.createElement("div",{className:r(ki.inputParent,(o={},o[ki.parentHideHiglights]=""!=S,o)),"data-testid":p,role:g,"aria-label":h,"aria-describedby":f,"aria-labelledby":b,tabIndex:v},a.createElement(Yl,{inputId:"searchBar",placeholder:k(j?"search-in-recycle":"search-files"),className:ki.input,floatingLabelEnabled:!1,withLeftIcon:!0,leftIconName:"search-icon",onValueChange:function(e){B(e),e?(L.current&&clearTimeout(L.current),L.current=setTimeout((function(){z({search:e})}),300)):z((function(e){return e.delete("search"),e}))},value:S,ariaLabel:k("search-label",{searchTerm:S}),role:"textbox",dataTestId:"uikit-input-documentSearch",showClearSearchTerm:0!==S.length,enableSearch:!0}),a.createElement(Ii,{searchTerm:S,count:null!=C?C:0,isLoading:y})),a.createElement("div",{className:ki.docNavButton},a.createElement(s.Link,{className:r(ki.documentsTitleLink,(n={},n[ki.documentsHighlight]=D,n)),id:"document",to:xi(M,"/")},a.createElement(nl,{iconName:"document-folder",altText:"documents"}),k("documents"))),a.createElement("div",{className:ki.labelParent},a.createElement("span",{className:ki.label},k("folders"))),a.createElement("div",{className:ki.folders},null!=d&&d.length||!y?a.createElement(Ei,{id:i,baseUrl:M,isLoading:y,updateIsRestricted:x,updateFolderItemId:E,items:d}):a.createElement("div",{className:ki.spinnerContainer},a.createElement(dl,{isLoading:!0}))),a.createElement("div",{className:ki.docNavButton,id:"recycleButton"},a.createElement(s.Link,{className:r(ki.documentsTitleLink,(l={},l[ki.documentsHighlight]=j,l)),id:"recycleButton",to:M+"/trash"},a.createElement(nl,{iconName:"recycle-bin",altText:"recycle bin"}),k("recycle-bin"))))},exports.Drawer=function(e){var o,n,l=e.isVisible,i=e.hasOverlay,s=void 0===i||i,_=e.closeDrawer,m=e.headerConfig,u=e.primaryBtnConfig,p=e.secondaryBtnConfig,g=e.children,h=e.withAvatar,f=void 0!==h&&h,b=e.userInitial,v=e.withBackButton,w=void 0!==v&&v,y=e.onBack,x=e.closeOnClickOutside,E=void 0===x||x,C=e.size,k=void 0===C?exports.DrawerSizeEnum.Default:C,T=e.dataTestId,I=void 0===T?"uikit-drawer":T,z=e.ariaLabel,N=void 0===z?"":z,S=e.isCopyable,B=void 0!==S&&S,L=e.onCopyLink,O=e.dropdownMenuItems,A=e.id,D=!!(u&&u.text&&u.onClick),j=!!(p&&p.text&&p.onClick),M=D||j,P=c.useTranslation("global").t,H=t.useRef(null);return a.createElement(d.Dialog.Root,{lazyMount:!0,unmountOnExit:!0,"data-testid":I,restoreFocus:!0,id:A,"aria-label":N,open:l,onOpenChange:function(e){e.open||_()},initialFocusEl:function(){return H.current},closeOnEscape:!0,onEscapeKeyDown:function(){_()},closeOnInteractOutside:E},a.createElement(d.Portal,null,a.createElement(d.Dialog.Positioner,{className:r(zi.drawerWrapper)},s&&a.createElement(d.Dialog.Backdrop,{className:zi.overlay,style:{zIndex:"var(--z-index-drawer-overlay)"}}),a.createElement(d.Dialog.Content,{style:{zIndex:"var(--z-index-drawer)"}},a.createElement("div",{className:r(zi.drawer,(o={},o[zi.large]=k===exports.DrawerSizeEnum.Large,o))},a.createElement("div",{ref:H,className:r(zi.drawerHeader,(n={},n[zi.drawerHeaderTitleOnly]=(null==m?void 0:m.title)&&!(null!=m&&m.subtitle)&&!(null!=m&&m.component),n)),tabIndex:-1},a.createElement("div",{className:zi.drawerHeaderLeft},f&&a.createElement(Vl,{type:"monogram",avatarSize:"medium-small",fontSize:"large",initials:b||""}),w&&a.createElement("button",{className:zi.backButton,onClick:y,"aria-label":P("go-back")},a.createElement(nl,{iconName:"left-arrow",altText:"Back",width:20,height:17})),a.createElement("div",{className:zi.drawerHeaderContent},(null==m?void 0:m.pretitle)&&a.createElement("div",{className:zi.drawerPretitle},m.pretitle),(null==m?void 0:m.title)&&a.createElement("h2",{id:"drawer-title",className:zi.drawerTitle},m.title),(null==m?void 0:m.subtitle)&&a.createElement("div",{className:zi.drawerSubtitle},m.subtitle),(null==m?void 0:m.component)&&a.createElement("div",{className:zi.headerComponentWrapper},m.component))),a.createElement("div",{className:zi.headerButtons},B&&a.createElement(mi,{icon:a.createElement(nl,{iconName:"copy-elipses",altText:P("copy-link"),width:20,height:20}),inputId:"copy-button",ariaLabel:P("copy-link"),toolTipText:P("copy-link"),withArrow:!1,smallTooltip:!0,toolTipCloserToButton:!0,withBorder:!1,onClick:L}),O&&O.length>0&&a.createElement(Si,{iconName:"ellipses-horizontal-icon",id:A+'-"drawer-dropdown-menu"',items:O,onChange:function(){}}),a.createElement(d.Dialog.CloseTrigger,{asChild:!0},a.createElement(mi,{icon:a.createElement(nl,{iconName:"dismiss-icon-thicker",altText:"Dismiss drawer button",size:20}),inputId:"close-modal-button",onClick:_,ariaLabel:P("close-modal")})))),a.createElement("div",{className:zi.drawerContent},g),M&&a.createElement("div",{className:zi.drawerFooter},j&&(null==p?void 0:p.text)&&(null==p?void 0:p.onClick)&&a.createElement(_l,{id:"drawer-secondary-btn",type:exports.ButtonTypeEnum.secondary,size:exports.ButtonSizeEnum.large,label:p.text,onClick:p.onClick,withRightIcon:!1,withBorder:!0,disabled:p.disabled,loading:p.isLoading}),D&&(null==u?void 0:u.text)&&u.onClick&&a.createElement(_l,{id:"drawer-primary-btn",type:exports.ButtonTypeEnum.primary,size:exports.ButtonSizeEnum.large,label:u.text,onClick:u.onClick,withRightIcon:!1,disabled:u.disabled,loading:u.isLoading})))))))},exports.DropdownInput=Oi,exports.DropdownMenu=Si,exports.EditProjectStages=function(e){var o=e.id,l=e.stageName,i=e.startDate,d=e.endDate,s=e.status,m=e.isActive,u=e.subStages,p=e.setStages,g=e.onDeleteSubStage,h=e.onAddSubstage,f=e.onAddCustomSubstage,b=e.substageOptions,v=e.showValidationErrors,w=void 0!==v&&v,y=e.role,x=e.dataTestId,E=void 0===x?"uikit-EditProjectStages":x,C=e.ariaLabel,k=e.ariaDescribedBy,T=e.ariaLabelledBy,I=e.tabIndex,z=c.useTranslation("overview"),N=z.t,S=z.i18n,B=c.useTranslation("dropdown").t,L=c.useTranslation("projectStages").t,O=t.useState(!1),A=O[0],D=O[1],j=t.useState(!1),M=j[0],P=j[1],H=t.useState({top:0,left:0}),R=H[0],V=H[1],F=t.useRef(null),W=t.useState(null),U=W[0],q=W[1],K=t.useState(null),G=K[0],Y=K[1],J=t.useRef(null),Z=t.useRef(null),X=new Date;X=new Date(X.getFullYear(),X.getMonth(),X.getDate(),0,0,0);var Q=function(){if(J.current){var e=J.current.getBoundingClientRect(),t=e.bottom+window.scrollY+4,a=e.left+window.scrollX;a+200>window.innerWidth&&(a=window.innerWidth-200-10),t+80>window.innerHeight+window.scrollY&&(t=e.top+window.scrollY-80-4),V({top:t,left:a})}};t.useEffect((function(){var e=function(e){var t=e.target,a=F.current&&F.current.contains(t),r=Z.current&&Z.current.contains(t);a||r||P(!1)},t=function(){M&&Q()},a=function(){M&&Q()};return M&&(setTimeout((function(){document.addEventListener("mousedown",e)}),0),window.addEventListener("scroll",t,!0),window.addEventListener("resize",a)),function(){document.removeEventListener("mousedown",e),window.removeEventListener("scroll",t,!0),window.removeEventListener("resize",a)}}),[M]);var $=t.useMemo((function(){var e=ji.find((function(e){return e.value==s})),t=null!=e&&e.translationKey?B(e.translationKey+"-label"):null==e?void 0:e.value;return{value:s,label:t,description:null!=e&&e.translationKey&&S.exists("dropdown:"+e.translationKey+"-description")?B(e.translationKey+"-description"):""}}),[s]),ee=t.useMemo((function(){return u.map((function(e){var t,a,r=ji.find((function(t){return t.value==e.status})),o=null!=r&&r.translationKey?B(r.translationKey+"-label"):null==r?void 0:r.value;return at({},e,{defaultSelection:{value:null!=(t=null==r?void 0:r.value)?t:e.status,label:null!=o?o:e.status,icon:null!=(a=null==r?void 0:r.icon)?a:""}})}))}),[u]),te=function(){Y(null)},ae=function(){q(null),Y(null)},re=[];i&&re.push(i),d&&re.push(d);var oe=new Set(re);return a.createElement("div",{className:"editProjectStages-module__container___hI2Tq","data-testid":E,role:y,"aria-label":C,"aria-describedby":k,"aria-labelledby":T,tabIndex:I},a.createElement("div",{className:"editProjectStages-module__firstStage___v5K44"},a.createElement("button",{id:o+"-trigger","aria-controls":o+"-accordion-content","aria-expanded":A,className:"editProjectStages-module__expandButton___n4qqb",onClick:function(){return D(!A)},"aria-label":N(A?"view-substages-collapse":"view-substages-expand",{stageName:l})},a.createElement(nl,A?{iconName:"chevron-down"}:{iconName:"chevron"})),a.createElement(Di,{ariaLabel:N(m?"toggle-stage-no-active":"toggle-stage-to-active",{stageName:l}),isDefaultChecked:m,onCheckedChange:function(){p((function(e){return e.map((function(e){return e.id===o?at({},e,{active:!e.active}):e}))}))}}),a.createElement("div",{className:"editProjectStages-module__details___-iDvU"},a.createElement("div",{className:"editProjectStages-module__stageName___5etl4"},l),a.createElement("div",{className:"editProjectStages-module__subStages___-zUPF"},u.length," substages")),a.createElement("div",{className:"editProjectStages-module__statusDropdown___4eCZ-"},a.createElement(pi,{ariaLabel:"",childrenTrigger:a.createElement("button",{className:"editProjectStages-module__editProjectDateInput___geE4i"},a.createElement(nl,{iconName:"calendar-placeholder",height:14,width:14}),a.createElement("div",{className:"editProjectStages-module__datePlaceholder___6NSKk"},a.createElement("span",{className:"sr-only"},l),a.createElement("span",{className:"sr-only"},N("date-range")),i&&d?vl(oe,S.language):N("date-range")),a.createElement("div",{className:"editProjectStages-module__dropdownArrow___Kfvf7"},a.createElement(nl,{iconName:"dropdown-arrow-down"}))),closeOnSelect:!0,placeholder:"",startDate:i?n.formatISO(i):"",endDate:d?n.formatISO(d):"",selectionMode:"range",onRangeChange:function(e){!function(e){if(e&&2==e.length){var t=e[0],a=e[1];p((function(e){return e.map((function(e){return e.id===o?at({},e,{startDate:t,endDate:a}):e}))}))}}(e)},calendarId:o,onChange:function(){}})),a.createElement("div",{className:"editProjectStages-module__statusDropdown___4eCZ-"},a.createElement(Oi,{ariaLabel:N("status-label",{stageName:l,status:$.label}),items:ji.map((function(e){return at({},e,{label:B(e.translationKey+"-label"),useCustomStyle:!0})})),onSelectionChange:function(e){p((function(t){return t.map((function(t){return t.id===o?at({},t,{status:e.value}):t}))}))},defaultSelection:$,error:!1,withIcon:!0,id:"projectTimeline-"+o+"-status",floatingLabelEnabled:!1,displayOffset:!0,enableSearch:!1,limitDropdownWidth:!1,leftIconPaddingLeft:"2.5rem",dropdownPortalClass:"editProjectStages-module__statusDropdownWrapper___ren5n",showCTA:!1}))),A&&a.createElement("div",{className:"editProjectStages-module__subStageContainer___RSi83",id:o+"-accordion-content",role:"region","aria-labelledby":o+"-trigger"},ee.map((function(e,t){var i,c,d,s=[];e.startDate&&s.push(e.startDate),e.endDate&&s.push(e.endDate);var _=new Set(s),m="TEMPLATE_PLACEHOLDER"===e.title,u=!(m||e.translationKey&&""!==e.translationKey.trim()),h=U===e.id,f=G===t;return a.createElement("div",{key:e.id,className:r("editProjectStages-module__subStage___I3mw4",(i={},i["editProjectStages-module__customSubstageRow___dp1xu"]=u,i["editProjectStages-module__dragging___taGva"]=h,i["editProjectStages-module__dragOver___--I-Z"]=f,i)),draggable:!0,onDragStart:function(t){return function(e,t){q(t),e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/plain",t)}(t,e.id)},onDragOver:function(e){return function(e,t){e.preventDefault(),e.dataTransfer.dropEffect="move",Y(t)}(e,t)},onDragLeave:te,onDrop:function(e){return function(e,t){e.preventDefault();var a=e.dataTransfer.getData("text/plain");a&&(p((function(e){return e.map((function(e){if(e.id===o&&e.substages){var r=[].concat(e.substages),n=r.findIndex((function(e){return e.id===a}));if(-1===n||n===t)return e;var l=r.splice(n,1);r.splice(t,0,l[0]);var i=r.map((function(e,t){return at({},e,{sortOrder:t})}));return at({},e,{substages:i})}return e}))})),q(null),Y(null))}(e,t)},onDragEnd:ae},a.createElement("div",{className:"editProjectStages-module__dragHandle___bF7Zu"},a.createElement(nl,{iconName:"drag-handle",width:16,height:42})),a.createElement("div",{className:"editProjectStages-module__substageNameField___1W2VW"},u?a.createElement(Yl,{placeholder:N("sub-stage"),value:e.title||"",onValueChange:function(t){return a=e.id,r=t,void p((function(e){return e.map((function(e){var t;return e.id===o?at({},e,{substages:null==(t=e.substages)?void 0:t.map((function(e){return e.id===a?at({},e,{title:r}):e}))}):e}))}));var a,r},inputId:"substageTimeline-"+e.id+"-name",floatingLabelEnabled:!1,error:w&&(!e.title||""===e.title.trim()),errorMessage:"",ariaLabel:N("sub-stage")}):a.createElement(Oi,{placeholder:N("sub-stage"),items:b,onSelectionChange:function(t){return function(e,t){p((function(a){return a.map((function(a){var r;return a.id===o?at({},a,{substages:null==(r=a.substages)?void 0:r.map((function(a){return a.id===e?at({},a,{translationKey:t.value,title:t.label||t.value}):a}))}):a}))}))}(e.id,t)},defaultSelection:m?void 0:{value:e.translationKey,label:L(e.translationKey)},ariaLabel:N("sub-stage"),error:w&&(!e.translationKey||""===e.translationKey),errorMessage:"",id:"substageTimeline-"+e.id+"-title",floatingLabelEnabled:!1,enableSearch:!1,displayOffset:!0,limitDropdownWidth:!1,showCTA:!1}),w&&(u&&(!e.title||""===e.title.trim())||!u&&(!e.translationKey||""===e.translationKey))&&a.createElement("div",{className:"editProjectStages-module__inlineErrorMessage___yy2Uc"},a.createElement(nl,{iconName:"error-message-icon",size:20}),u&&N("sub-stage-required"),!u&&N("sub-stage-required"))),a.createElement("div",{className:"editProjectStages-module__substageeDateField___1pdNj"},a.createElement(pi,{ariaLabel:"",childrenTrigger:a.createElement("button",{className:r("editProjectStages-module__editProjectDateInput___geE4i",(c={},c["editProjectStages-module__errorState___3O5HD"]=w&&(!e.startDate||!e.endDate),c))},a.createElement("span",{className:"sr-only"},u?e.title:L(e.translationKey)),a.createElement("span",{className:"sr-only"},N("date-range")),a.createElement(nl,{iconName:"calendar-placeholder",height:14,width:14}),a.createElement("div",{className:r("editProjectStages-module__datePlaceholder___6NSKk",(d={},d["editProjectStages-module__faded___Idd7y"]=!e.startDate||!e.endDate,d))},e.startDate&&e.endDate?vl(_,S.language):N("date-range")),a.createElement("div",{className:"editProjectStages-module__dropdownArrow___Kfvf7"},a.createElement(nl,{iconName:"dropdown-arrow-down"}))),closeOnSelect:!0,placeholder:"",startDate:n.formatISO(e.startDate?e.startDate:X),endDate:n.formatISO(e.endDate?e.endDate:X),selectionMode:"range",onRangeChange:function(t){!function(e,t){if(e&&2==e.length){var a=e[0],r=e[1];p((function(e){return e.map((function(e){var n;return e.id===o?at({},e,{substages:null==(n=e.substages)?void 0:n.map((function(e){return e.id===t?at({},e,{startDate:a,endDate:r}):e}))}):e}))}))}}(t,e.id)},calendarId:l.split(" ").join("_")+"-"+e.id,onChange:function(){},triggerClassName:"editProjectStages-module__dateTrigger___XUi2v"}),w&&(!e.startDate||!e.endDate)&&a.createElement("div",{className:"editProjectStages-module__inlineErrorMessage___yy2Uc"},a.createElement(nl,{iconName:"error-message-icon",size:20}),N("date-range-required"))),a.createElement("div",{className:"editProjectStages-module__substageStatusField___mJPvf"},a.createElement(Oi,{ariaLabel:N("status-label",{stageName:u?e.title:L(e.translationKey),status:e.defaultSelection.label}),items:ji.map((function(e){return at({},e,{label:B(e.translationKey+"-label"),useCustomStyle:!0})})),onSelectionChange:function(t){!function(e,t){p((function(a){return a.map((function(a){var r;return a.id===o?at({},a,{substages:null==(r=a.substages)?void 0:r.map((function(a){return a.id===e?at({},a,{status:t.value}):a}))}):a}))}))}(e.id,t)},defaultSelection:e.defaultSelection,error:w&&(!e.status||""===e.status),errorMessage:"",withIcon:!0,floatingLabelEnabled:!1,id:"substageTimeline-"+e.id,displayOffset:!0,enableSearch:!1,limitDropdownWidth:!1,leftIconPaddingLeft:"2.5rem",dropdownPortalClass:"editProjectStages-module__statusDropdownWrapper___ren5n",showCTA:!1}),w&&(!e.status||""===e.status)&&a.createElement("div",{className:"editProjectStages-module__inlineErrorMessage___yy2Uc"},a.createElement(nl,{iconName:"error-message-icon",size:20}),N("status-required"))),a.createElement("button",{"aria-label":N("remove-substage",{stageName:u?e.title:L(e.translationKey)}),className:"editProjectStages-module__deleteButton___zFJaK",onClick:function(){g(o,e.id)}},a.createElement(nl,{iconName:"recycle-bin"})))})),a.createElement("div",{className:"editProjectStages-module__addSubstageContainer___8Bh8e",ref:F},a.createElement("button",{ref:J,className:"editProjectStages-module__addSubstageButton___YeUVB",onClick:function(){M||Q(),P(!M)},"aria-expanded":M,"aria-haspopup":"true"},N("add-substage"),a.createElement(nl,{iconName:"dropdown-arrow-down"})),M&&_.createPortal(a.createElement("div",{ref:Z,className:"editProjectStages-module__addSubstageDropdown___5DXN6",style:{top:R.top+"px",left:R.left+"px"}},a.createElement("button",{className:"editProjectStages-module__dropdownOption___lGUm-",onMouseDown:function(e){e.preventDefault(),e.stopPropagation(),h(o),P(!1)},onClick:function(e){e.preventDefault(),e.stopPropagation()}},N("add-from-template")),a.createElement("button",{className:"editProjectStages-module__dropdownOption___lGUm-",onMouseDown:function(e){e.preventDefault(),e.stopPropagation(),f(o),P(!1)},onClick:function(e){e.preventDefault(),e.stopPropagation()}},N("create-custom-substage"))),document.body))))},exports.FileDocDetailModal=function(e){var r,o,n=e.id,l=e.sharedWith,i=e.locationFileName,d=e.lastModifiedDate,s=e.lastModifiedBy,_=e.uploadedDate,m=e.uploadedBy,u=e.defaultOpen,p=e.manageAccess,g=e.typeOfModal,h=e.onManageAccess,f=c.useTranslation("documents").t,b=t.useState(u||!1),v=b[0],w=b[1],y=t.useRef(null);return t.useEffect((function(){document.addEventListener("mousedown",(function(e){y.current&&!y.current.contains(e.target)&&w(!1)}))}),[]),a.createElement("div",{className:oc.DocModal,ref:y},a.createElement("button",{id:n,onClick:function(){w(!0)},className:oc.infoButton},a.createElement(nl,{iconName:"info-icon-black-and-white",altText:"Info Icon"})),a.createElement(ic,{id:"file-doc-detail-modal",dataTestId:"uikit-modal-folderInfoDetails",ariaLabel:g==exports.InfoModalType.DOCUMENT?"Document Information":"Folder Information",hide:function(){return w(!1)},allowOverflow:!0,isVisible:v,title:g==exports.InfoModalType.DOCUMENT?f("document-details"):f("folder-details"),size:exports.ModalSize.MINI,closeButton:!1,includeHeaderBorder:!1},a.createElement("div",{className:oc.modalSubtitle},"SHARED WITH"),l&&a.createElement("div",{className:oc.informationContainer},l),p&&a.createElement("a",{className:oc.manageAccess,href:"#",onClick:function(){w(!1),h()}},f("manage-access")),a.createElement(cc,{header:f("location"),chipIcon:"folder-icon",chipName:(r=i,o=r.split("/").filter(Boolean),o[o.length-1]||""),chipSection:!0}),a.createElement("div",{id:"bottom-border-1",className:oc.bottomBorder}),a.createElement(cc,{header:f("last-modified"),inputDate:d,personName:s}),g==exports.InfoModalType.DOCUMENT&&a.createElement("div",null,a.createElement("div",{id:"bottom-border-2",className:oc.bottomBorder}),a.createElement(cc,{header:f("uploaded-by"),inputDate:_,personName:m}))))},exports.GB=1073741824,exports.Icon=nl,exports.ImageCarousel=function(e){var r,o=e.images,n=e.interval,l=e.controlColors,i=void 0!==l&&l,c=e.dataTestId,s=void 0===c?"uikit-imageCarousel":c,_=e.role,m=e.ariaLabel,u=void 0===m?"Image Carousel":m,p=e.ariaDescribedBy,g=e.ariaLabelledBy,h=e.tabIndex,f=e.playButtonLabel,b=e.pauseButtonLabel,v=t.useState(0),w=v[0],y=v[1],x=t.useState(!0),E=x[0],C=x[1];!function(e,a,r,o,n){void 0===n&&(n=1e4);var l=t.useRef(null);t.useEffect((function(){return e&&(l.current=setTimeout((function(){o((function(e){return(e+1)%a}))}),n)),function(){l.current&&clearTimeout(l.current)}}),[e,r,a,o,n])}(E,o.length,w,y,n);var k="carousel-module__carouselButton___gw5uI "+(i?"carousel-module__carouselButtonControlColors___cp7k6":null),T="carousel-module__pausePlayButton___qVisS "+(i?"carousel-module__pausePlayButtonControlColors___otr-9":null);return a.createElement(d.Carousel.Root,{index:w,onIndexChange:function(e){return y(e.index)},loop:!0,className:"carousel-module__carousel___ealh-","data-testid":s,role:_,"aria-label":u,"aria-describedby":p,"aria-labelledby":g,tabIndex:h},a.createElement(d.Carousel.Viewport,{className:"carousel-module__carousel-viewport___JD9Sy"},a.createElement(d.Carousel.ItemGroup,{className:"carousel-module__carousel-item-group___2tOEr"},o.map((function(e,t){return a.createElement(d.Carousel.Item,{key:t,index:t,className:"carousel-module__carousel-item___fnwSS "+(w===t?"carousel-module__carousel-item-active___y0w-U":"")},a.createElement("img",{src:e.src,alt:"Slide "+t,className:"carousel-module__carousel-img___rba07"}))})))),a.createElement(d.Carousel.Control,{className:"carousel-module__carousel-controls___pdqjL"},a.createElement("div",{className:"carousel-module__carousel-text___R-srU"},(null==(r=o[w])?void 0:r.text)||""),a.createElement("div",{className:"carousel-module__carousel-buttons___W5ILr"},a.createElement(d.Carousel.PrevTrigger,{onClick:function(){return y((w-1+o.length)%o.length)},className:k,tabIndex:0},a.createElement(nl,{iconName:"left-arrow",altText:"previous-icon"})),a.createElement("div",{className:T},a.createElement("button",{onClick:function(){C((function(e){return!e}))},"aria-label":E?b:f},a.createElement(nl,E?{iconName:"pause-icon",altText:"pause-icon"}:{iconName:"play-icon",altText:"play-icon"}))),a.createElement(d.Carousel.NextTrigger,{onClick:function(){return y((w+1)%o.length)},className:k,tabIndex:0},a.createElement(nl,{iconName:"right-arrow",altText:"next-icon"})))))},exports.InlineMessage=function(e){var t,o=e.type,n=void 0===o?"info":o,l=e.iconSize,i=void 0===l?24:l,c=e.title,d=e.message,s=void 0===d?"Message Summary":d,_=e.dataTestId,m=e.ariaLabel,u=e.children,p=e.withIcon;return a.createElement("div",{className:Mi.inlineMessage+" "+Mi[n],"data-testid":void 0===_?"uikit-inlineMessage":_,role:e.role,"aria-label":void 0===m?"":m,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy,tabIndex:e.tabIndex},(void 0===p||p)&&a.createElement(nl,{iconName:e.iconName||Pi[n],altText:n+" icon",width:i||e.width||16,height:i||e.height||16}),a.createElement("div",{className:r(Mi.inlineMessageContent,(t={},t[Mi.inlineMessageNoTitle]=!c,t))},c&&a.createElement("p",{className:Mi.inlineMessageTitle},c),s&&a.createElement("p",{className:Mi.inlineMessageText},s),u))},exports.Input=Yl,exports.InviteUserCard=Qi,exports.LiveAnnouncer=Ii,exports.LocationItemChip=function(e){return a.createElement(Fl,{text:e.text,onClickAction:e.onClickAction,type:exports.ChipType.LOCATION,iconName:e.iconName,id:e.id})},exports.MAX_COMMENT_CHARACTER_COUNT=1e3,exports.MILLISECONDS_IN_DAY=864e5,exports.MILLISECONDS_IN_HOUR=36e5,exports.MILLISECONDS_IN_MINUTE=6e4,exports.MenuItem=function(e){var t,o,n=e.label,l=e.link,i=e.iconName,c=e.backgroundColorSet,d=void 0!==c&&c,_=e.onClick,m=void 0===_?function(){}:_,u=e.active,p=e.dataTestId,g=void 0===p?"uikit-menuItem":p,h=e.role,f=e.ariaLabel,b=e.ariaDescribedBy,v=e.ariaLabelledBy,w=e.tabIndex,y=s.useLocation(),x=u||y.pathname.includes(l);return a.createElement("li",{"data-testid":g,role:h,"aria-label":f,"aria-describedby":b,"aria-labelledby":v,tabIndex:w,onClick:m},a.createElement(s.Link,{to:l,className:r($i.link,(t={},t[$i.highlighted]=d,t[$i.menuItemBackgroundColor]=d,t[$i.active]=x&&d,t))},a.createElement(nl,{iconName:i,altText:n+" Icon"}),a.createElement("span",{className:r((o={},o[$i.activeLink]=x&&d,o),$i.longLabel)},n)))},exports.Modal=ic,exports.NotificationTypeBadge=function(e){var t,r=e.type,o=e.dataTestId,n=void 0===o?"uikit-notificationTypeBadge":o,l=e.role,i=e.ariaLabel,d=void 0===i?"Notification Type Badge":i,s=e.ariaDescribedBy,_=e.ariaLabelledBy,m=e.tabIndex,u=c.useTranslation("notifications").t,p=((t={})[exports.NotificationBadgeType.ACTION_ITEM]=u("action-item"),t[exports.NotificationBadgeType.DELIVERABLE]=u("deliverable"),t[exports.NotificationBadgeType.PROJECT_HEALTH_STATUS]=u("project-health-status"),t[exports.NotificationBadgeType.GENERAL]=u("general"),t);return a.createElement("div",{className:"notificationTypeBadge-module__notificationTypeBadge___AuyV5",style:{backgroundColor:dc[r]},"data-testid":n,role:l,"aria-label":d,"aria-describedby":s,"aria-labelledby":_,tabIndex:m},a.createElement("span",{className:"notificationTypeBadge-module__notificationTypeBadgeText___fFRXj"},p[r]))},exports.NotificationTypeToBadgeColorMap=dc,exports.ProgressBar=function(e){var t=e.currentStep,o=e.dataTestId,n=e.ariaLabel;return a.createElement("div",{className:"progressBar-module__progressContainer___ibF48","data-testid":void 0===o?"uikit-progressBar":o,role:e.role,"aria-label":void 0===n?"":n,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy,tabIndex:e.tabIndex},e.steps.map((function(e,o){var n;return a.createElement("div",{key:o,className:r("progressBar-module__progressSection___rY7dm",(n={},n["progressBar-module__active___ESJ6E"]=o<=t,n))})})))},exports.ProgressCompletion=function(e){var r=e.title,o=e.percentage,n=e.updatedDateTime,l=e.isClient,i=void 0===l||l,d=e.dataTestId,s=void 0===d?"uikit-progressCompletion":d,_=e.role,m=e.ariaLabel,u=e.ariaDescribedBy,p=e.ariaLabelledBy,g=e.tabIndex,h=e.onClick,f=e.setPercentage,b=t.useState(!1),v=b[0],w=b[1],y=t.useState(""+o),x=y[0],E=y[1],C=c.useTranslation("global").t,k=function(){var e=tt(ot().mark((function e(){var t;return ot().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=parseFloat(x),isNaN(t)||!(t<=100)){e.next=13;break}return f(t),e.prev=3,e.next=6,h(t);case 6:w(!1),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(3),w(!1),console.error(e.t0);case 13:case"end":return e.stop()}}),e,null,[[3,9]])})));return function(){return e.apply(this,arguments)}}();return t.useEffect((function(){E(""+o)}),[o]),a.createElement("div",{className:"progressCompletion-module__container___zzhpF","data-testid":s,role:_,"aria-label":m,"aria-describedby":u,"aria-labelledby":p,tabIndex:g},a.createElement("div",{className:"progressCompletion-module__header___jRfpe"},a.createElement("h3",{className:"progressCompletion-module__title___SEWuS"},r),!i&&a.createElement("button",{"aria-expanded":v,"aria-haspopup":"dialog","aria-controls":"progress-dialog",className:"progressCompletion-module__editContainer___p5VeB",onClick:function(){return w(!0)}},C("edit")),a.createElement(ic,{id:"progress-dialog",isVisible:v,hide:function(){return w(!1)},allowOverflow:!1,title:C("edit-progress"),primaryBtnConfig:{id:"primary-button",label:C("save"),type:exports.ButtonTypeEnum.primary,onClick:k,withRightIcon:!1,size:exports.ButtonSizeEnum.large,withLeftIcon:!1,disabled:parseFloat(x)===o||!x},secondaryBtnConfig:{id:"secondary-btn",label:C("cancel"),type:exports.ButtonTypeEnum.tertiary,onClick:function(){E(""+o),w(!1)},size:exports.ButtonSizeEnum.large,withRightIcon:!1,withLeftIcon:!1},footerBtnAlignment:exports.FooterButtonAlignment.END,size:exports.ModalSize.SMALL},a.createElement("div",{className:"progressCompletion-module__modalChildren___D1wRq"},a.createElement("div",{className:"progressCompletion-module__progressBarContainer___HcbzG progressCompletion-module__progressBarContainerModal___ShD9C"},a.createElement("div",{className:"progressCompletion-module__progressBar___gAcCv",style:{width:x+"%"}})),a.createElement("div",{className:"progressCompletion-module__inputWrapper___lTTvh"},a.createElement(Yl,{ariaLabel:C("project-completion-label"),inputId:"percentage",value:x,onValueChange:function(e){return function(e){if(/^\d{0,3}$/.test(e)){var t=parseInt(e,10);!isNaN(t)&&t>100?E("100"):E(e)}}(e)},floatingLabelEnabled:!1}),a.createElement("div",null,C("progress-percentage")))))),a.createElement("div",{className:"progressCompletion-module__lastUpdatedContainer___couy8"},C("last-updated")," ",n),a.createElement("div",{className:"progressCompletion-module__progressWrapper___N8-H6"},a.createElement("span",{className:"progressCompletion-module__percentage___EIx-P"},o,C("progress-percentage"))),a.createElement("div",{className:"progressCompletion-module__progressBarContainer___HcbzG"},a.createElement("div",{className:"progressCompletion-module__progressBar___gAcCv",style:{width:o+"%"}})))},exports.ProjectStage=Wc,exports.ProjectTimeline=function(e){var t=e.title,r=e.stages,o=e.lastUpdated,n=e.isClient,l=void 0!==n&&n,i=e.isEditMode,s=e.dataTestId,_=void 0===s?"uikit-projectTimeline":s,m=e.role,u=e.ariaLabel,p=e.ariaDescribedBy,g=e.ariaLabelledBy,h=e.tabIndex,f=c.useTranslation("overview").t,b=c.useTranslation("global").t,v=c.useTranslation("dropdown").t;return a.createElement("div",{className:Fc.container,"data-testid":_,role:m,"aria-label":u,"aria-describedby":p,"aria-labelledby":g,tabIndex:h},a.createElement("div",{className:Fc.header},a.createElement("h3",{className:Fc.title},t),l?a.createElement(d.Popover.Root,{id:"status-legend"},a.createElement(d.Popover.Trigger,{asChild:!0},a.createElement("button",{className:Fc.legend},a.createElement("span",{className:Fc.legendTitle},f("status-legend")),a.createElement("div",{className:Fc.tooltipTrigger},a.createElement(nl,{iconName:"help-icon",altText:"",color:"#0062b8"})))),a.createElement(d.Popover.Positioner,null,a.createElement(d.Popover.Content,{className:Fc.tooltipCard},a.createElement(d.Popover.Title,{className:Fc.timelineStatus},f("timeline-statuses")),a.createElement(d.Popover.Description,null,a.createElement("div",null,ji.map((function(e,t){return a.createElement("div",{className:Fc.statusContainer,key:"status-"+t},a.createElement("div",{className:Fc.statusIcon},a.createElement(nl,{iconName:e.icon,altText:v(e.translationKey+"-label")+" icon"})),a.createElement("div",null,a.createElement("div",{className:Fc.statusLabel},v(e.translationKey+"-label")),a.createElement("div",{className:Fc.statusDescription},v(e.translationKey+"-description"))))}))))))):a.createElement("button",{className:Fc.editContainer,onClick:function(){i()}},b("edit"))),o&&a.createElement("div",{className:Fc.lastUpdated},b("last-updated")," ",o),a.createElement("div",{className:Fc.timeline},r.map((function(e,t){if(!e.active)return null;var o=r.slice(t+1).some((function(e){return e.active}));return a.createElement(Wc,Object.assign({},e,{id:e.id,key:e.id+"-"+t,status:e.status,isSubstage:e.isSubstage,isLastStage:!o}))}))))},exports.RadioDropdownItem=di,exports.SearchDropdown=function(e){var o,n=e.suggestedUsers,l=e.onUserAdd,i=e.onUserRemove,s=e.selectedUsers,_=e.readOnlyUserIds,m=e.disabled,u=void 0!==m&&m,p=e.dataTestId,g=void 0===p?"uikit-sectionDropdown":p,h=e.ariaLabel,f=e.ariaDescribedBy,b=e.ariaLabelledBy,v=e.defaultLabel,w=e.useMaxWidth,y=void 0===w||w,x=e.isRequired,E=void 0!==x&&x,C=e.showFormRequiredFields,k=e.showName,T=void 0===k||k,I=e.showEmail,z=void 0===I||I,N=e.allowUnverifiedUsers,S=void 0===N||N,B=e.maxAssignees,L=e.maxResults,O=void 0===L?100:L,A=e.id,D=e.className,j=e.errorOnBlur,M=void 0===j||j,P=c.useTranslation("global").t,H=c.useTranslation("actionItems").t,R=t.useState(""),V=R[0],F=R[1],W=t.useState(!1),U=W[0],q=W[1],K=gl().focusClass,G=s.length>0,Y=s.length===B,J=C&&0===s.length,Z=t.useMemo((function(){return n.filter((function(e){var t;return((null==(t=e.name)?void 0:t.toLowerCase().includes(V.toLowerCase()))||e.email.toLowerCase().includes(V.toLowerCase()))&&-1===s.findIndex((function(t){return t.uniqueUserId===e.uniqueUserId}))})).slice(0,O)}),[V,n,s]),X=V&&0===Z.length&&S,Q=d.useCombobox({id:A,allowCustomValue:S,items:Z.map((function(e){return e.email})),multiple:!0,closeOnSelect:!0,openOnClick:!0,onValueChange:function(e){var t,a,r,o,i=e.value;t=i[i.length-1],r=n.find((function(e){return e.email===t})),o={email:t,displayText:null!=r&&null!=(a=r.name)&&a.replace(" ","")?r.name:t,uniqueUserId:null==r?void 0:r.uniqueUserId,chipType:r?exports.ChipType.DEFAULT:kl(t)?exports.ChipType.WARNING:exports.ChipType.ERROR,avatarColor:"",userGroupName:null==r?void 0:r.userGroupName},l(o),F("")},value:s.map((function(e){return e.email})),disabled:u||Y,positioning:{gutter:0},loopFocus:!0,inputValue:V,onInputValueChange:function(e){F(e.inputValue)},selectionBehavior:"preserve",inputBehavior:"autohighlight",highlightedValue:X?""+V:void 0}),$=function(e){return-1===(null!=_?_:[]).indexOf(null!=e?e:"")};return a.createElement(d.Field.Root,{className:bc.fieldRoot,invalid:U||J||Y},a.createElement(d.Combobox.RootProvider,{value:Q},a.createElement(d.Combobox.Control,{"data-testid":g,className:r(bc.userSelectInput,D,K,(o={},o[bc.useMaxWidth]=y,o[bc.userSelectInputError]=U||J||Y,o))},a.createElement("div",{className:bc.selectedUsers},s.map((function(e){var t;return a.createElement(Fl,{key:null!=(t=e.uniqueUserId)?t:e.email,text:e.displayText,onDismissBtnClick:$(e.id)?function(){i(e)}:void 0,type:e.chipType,iconName:e.chipType==exports.ChipType.WARNING?"person-warning-icon":e.chipType==exports.ChipType.ERROR?"error-circle":void 0,iconHeight:e.chipType===exports.ChipType.ERROR?20:void 0,iconWidth:e.chipType===exports.ChipType.ERROR?20:void 0,leftElement:a.createElement(Vl,{initials:El(e.displayText||e.name||e.email),avatarSize:"x-small",fontSize:"x-small",type:"monogram"})})})),a.createElement("div",{className:bc.label},a.createElement(d.Combobox.Input,{"aria-label":h,"aria-describedby":f,"aria-labelledby":b,onFocus:function(){return q(!1)},onBlur:function(){M&&E&&0===s.length&&q(!0)},autoComplete:"off",className:bc.input,placeholder:G?"":v||P("search-dropdown-placeholder")})))),a.createElement(d.Combobox.Positioner,null,a.createElement(d.Combobox.Content,{className:bc.dropdown},Z.map((function(e){return a.createElement(d.Combobox.Item,{key:e.uniqueUserId,item:e.email,className:bc.dropdownItem},a.createElement(d.Combobox.ItemText,null,a.createElement(Qi,{name:T?e.name.replace(" ","")?e.name:e.email:"",email:z?e.email:"",avatarColor:e.avatarColor})),a.createElement(d.Combobox.ItemIndicator,null))})),X&&a.createElement(d.Combobox.Item,{item:V,className:bc.dropdownItem,id:V},a.createElement(d.Combobox.ItemText,null,a.createElement(Qi,{email:Q.inputValue.toLowerCase(),isNewUser:!0}))),!X&&0===Z.length&&n.length>0&&a.createElement("div",{id:"no-results",className:bc.noResults},P("no-results-found"))))),a.createElement(d.Field.ErrorText,null,a.createElement("div",{className:bc.errorContainer},a.createElement("div",{className:bc.errorTextContainer},a.createElement(nl,{iconName:"error-circle",size:20}),a.createElement("p",{className:bc.errorText},(J||U)&&P("required-field"),Y&&H("max-selected-users-text",{maxAssignees:B}))))))},exports.SectionBanner=function(e){var t,o=e.text,n=e.dataTestId,l=void 0===n?"uikit-sectionBanner":n,i=e.role,c=e.ariaLabel,d=void 0===c?"Section Banner":c,s=e.ariaDescribedBy,_=e.ariaLabelledBy,m=e.tabIndex;return a.createElement("div",{className:r("sectionBanner-module__sectionBanner___MkOWU",(t={},t["sectionBanner-module__sectionBanner--highlighted___RAuTJ"]=e.isHighlighted,t)),"data-testid":l,role:i,"aria-label":d,"aria-describedby":s,"aria-labelledby":_,tabIndex:m},o)},exports.SelectionCard=function(e){var t,o,n=e.title,l=e.subtitle,i=e.theme,c=void 0===i?exports.BackgroundColor.WHITE:i,d=e.leftIconConfig,s=e.rightIconConfig,_=e.dataTestId,m=void 0===_?"uikit-selectionCard":_,u=e.role,p=e.ariaLabel,g=void 0===p?"":p,h=e.ariaDescribedBy,f=e.ariaLabelledBy,b=e.tabIndex,v=e.disabled,w=void 0!==v&&v,y=d&&d.name&&d,x=s&&s.name&&s;return a.createElement("button",{onClick:e.onCardClick,className:r(vc.selectionCard,vc["selectionCard--"+c],e.cardStyles,(t={},t[vc.selectionCardDisabled]=w,t)),"data-testid":m,role:u,"aria-label":g,"aria-describedby":h,"aria-labelledby":f,tabIndex:b,disabled:w},y&&(null==d?void 0:d.name)&&(null==d?void 0:d.altText)&&a.createElement("div",{className:vc.leftIconContainer},a.createElement(nl,{iconName:d.name,color:w?"#666666":"#333333"})),a.createElement("div",{className:vc.textContainer},a.createElement("div",{className:vc.title},n),l&&a.createElement("div",{className:vc.subtitle},l)),x&&(null==s?void 0:s.name)&&(null==s?void 0:s.altText)&&a.createElement("div",{className:r(vc.rightIconContainer,(o={},o[vc[""+s.colorOverride]]=s.colorOverride,o))},a.createElement(nl,{iconName:s.name})))},exports.Selector=$l,exports.Spinner=dl,exports.Splitter=function(e){var t=e.leftPanel,o=e.rightPanel,n=e.defaultSizes,l=e.maxLimits,i=void 0===l?[80,100]:l,c=e.className,s=e.dataTestId,_=void 0===s?"uikit-splitter":s,m=e.panelOverflow,u=void 0===m?"auto":m,p=a.useState(void 0===n?[30,70]:n),g=p[0],h=p[1];return a.createElement(d.Splitter.Root,{className:r(zc.splitter,c),"data-testid":_,size:[{id:"left-panel",size:g[0]},{id:"right-panel",size:g[1]}],onSizeChange:function(e){var t=e.size.map((function(e){return e.size})).filter((function(e){return void 0!==e}));h(t)},onSizeChangeEnd:function(e){var t=e.size.map((function(e){return e.size})).filter((function(e){return void 0!==e})),a=t[0],r=t[1];a>i[0]&&(r=100-(a=i[0])),r>i[1]&&(a=100-(r=i[1])),h([a,r])}},a.createElement(d.Splitter.Panel,{className:r(zc.panel,"visible"===u&&zc.noOverflow),id:"left-panel"},t),a.createElement(d.Splitter.ResizeTrigger,{className:zc.resizeTrigger,id:"left-panel:right-panel"}),a.createElement(d.Splitter.Panel,{className:zc.panel,id:"right-panel"},o))},exports.StatusBadge=function(e){var o,n,l,i=e.type,d=e.badgeType,s=e.items,_=e.onClick,m=e.dataTestId,u=void 0===m?"uikit-statusBadge":m,p=e.role,g=e.ariaDescribedBy,h=e.ariaLabelledBy,f=e.tabIndex,b=e.isTooltip,v=e.ariaLabel,w=rt(e,jc),y=t.useState(!1),x=y[0],E=y[1],C=c.useTranslation("enums/statusType").t,k=d==exports.StatusBadgeType.PROJECT,T=d===exports.StatusBadgeType.ACTION_ITEM,I=_||b?"button":"span";try{l=k?Sc(i):Nc(i)}catch(e){console.error(e),l=k?exports.ProjectHealthStatusType.ONTRACK:exports.ActionItemStatusType.TODO}return a.createElement(I,Object.assign({className:r("statusBadge-module__statusBadge___GhPoU",(o={},o["statusBadge-module__clickable___NtlLj"]=b||_,o)),onClick:function(){E(!x),_&&_()},"data-testid":u,role:p,"aria-describedby":g,"aria-labelledby":h,tabIndex:f,disabled:!_},w,{"aria-label":v}),a.createElement("span",{className:r("statusBadge-module__statusIconContainer___-nt0J",(n={},n["statusBadge-module__statusIconContainerWithPulse___J-TTm"]=k,n)),style:{backgroundColor:Dc[l]}}),a.createElement("span",{className:r("statusBadge-module__statusIcon___odKXG",T&&"statusBadge-module__statusIconLarge___OUxGn")},a.createElement(nl,{iconName:Oc[l],altText:"",color:Ac[l],size:T?16:10})),a.createElement("span",{className:"statusBadge-module__statusBadgeText___MwNbg"},C(l)),s&&s.length>0&&a.createElement(nl,{iconName:x?"chevron-up":"chevron-down",altText:""}))},exports.StatusBadgeDropdown=function(e){var o,n,l=e.type,i=e.badgeType,s=e.onClick,_=e.dataTestId,m=void 0===_?"uikit-statusBadge":_,u=e.ariaLabel,p=e.items,g=void 0===p?[]:p,h=t.useState(!1),f=h[0],b=h[1],v=t.useState(l),w=v[0],y=v[1],x=c.useTranslation("enums/statusType").t,E=i==exports.StatusBadgeType.PROJECT,C=E?Sc:Nc;try{var k=w.toLocaleUpperCase().replace(/\s/g,"");n=C(k)}catch(e){console.error(e),n=E?exports.ProjectHealthStatusType.ONTRACK:exports.ActionItemStatusType.TODO}var T=function(){s&&s()};return a.createElement("div",{className:"statusBadge-module__actionItemStatusBadgeWrapper___9DFEm"},a.createElement(d.Menu.Root,{id:"actionItemBadge",closeOnSelect:!0,onOpenChange:function(e){return b(e.open)}},a.createElement(d.Menu.Trigger,{asChild:!0},a.createElement("button",{className:r("statusBadge-module__statusBadge___GhPoU","statusBadge-module__clickable___NtlLj"),onClick:T,"data-testid":m,"aria-label":u},a.createElement("span",{className:r("statusBadge-module__statusIconContainer___-nt0J",(o={},o["statusBadge-module__statusIconContainerWithPulse___J-TTm"]=E,o)),style:{backgroundColor:Dc[n]}}),a.createElement("span",{className:"statusBadge-module__statusIcon___odKXG"},a.createElement(nl,{iconName:Oc[n],altText:Oc[n],color:Ac[n],size:10})),a.createElement("span",{className:"statusBadge-module__statusBadgeText___MwNbg"},x(n)),a.createElement(nl,{iconName:f?"chevron-up":"chevron-down"}))),a.createElement(d.Menu.Positioner,null,a.createElement(d.Menu.Content,null,g.map((function(e,t){return a.createElement(d.Menu.Item,{asChild:!0,key:"status-badge-dropdown-"+t,value:e.value},a.createElement("div",{className:"statusBadge-module__itemContainer___UF5lo",onClick:function(){var t;T(),y(null!=(t=e.label)?t:""),e.criticalHandler&&e.criticalHandler()},onTouchStart:function(){var t;T(),y(null!=(t=e.label)?t:""),e.criticalHandler&&e.criticalHandler()}},e.label&&a.createElement("div",{className:"statusBadge-module__forIcon___dZSzw"},a.createElement(nl,{iconName:Oc[C(e.value)],altText:"",color:Ac[C(e.value)],size:14})),a.createElement("div",{className:"statusBadge-module__itemTextContainer___ZRjXa"},a.createElement("div",{className:"statusBadge-module__itemLabel___R-RHf"},e.label),e.text&&a.createElement("div",{className:"statusBadge-module__itemText___vErL5"},e.text))))}))))))},exports.StatusChip=function(e){var r=e.type,o=e.onClick,n=e.dataTestId,l=void 0===n?"uikit-statusChip":n,i=e.role,c=e.ariaLabel,d=void 0===c?"Status Chip":c,s=e.ariaDescribedBy,_=e.ariaLabelledBy,m=e.tabIndex,u=t.useState(!1),p=u[0],g=u[1];return a.createElement("button",{className:"statusChip-module__statusChip___zn5qj",onClick:function(){g(!p),o&&o()},"data-testid":l,role:i,"aria-label":d,"aria-describedby":s,"aria-labelledby":_,tabIndex:m},a.createElement(nl,{iconName:Oc[r],altText:Oc[r],color:Ac[r]}),a.createElement("span",{className:"statusChip-module__statusChipText___A4pxm"},Bc[r]))},exports.StatusTypeToBadgeIconColorMap=Dc,exports.StatusTypeToCapitalizedTextMap=Lc,exports.StatusTypeToIconColorMap=Ac,exports.StatusTypeToIconMap=Oc,exports.StatusTypeToTextMap=Bc,exports.TEMPLATED_FOLDER="Deliverables",exports.TEMPLATED_SUBFOLDERS=["Draft Deliverables","Ready to be Signed","Final Deliverables"],exports.Tab=Pc,exports.Table=function(e){var o,n=e.columns,l=e.data,i=e.placeHolderItems,d=void 0===i?[]:i,s=e.emptyMessage,_=e.emptySubMessage,m=e.emptyMessageIcon,u=void 0===m?"delete-off-icon":m,p=e.dataTestId,g=void 0===p?"uikit-table":p,h=e.role,f=e.ariaLabel,b=e.ariaDescribedBy,v=e.ariaLabelledBy,w=e.tabIndex,y=e.trailingComponent,x=e.className,E=e.onClickRow,C=e.isLoading,k=e.caption,T=e.shouldRenderDocumentsSkeleton,I=void 0!==T&&T,z=e.dataKeyProperty,N=e.highlightImportantRow,S=c.useTranslation("documents").t,B=c.useTranslation("global").t,L=Array.from({length:5}),O=t.useState(!1),A=O[0],D=O[1];t.useEffect((function(){if(!C){var e=setTimeout((function(){D(!1)}),100);return function(){return clearTimeout(e)}}D(!0)}),[C]),s||(s=S("empty-folder"));var j=function(e){return e&&Rc[e]||""};return a.createElement("div",{className:r(Hc.tableContainer,(o={},o[Hc.containerWithScroll]=l.length>0,o[Hc.containerNoScroll]=0===l.length,o[Hc.fullTableHeight]=l.length>5,o),x),"data-testid":g,role:h,tabIndex:w},a.createElement("table",{className:Hc.table,"aria-label":f,"aria-describedby":b,"aria-labelledby":v},k&&a.createElement("caption",{className:"sr-only"},k),a.createElement("thead",null,a.createElement("tr",{className:Hc.tableRow},n.map((function(e){return a.createElement("th",{key:e.key,className:r(Hc.tableHeader,j(e.headerClass))},a.createElement("div",{className:Hc.tableHeaderCell},e.header,e.showFilterIcon&&a.createElement("span",{className:Hc.icon},a.createElement(nl,{iconName:"chevron-up-down",altText:"Sort"}))))})))),a.createElement("tbody",null,null==d?void 0:d.map((function(e,t){return a.createElement("tr",{key:"placeholder-"+t,className:r(Hc.placeholderItemRow)},a.createElement("td",null,a.createElement("div",{className:r(Hc.tableCell,Hc.placeholderSelectCell)},a.createElement(ri,{id:"select-document-placeholder-"+t,value:!1,onChange:function(){}}))),a.createElement("td",{className:Hc.placeholderItem},a.createElement("div",{className:Hc.placeholderItemCell},a.createElement("div",{className:Hc.iconCell},a.createElement(dl,{isLoading:!0,type:exports.SpinnerType.TABLE})),a.createElement("div",{className:Hc.placeholderItemText},e,a.createElement("span",{className:Hc.placeholderItemLoadingText},B("uploading"),"...")))))})),A&&I&&L.map((function(e,t){return a.createElement("tr",{key:"skeleton-"+t,className:r(Hc.skeletonItemRow)},a.createElement("td",null,a.createElement("div",{className:r(Hc.tableCell,Hc.skeletonSelectCell)},a.createElement(ri,{id:"skeleton-checkbox-"+t,value:!1,onChange:function(){}}))),a.createElement("td",{className:Hc.skeletonItem},a.createElement("div",{className:Hc.skeletonItemCell})),a.createElement("td",{className:Hc.skeletonItem},a.createElement("div",{className:Hc.skeletonItemCell})),a.createElement("td",{className:Hc.skeletonItem},a.createElement("div",{className:Hc.skeletonItemCell})))})),!A&&l.map((function(e,t){var o;return a.createElement("tr",{tabIndex:E?0:void 0,key:"data-"+(z&&e[z]?e[z]:t),className:r(Hc.tableRow,(o={},o[Hc.rowClick]=E,o[Hc.highlightImportantRow]=N&&N(e),o)),onClick:E?function(){return E(e)}:void 0,onKeyUp:E?function(t){return function(e,t){!E||"Enter"!==e.key&&" "!==e.key&&"Space"!==e.code||(e.preventDefault(),E(t))}(t,e)}:void 0},n.map((function(o){return a.createElement("td",{key:o.key,className:r(Hc.tableCell,j(o.tableCellClass))},o.render?o.render(e,t):e[o.key])})))})))),y,0===l.length&&!C&&0===d.length&&a.createElement("div",{className:Hc.emptyMessageContainer},a.createElement(nl,{iconName:u,altText:"",size:20}),a.createElement("div",{className:Hc.emptyMessageText},a.createElement("div",{className:Hc.emptyMessageTitle},s),_&&a.createElement("div",{className:Hc.emptyMessageSubMessage},_))))},exports.Tabs=function(e){var r=e.id,o=e.tabs,n=e.dataTestId,l=void 0===n?"uikit-tabs":n,i=e.ariaLabel,c=s.useLocation().pathname,d=t.useRef(null),_=t.useRef([]),m=t.useRef(null);return t.useEffect((function(){if(d.current){var e=d.current.querySelectorAll('[role="tab"]');_.current=Array.from(e)}}),[o]),a.createElement("div",{className:"tabs-module__tabsContainerWrapper___jOEVs",role:"tablist",id:"tab-list-"+r,"data-testid":l,"aria-label":i,onKeyDown:function(e){var t,a=_.current,r=a.length;if(0!==r){var o=null!=(t=m.current)?t:-1;-1===o&&(o=a.findIndex((function(e){return"true"===e.getAttribute("aria-selected")})));var n=o;switch(e.key){case"ArrowRight":e.preventDefault(),n=o+1;break;case"ArrowLeft":e.preventDefault(),n=o-1;break;case"Home":e.preventDefault(),n=0;break;case"End":e.preventDefault(),n=r-1;break;default:return}a[n=n<0?r-1:n>r-1?0:n].focus(),m.current=n}},onBlur:function(){m.current=null},ref:d},o.map((function(e,t){return a.createElement(Pc,Object.assign({key:"tab-"+t},e,{isActive:e.isActive||c===e.link,"aria-controls":e.id+"-panel",id:"nav-tab-"+e.id}))})))},exports.TaskType=Ql,exports.TaskTypeDropdown=function(e){var r=e.id,o=e.items,n=e.dataTestId,l=void 0===n?"uikit-taskTypeDropdown":n,i=e.role,c=e.ariaLabel,s=void 0===c?"Task Type Dropdown":c,_=e.ariaDescribedBy,m=e.ariaLabelledBy,u=e.tabIndex,p=e.onChange,g=t.useState(!1),h=g[0],f=g[1],b=t.useState(o[0]),v=b[0],w=b[1],y=function(){f(!h)};return a.createElement("div",{className:Vc.parent},a.createElement(d.Menu.Root,{open:h,id:r,className:Vc.root,closeOnSelect:!0,onEscapeKeyDown:y,onPointerDownOutside:y,onSelect:function(e){var t;t=o[parseInt(e.value)],p(t),w(t),f(!1)},"data-testid":l,role:i,"aria-label":s,"aria-describedby":_,"aria-labelledby":m,tabIndex:u},a.createElement(d.Menu.Trigger,{onClick:y,onKeyDown:function(e){"Enter"!==e.code&&"Space"!==e.code||y()}},a.createElement(Ql,{iconName:v.iconName,backgroundColor:v.color}),a.createElement(nl,{iconName:h?"chevron-up":"chevron-down",altText:h?"chevron-up":"chevron-down"})),a.createElement(d.Menu.Positioner,null,a.createElement(d.Menu.Content,null,o.map((function(e,t){return a.createElement(d.Menu.Item,{key:t.toString(),value:t.toString(),tabIndex:t},a.createElement(Ql,{iconName:e.iconName,backgroundColor:e.color}),e.label)}))))))},exports.TextArea=function(e){var r=e.label,o=void 0===r?"Text Area":r,n=e.placeholder,l=void 0===n?"What needs to be done?":n,i=e.maxLength,c=void 0===i?200:i,d=e.displayMaxLength,s=void 0!==d&&d,_=e.value,m=void 0===_?"":_,u=e.onChange,p=e.validationState,g=e.errorMessage,h=e.hideLabel,f=void 0===h||h,b=e.dataTestId,v=void 0===b?"uikit-textArea":b,w=e.role,y=e.ariaLabel,x=void 0===y?"Text Area":y,E=e.ariaDescribedBy,C=e.ariaLabelledBy,k=e.tabIndex,T=e.required,I=t.useState(m),z=I[0],N=I[1],S=t.useRef(null),B=gl().focusClass;t.useEffect((function(){N(m)}),[m]),t.useEffect((function(){S.current&&(S.current.style.height=S.current.scrollHeight+"px")}),[S,S.current]);var L="textarea-module__textArea___9YNgE "+B+" "+("error"===p?"textarea-module__error___PKCB3":"success"===p?"textarea-module__success___BFQ5o":""),O=t.useRef();return a.createElement("div",{className:"textarea-module__container___IsgwQ","data-testid":v,role:w,"aria-label":x,"aria-describedby":E,"aria-labelledby":C,tabIndex:k},(!f||s)&&a.createElement("div",{className:"textarea-module__info___aGE-M"},a.createElement("label",{htmlFor:"textarea",className:f?"textarea-module__hiddenLabel___0KxD1":"textarea-module__label___FFa15"},o),T&&a.createElement("span",{className:"textarea-module__requiredStar___vibR0"},"*"),s&&a.createElement("span",{className:"textarea-module__maxLength___d-THn"},z.length," / ",c)),a.createElement("textarea",{required:T,id:"textarea",ref:S,className:L,placeholder:l,value:z,onChange:function(e){var t=e.currentTarget;clearTimeout(O.current),O.current=setTimeout((function(){t.style.height="auto",t.style.height=t.scrollHeight+"px"}),0),function(e){var t=e.target.value;t.length<=c&&(N(t),u&&u(t))}(e)},rows:1,"aria-label":o}),"error"===p&&g&&a.createElement("div",{className:"textarea-module__info___aGE-M"},a.createElement(nl,{iconName:"error-message-icon",size:20}),a.createElement("span",{id:"error-message",className:"textarea-module__errorMessage___hkv1f"},g)))},exports.TextButton=Li,exports.TextCarousel=function(e){var r=e.items,o=e.interval,n=void 0===o?2e3:o,l=e.defaultIndex,i=void 0===l?0:l,s=e.dataTestId,_=void 0===s?"uikit-textCarousel":s,m=e.role,u=e.ariaLabel,p=void 0===u?"Text Carousel":u,g=e.ariaDescribedBy,h=e.ariaLabelledBy,f=e.tabIndex,b=c.useTranslation("onboarding"),v=b.t,w=b.i18n,y=t.useState(i),x=y[0],E=y[1];return t.useEffect((function(){setTimeout((function(){E((x+1)%r.length)}),n)}),[x,n,r.length]),a.createElement("div",{className:"textCarousel-module__textCarousel___ZlAlw","data-testid":_,role:m,"aria-label":p,"aria-describedby":g,"aria-labelledby":h,tabIndex:f},a.createElement("div",{className:"textCarousel-module__didYouKnow___xX493"},a.createElement(nl,{iconName:"did-you-know",altText:"did you know",size:14}),v("did-you-know")),a.createElement(d.Carousel.Root,{align:"center",loop:!0,slidesPerView:1,orientation:"horizontal",index:x},a.createElement(d.Carousel.Control,null),a.createElement(d.Carousel.Viewport,null,a.createElement(d.Carousel.ItemGroup,{className:"textCarousel-module__carouselItems___hwglz"},r.map((function(e,t){return a.createElement(d.Carousel.Item,{className:"textCarousel-module__carouselItem___l7Mt4",key:t,index:t},a.createElement("p",{className:"textCarousel-module__itemText___kiP2i"},e.translationKey&&w.exists("onboarding:consideration-carousel-item-text-"+e.translationKey)?v("consideration-carousel-item-text-"+e.translationKey):e.text),a.createElement("span",{className:"textCarousel-module__itemSource___1sllB"},e.translationKey&&w.exists("onboarding:consideration-carousel-item-source-"+e.translationKey)?v("consideration-carousel-item-source-"+e.translationKey):e.text))}))))),a.createElement("div",{className:"textCarousel-module__indicators___8zX73"},r.map((function(e,t){return a.createElement("div",{"data-state":x===t?"active":"inactive",className:"textCarousel-module__indicator___Pxzb1"})}))))},exports.Toast=function(e){var r,o,n=e.title,l=e.message,i=e.type,c=e.isVisible,d=e.duration,s=void 0===d?5e3:d,_=e.persist,m=void 0!==_&&_,u=e.withCTA,p=e.ctaText,g=e.onCTAClick,h=e.dataTestId,f=void 0===h?"uikit-toast":h,b=e.role,v=e.ariaLabel,w=e.ariaDescribedBy,y=e.ariaLabelledBy,x=e.tabIndex,E=e.onClose,C=e.errorMessageWithLineBreaks,k=void 0!==C&&C,T=e.replaceLineBreaksWithBulletPoints,I=void 0!==T&&T,z=t.useState(!1),N=z[0],S=z[1],B=t.useCallback((function(){S(!1),setTimeout((function(){E()}),300)}),[E]);return t.useEffect((function(){if(c){if(setTimeout((function(){S(!0)}),100),!m){var e=setTimeout((function(){B()}),s);return function(){return clearTimeout(e)}}}else S(!1),E();return function(){}}),[c,s,B,m]),c||N?a.createElement("div",{className:Uc.toast+" "+(N?Uc.show:Uc.hide)+" "+Uc[i],"data-testid":f,role:b,"aria-label":v,"aria-describedby":w,"aria-labelledby":y,tabIndex:x},a.createElement(nl,{iconName:{success:"success-checkmark",info:"info-icon",error:"error-icon",warning:"warning-icon"}[i],altText:i+" icon",size:18}),a.createElement("div",{className:Uc.toastContainer},a.createElement("div",{className:Uc.toastText},n?a.createElement("p",{className:Uc.toastTitle,"aria-label":n},n):null,l?a.createElement("p",{className:Uc.toastMessage,"aria-label":l},a.createElement("span",null,k&&I?(r=l.split("\n").map((function(e){return e.trim()})).filter(Boolean),o={fontSize:"14px",paddingLeft:"10px"},1===r.length?a.createElement("div",{style:o},a.createElement("span",{style:{position:"relative",left:"-5px"}},"• ",r[0])):a.createElement("ul",{style:{margin:0,padding:0,listStyle:"none"}},r.map((function(e,t){return a.createElement("li",{key:t,style:o},a.createElement("span",{style:{position:"relative",left:"-5px"}},"• ",e))})))):l),u&&a.createElement("span",{className:Uc.toastCTA,onClick:g,"aria-label":p},p)):null),a.createElement("div",{onClick:B},a.createElement(nl,{iconName:"close-icon",altText:"close toast icon",ariaLabel:"Close toast",size:11})))):a.createElement(a.Fragment,null)},exports.Toaster=function(e){var t=e.dataTestId,o={success:"success-checkmark",info:"info-icon",error:"error-icon",warning:"warning-icon"};return a.createElement(d.Toaster,{toaster:bi,"data-testid":void 0===t?"uikit-toaster":t},(function(e){var t,n=vi.get(e.id),l=e.meta||{},i=l.errorMessageWithLineBreaks,c=l.replaceLineBreaksWithBulletPoints;return a.createElement(d.Toast.Root,{key:e.id,className:r(hi.toast,hi[e.type||"info"]),tabIndex:0,role:"group","aria-live":"assertive",ref:function(e){e&&setTimeout((function(){e.focus()}),100)}},a.createElement("div",{className:hi.toastIcon},a.createElement(nl,{iconName:o[e.type||"info"],altText:e.type+" icon",size:18})),a.createElement("div",{className:hi.toastContent},e.title&&a.createElement(d.Toast.Title,{className:hi.toastTitle,tabIndex:0,role:"heading"},e.title),e.description&&a.createElement(d.Toast.Description,{className:hi.toastDescription,tabIndex:0,role:"text"},a.createElement("span",null,i&&c?1===(t=String(e.description).split("\n").map((function(e){return e.trim()})).filter(Boolean)).length?a.createElement("div",{style:{fontSize:"14px",paddingLeft:"10px"}},a.createElement("span",{style:{position:"relative",left:"-5px"}},"• ",t[0])):a.createElement("ul",{className:hi.bulletPoints},t.map((function(e,t){return a.createElement("li",{key:t},e)}))):i?String(e.description).split("\n").map((function(t,r){return a.createElement(a.Fragment,{key:r},t,r<String(e.description).split("\n").length-1&&a.createElement("br",null))})):e.description),n&&a.createElement("button",{className:hi.toastCTA,onClick:function(e){e.stopPropagation(),e.preventDefault(),n.onClick&&n.onClick()}},n.label))),a.createElement(d.Toast.CloseTrigger,{className:hi.closeButton},a.createElement(nl,{iconName:"close-icon",altText:"Close",size:12})))}))},exports.Toggle=Di,exports.ToggleListItem=function(e){var t,o=e.title,n=e.subtitle,l=e.withBorder,i=e.toggleConfig,c=e.tooltipConfig,d=e.dataTestId,s=e.ariaLabel;return a.createElement("div",{"data-testid":void 0===d?"uikit-toggleListItem":d,role:e.role,"aria-label":void 0===s?"Toggle List Item":s,"aria-describedby":e.ariaDescribedBy,"aria-labelledby":e.ariaLabelledBy,tabIndex:e.tabIndex,className:r("toggleListItem-module__toggleListItem___HVP-i",(t={},t["toggleListItem-module__toggleListItemWithBorder___wSssQ"]=void 0===l||l,t))},a.createElement("div",{className:"toggleListItem-module__toggleListInfo___wKXno"},a.createElement("div",{className:"toggleListItem-module__titleContainer___0Mrv7"},a.createElement("div",{className:"toggleListItem-module__title___I9cUl"},o),c&&a.createElement(_i,Object.assign({},c),a.createElement(nl,{iconName:"warning-icon",altText:"Toggle list item alt text"}))),a.createElement("div",{className:"toggleListItem-module__subtitle___wjFnm"},n)),a.createElement(Di,Object.assign({},i)))},exports.UIKitContext=function(e){return a.createElement(a.Fragment,null,e.children)},exports.UserSelect=function(e){var o=e.id,n=e.users,l=e.selected,i=e.onChange,s=e.placeholder,_=e.label,m=e.hideLabel,u=e.ariaDescribedBy,p=e.ariaLabel,g=t.useState(null),h=g[0],f=g[1],b=t.useState(null),v=b[0],w=b[1],y=t.useState(!1),x=y[0],E=y[1],C=c.useTranslation("dropdown").t,k=gl().focusClass,T=function(e,t){var a=e;return t&&(a=function(e,t){return t=t.toLowerCase(),e.filter((function(e){var a=e.displayName.toLowerCase(),r=e.email.toLowerCase();return!!a.includes(t)||!!r.includes(t)}))}(a,t)),a.slice(0,100)}(n,h),I=function(e,t){if(!t)return null;var a=e.at(0);return a?a.id:null}(T,h),z=0===T.length;return a.createElement("div",{className:"userSelect-module__container___MJlZe"},a.createElement(d.Combobox.Root,{id:o,items:T,placeholder:s,openOnClick:!0,open:x,onOpenChange:function(e){E(e.open)},itemToValue:function(e){return e.id},itemToString:function(e){return e.displayName},selectionBehavior:"preserve",value:l?[l.id]:[],onValueChange:function(e){var t,a=null!=(t=e.items.at(0))?t:null;i(a),f(null)},inputValue:null!=h?h:l?l.displayName:"",onInputValueChange:function(e){i(null),f(e.inputValue)},highlightedValue:null!=v?v:I,onHighlightChange:function(e){w(e.highlightedValue)},onBlur:function(){f(null)},positioning:{fitViewport:!0,strategy:"fixed"},loopFocus:!0},a.createElement(d.Combobox.Label,{className:r(m&&"userSelect-module__visuallyHidden___69iLr")},_),a.createElement(d.Combobox.Control,null,l&&a.createElement("span",{className:"userSelect-module__selectedAvatar___xvQiJ"},a.createElement(Vl,{initials:El(l.displayName),avatarSize:"x-small",fontSize:"x-small",type:"monogram"})),a.createElement(d.Combobox.Input,{onFocus:function(){f("")},onKeyDown:function(e){"ArrowDown"!==e.key||x||(e.preventDefault(),E(!0))},className:k,"aria-expanded":x,"aria-haspopup":"listbox",role:"combobox","aria-autocomplete":"list","aria-describedby":u,"aria-label":p||_})),a.createElement(d.Portal,null,a.createElement(d.Combobox.Positioner,{className:"userSelect-module__positioner___mBs40"},a.createElement(d.Combobox.Content,{className:"userSelect-module__content___GgJJ2"},T.map((function(e){return a.createElement(d.Combobox.Item,{key:e.id,item:e},a.createElement(Vl,{initials:El(e.displayName),avatarSize:"x-small",fontSize:"x-small",type:"monogram"}),a.createElement(d.Combobox.ItemText,null,e.displayName))})),z&&a.createElement("div",{className:"userSelect-module__noResults___X1gAU"},C("no-results")))))))},exports.WYSIWYGEditor=Ji,exports.WarningItemChip=function(e){var t=e.iconName;return a.createElement(Fl,{text:e.text,onDismissBtnClick:e.onDismissBtnClick,type:exports.ChipType.WARNING,iconName:void 0===t?"person-warning-icon":t})},exports.downloadFile=xl,exports.getDateLabel=fl,exports.getDateRangeLabel=vl,exports.getDateTimeLabel=bl,exports.getDocumentsDateLabel=function(e,t){void 0===t&&(t="en-CA");var a=n.parseISO(e);return((new Date).getTime()-a.getTime())/6e4<1?t.includes("fr")?"À l'instant":"Just now":bl(e,t,!1,!0)},exports.getDueDateLabel=function(e,t){if(void 0===t&&(t="en-CA"),!e)return"";var a=n.parseISO(e),r=t.includes("fr")?l.frCA:l.enCA;if(n.isToday(a))return r===l.frCA?"Aujourd'hui":"Today";if(hl(e)){var o=new Date,i=new Date(o.getFullYear(),o.getMonth(),o.getDate(),0,0,0),c=new Date(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0),d=Math.floor((i.getTime()-c.getTime())/864e5),s=(r===l.frCA?"jour":"day")+(d>1?"s":"");return r===l.frCA?"il y a "+d+" "+s:d+" "+s+" ago"}return fl(e,t)},exports.getExtension=Il,exports.getFirstAndLast=function(e){if(!e)return[];var t=e.trim().split(" ");return t.length<2?t:[t[0],t.slice(1).join(" ")]},exports.getFocusableElements=Al,exports.getFolderUrl=xi,exports.getInitials=El,exports.getInitialsWithFirstAndLast=function(e,t){return((null==e?void 0:e.trim().charAt(0).toUpperCase())||"")+((null==t?void 0:t.trim().charAt(0).toUpperCase())||"")},exports.getInvalidEmails=function(e){return e.filter((function(e){return!kl(e.email)})).map((function(e){return e.email}))},exports.getStartEndDateRange=wl,exports.getTimezoneConvertedDate=function(e){return e instanceof Date?e:"Z"===e.slice(-1)?new Date(e):new Date(e+"Z")},exports.isDueDateInDateRange=function(e,t,a){return a>=e&&a<t},exports.isPastDue=hl,exports.isReadonlyPath=yl,exports.isValidEmail=kl,exports.normalizeActionItemStatusType=Nc,exports.normalizeProjectHealthStatusType=Sc,exports.removeExtension=Tl,exports.toaster=bi,exports.useClickOutside=function(e){var a=e.onClickOutside,r=e.isVisible,o=e.ignoreSelector,n=t.useRef(null);return t.useEffect((function(){if(r){var e=function(e){var t=e.target;[].concat(null!=o?o:[]).some((function(e){return e&&t.closest(e)}))||r&&n.current&&!n.current.contains(t)&&a()};return document.addEventListener("mouseup",e),document.addEventListener("touchend",e),function(){document.removeEventListener("mouseup",e),document.removeEventListener("touchend",e)}}}),[r,a,o]),n},exports.useEscapeClose=Bl,exports.useFocusManager=ul,exports.useFocusTrap=function(e){var a=e.containerRef,r=e.isActive,o=e.onClose;Bl(r,o),t.useEffect((function(){if(r&&a.current){var e=Al(a);e.length>0&&setTimeout((function(){e[0].focus()}),100)}}),[r,a]),t.useEffect((function(){if(r&&a.current){var e=function(e){if("Tab"===e.key&&a.current){var t=Al(a);if(0!==t.length){var r=t[0],o=t[t.length-1],n=document.activeElement;e.shiftKey?n!==r&&a.current.contains(n)||(e.preventDefault(),o.focus()):n!==o&&a.current.contains(n)||(e.preventDefault(),r.focus())}else e.preventDefault()}};return document.addEventListener("keydown",e),function(){document.removeEventListener("keydown",e)}}}),[r,a,o])},exports.useFocusedBy=gl,exports.usePopoverFocusManager=function(e){var a=e.triggerRef,r=e.popoverRef,o=e.onClose;Bl(!0,o),t.useEffect((function(){function e(e){if("Tab"===e.key){var t=Al(r);if(t&&0!==t.length){var n=document.activeElement;e.shiftKey||n!==t[t.length-1]||o(),e.shiftKey&&n===a.current&&o()}}}return document.addEventListener("keydown",e),function(){return document.removeEventListener("keydown",e)}}),[o])},exports.useThemeColors=Rl,exports.useToast=wi,exports.validateFileForUpload=Sl,exports.validateSubStages=function(e){return e.every((function(e){return e.title&&""!==e.title.trim()&&e.startDate&&e.endDate&&e.status&&""!==e.status}))};
//# sourceMappingURL=uikit.cjs.production.min.js.map
