import styles from "./assigneeOrder.module.css";
import {
  <PERSON><PERSON>,
  TextButton,
  TextButtonSizeEnum,
  DropdownUser,
  UserSelect,
} from "@bcp/uikit";
import { ActionItemUser } from "~/services/action-items/spec";
import { SelectedAssignee } from "../../shared/spec";
import { useTranslation } from "react-i18next";
import { useState, useRef } from "react";

interface Props {
  users: ActionItemUser[];
  value: SelectedAssignee[];
  onChange?: (selectedAssignees: SelectedAssignee[]) => void;
  maxAssignees?: number;
}

export const AssigneeOrder: React.FC<Props> = ({
  users,
  value,
  onChange,
  maxAssignees,
}) => {
  const [adding, setAdding] = useState(false);
  const { t } = useTranslation("actionItems");
  const addRowRef = useRef<HTMLDivElement>(null);

  const rows = adding ? [...value, null] : value;

  const handleAddRow = () => {
    setAdding(true);
    focusAddRow();
  };

  const focusAddRow = () => {
    // Force Ark UI input element into focus
    // Timeout hack addresses a bug where Ark UI Positioner appears
    // in the wrong position if triggered too quickly after render
    const delay = addRowRef.current ? 0 : 200;
    setTimeout(() => {
      const input = addRowRef.current?.querySelector("input");
      input?.focus();
    }, delay);
  };

  const handleAdd = (selected: DropdownUser | null) => {
    // Empty field is not allowed
    if (selected === null) {
      return;
    }

    const updatedAssignees = [...value, { user: selected }];

    onChange?.(updatedAssignees);
    setAdding(false);
  };

  const handleDelete = (index: number) => {
    const updatedAssignees = [...value];
    updatedAssignees.splice(index, 1);
    onChange?.(updatedAssignees);
  };

  const handleChange = (index: number, selected: DropdownUser | null) => {
    // Empty field is not allowed
    if (selected === null) {
      return;
    }

    // If the user is already assigned, we can swap them
    const swapUser = users.find(user => user.bgpId === value[index].user.bgpId);
    const oldIndex = value.findIndex(
      assignee => assignee.user.bgpId === selected.bgpId
    );

    const updatedAssignees = value.map((assignee, i) => {
      if (i === oldIndex && swapUser) {
        return { user: swapUser };
      }

      if (i === index) {
        return { user: selected };
      }

      return { user: assignee.user };
    });

    onChange?.(updatedAssignees);
  };

  const reachedMax = maxAssignees ? rows.length >= maxAssignees : false;

  return (
    <div
      className={styles.container}
      role="group"
      aria-labelledby="signing-order-heading"
    >
      <div id="signing-order-heading" className={styles.srOnly}>
        {t("signing-order-list")}
      </div>
      {rows.map((assignee, i) => {
        const order = i + 1;
        const signeeId = `signee-${i}`;
        const orderDescriptionId = `signee-order-${i}`;

        // Edit an assignee
        if (assignee) {
          return (
            <div className={styles.row} key={i}>
              <span
                className={styles.order}
                id={orderDescriptionId}
                aria-label={t("signing-order-position", { order })}
              >
                {order}
              </span>
              <UserSelect
                id={signeeId}
                label={t("select-a-user")}
                hideLabel
                selected={assignee.user}
                onChange={user => handleChange(i, user)}
                users={users.filter(user => user.bgpId !== assignee.user.bgpId)}
                ariaDescribedBy={orderDescriptionId}
                ariaLabel={t("select-signee-for-position", { order })}
              />
              <button
                className={styles.deleteButton}
                aria-label={t("delete-signee-at-position", { order })}
                onClick={() => handleDelete(i)}
              >
                <Icon iconName="delete-icon" />
              </button>
            </div>
          );
        }

        // Add an assignee
        return (
          <div className={styles.row} ref={addRowRef} key={i}>
            <span
              className={styles.order}
              id={orderDescriptionId}
              aria-label={t("signing-order-position", { order })}
            >
              {order}
            </span>
            <UserSelect
              id={signeeId}
              label={t("select-a-user")}
              hideLabel
              selected={null}
              onChange={handleAdd}
              users={users.filter(
                user =>
                  !value.some(assignee => assignee.user.bgpId === user.bgpId)
              )}
              ariaDescribedBy={orderDescriptionId}
              ariaLabel={t("select-signee-for-position", { order })}
            />
            <button
              className={styles.deleteButton}
              aria-label={t("cancel-add-signee-at-position", { order })}
              onClick={() => setAdding(false)}
            >
              <Icon iconName="delete-icon" />
            </button>
          </div>
        );
      })}

      {reachedMax && (
        <div className={styles.error}>
          <Icon iconName="error-circle" size={20} />
          <span className={styles.errorText}>
            {t("max-selected-users-text", { maxAssignees })}
          </span>
        </div>
      )}

      {!reachedMax && (
        <TextButton
          id="add-action-item-assignee-user"
          label={t("add-a-user")}
          size={TextButtonSizeEnum.medium}
          onClick={handleAddRow}
          iconName="add-icon"
          customIconSize={10}
          className={styles.addButton}
        />
      )}
    </div>
  );
};
