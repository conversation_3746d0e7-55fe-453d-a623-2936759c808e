.container {
  height: calc(
    95% - (16px + 42px + 16px + 38px)
  ); /* subtract title height and top/bottom padding and filters */

  [data-scope="file-upload"][data-part="root"] {
    display: flex;
    align-items: flex-start;
    height: 100%;
    background: var(--color-primary-soft-charcoal);
    border-color: var(--color-primary-soft-charcoal);
  }
}

.containerTrashDocuments {
  height: calc(
    100% - (16px + 24px + 16px + 45px)
  ); /* available height - top tittle padding - title height - margin below header - banner deleted after */

  margin-top: 0.5rem;
  .tableContainer {
    height: calc(100% - 53px); /* subtract filter */
  }
}

.tableRowDownloadSpinnerContainer {
  padding-left: 14px;
  padding-right: 14px;
}

.readOnlyChip {
  margin-left: auto;
}

.currentFolderBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  color: var(--color-primary-inactive-charcoal);
  font-weight: 600;
  font-size: var(--fontsize-body-xsmall);
  background-color: var(--color-bg-light-grey);
}

.tableDropzone {
  position: relative;
  width: 100%;
  border-radius: 8px;

  [data-scope="file-upload"][data-part="dropzone"] {
    display: flex;
    align-items: flex-start;
    padding: 0;
    border: none;
    border: 2px solid transparent;
    pointer-events: none;
    width: 100%;
    height: 100%;
    cursor: default;
  }

  [data-scope="file-upload"][data-part="dropzone"] * {
    pointer-events: auto !important;
  }

  [data-scope="file-upload"][data-part="dropzone"]:hover {
    background-color: var(--color-primary-soft-charcoal);
  }

  [data-scope="file-upload"][data-part="dropzone"][data-dragging] {
    border-color: var(--color-secondary-cobalt);
    border-style: solid;
    border-radius: 8px;
    position: relative;

    table {
      background-color: var(--color-bg-light-cobalt-transparent);
    }
    > div:first-child {
      background-color: var(--color-bg-light-cobalt-transparent);
    }
  }
}

.moveDocumentsLocationChip {
  display: flex;
  gap: 0.5rem;
}

.uploadedByPrefix {
  display: none;
}

.folderCounter {
  color: var(--color-primary-inactive-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  letter-spacing: -0.1px;
  text-align: left;
  display: block;
}

@media (min-width: 1280px) {
  .uploadedByPrefix {
    display: inline;
  }
}
