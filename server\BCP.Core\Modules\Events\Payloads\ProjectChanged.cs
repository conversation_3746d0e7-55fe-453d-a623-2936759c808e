using BCP.Data.Models;

namespace BCP.Core.Events.Payloads;

public class ProjectChanged : IEventPayload
{
    public static EventName EventName => EventName.ProjectChanged;
    public required Guid ClientId { get; set; }
    public required Guid ProjectId { get; set; }
    public IEnumerable<Guid>? AddedUsers { get; set; }
    public IEnumerable<Guid>? ReinvitedUsers { get; set; }
    public ProjectStatus? HealthStatus { get; set; }
}
