using BCP.Core.Common;
using BCP.Core.Email;
using BCP.Core.Events;
using BCP.Core.Job;
using BCP.Data;
using BCP.Data.Models;
using BCP.Data.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EmailTemplates = BCP.Core.Email.Templates;
using NotificationPayloads = BCP.Core.Notifications.Payloads;

namespace BCP.Core.Notifications;

public class NotificationDispatcher : IJobHandler
{
    public static string JobName => "notification_dispatcher";
    public static string Schedule => "*/1 * * * *";
    private readonly ILogger<NotificationDispatcher> _logger;
    private readonly INotificationsConfig _config;
    private readonly IEmailService _emailService;
    private readonly DataContext _context;
    private const int BatchSize = 50;

    private readonly CoreException _cancelException = new CoreException(
        "Notification processing has been cancelled"
    );

    public NotificationDispatcher(
        ILogger<NotificationDispatcher> logger,
        DataContext context,
        INotificationsConfig config,
        IEmailService emailService
    )
    {
        _logger = logger;
        _config = config;
        _context = context;
        _emailService = emailService;
    }

    public async Task Run(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Running Notification Job");
        await HandleQueue(cancellationToken);
        await HandleReminders(cancellationToken);
        _logger.LogInformation("Finishing Job");
    }

    private async Task HandleQueue(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling notification email queue");
        var queue = _context
            .Notifications.Where(x => x.EmailStatus == NotificationEmailStatus.Queued)
            .Include(n => n.User)
            .Include(n => n.Client)
            .Include(n => n.Project)
            .Include(n => n.Actor)
            .OrderBy(x => x.CreatedAt)
            .Take(BatchSize)
            .ToList();

        foreach (var notification in queue)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    throw _cancelException;
                if (notification.NotificationType == NotificationType.PortalInvite)
                {
                    var messageId = await HandlePortalInvite(notification);
                    notification.EmailStatus = string.IsNullOrWhiteSpace(messageId)
                        ? NotificationEmailStatus.Failed
                        : NotificationEmailStatus.EmailQueued;
                    notification.EmailMessageId = messageId;
                }
                else
                {
                    await HandleNotification(notification);
                    notification.EmailStatus = NotificationEmailStatus.Sent;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing email for notification: {notification.Id}");
                notification.EmailStatus = NotificationEmailStatus.Failed;
                _context.Notifications.Update(notification);
            }
            finally
            {
                notification.EmailProcessedAt = DateTime.UtcNow;
                _context.Notifications.Update(notification);
                await _context.SaveChangesAsync();
            }
        }

        var emailQueue = _context
            .Notifications.Where(x => x.EmailStatus == NotificationEmailStatus.EmailQueued)
            .Where(x => !string.IsNullOrWhiteSpace(x.EmailMessageId))
            .Include(n => n.User)
            .Include(n => n.Client)
            .Include(n => n.Project)
            .Include(n => n.Actor)
            .OrderBy(x => x.CreatedAt)
            .Take(BatchSize)
            .ToList();

        var now = DateTime.UtcNow;
        foreach (var notification in emailQueue)
        {
            var status = await _emailService.GetDeliveryStatus(notification.EmailMessageId);
            UpdateDeliveryStatus(now, notification, status);
            notification.UpdatedAt = now;
            _context.Notifications.Update(notification);
            await _context.SaveChangesAsync();
        }
    }

    private static void UpdateDeliveryStatus(
        DateTime now,
        Notification notification,
        MessageStatus status
    )
    {
        if (status == MessageStatus.Delivered)
        {
            notification.EmailStatus = NotificationEmailStatus.Sent;
            return;
        }
        if (status == MessageStatus.NotDelivered)
        {
            notification.EmailStatus = NotificationEmailStatus.Failed;
            return;
        }
        if (status == MessageStatus.Error)
        {
            notification.EmailStatus = NotificationEmailStatus.Failed;
            return;
        }
        if ((now - notification.EmailProcessedAt) > TimeSpan.FromMinutes(3))
        {
            notification.EmailStatus = NotificationEmailStatus.TimedOut;
            return;
        }
    }

    private async Task HandleNotification(Notification notification)
    {
        var notificationType = notification.NotificationType.GetString();
        _logger.LogInformation(
            $"Processing Email Notification: {notification.Id}, User: {notification.UserId}, Type: {notificationType}"
        );
        switch (notification.NotificationType)
        {
            case NotificationType.ProjectInvite:
                await HandleProjectInvite(notification);
                break;
            case NotificationType.ClientAdminInvite:
                await HandleClientAdminInvite(notification);
                break;
            case NotificationType.OnboardingCompleted:
                await HandleOnboardingCompleted(notification);
                break;
            case NotificationType.ActionItemAssigned:
                await HandleActionItemAssigned(notification);
                break;
            case NotificationType.FinalDeliveryAdded:
                await HandleFinalDeliveryAdded(notification);
                break;
            case NotificationType.ActionItemStatusChanged:
                await HandleActionItemStatusChanged(notification);
                break;
            default:
                throw new CoreException(
                    $"Notification: {notification.Id}, of type: {notificationType}, "
                        + "doesn't have a valid Email Handler"
                );
        }
        _logger.LogInformation(
            $"Finished Processing Email Notification: {notification.Id}, User: {notification.UserId}, Type: {notificationType}"
        );
    }

    private async Task<string?> HandlePortalInvite(Notification notification)
    {
        var nPayload = notification.GetPayload<NotificationPayloads.PortalInvite>();
        //the notification holds the redemption url and it needs to be passed to the email template
        var template = new EmailTemplates.PortalInvite
        {
            Client = notification.Client,
            User = notification.User,
            RedemptionUrl = nPayload.RedemptionUrl,
        };
        return await _emailService.SendEmail(template);
    }

    private async Task HandleProjectInvite(Notification notification)
    {
        var template = new EmailTemplates.ProjectInvite
        {
            Client = notification.Client,
            Project = notification.Project,
            User = notification.User,
            Actor = notification.Actor,
        };

        await _emailService.SendEmail(template);
    }

    private async Task HandleClientAdminInvite(Notification notification)
    {
        var template = new EmailTemplates.ClientAdminInvite
        {
            Client = notification.Client,
            User = notification.User,
        };

        await _emailService.SendEmail(template);
    }

    private async Task HandleOnboardingCompleted(Notification notification)
    {
        var template = new EmailTemplates.OnboardingCompleted
        {
            Client = notification.Client,
            Project = notification.Project,
            User = notification.User,
            Actor = notification.Actor!,
        };
        await _emailService.SendEmail(template);
    }

    private async Task HandleFinalDeliveryAdded(Notification notification)
    {
        var payload = notification.GetPayload<NotificationPayloads.FinalDeliveryAdded>();
        var deliverableName = Path.GetFileNameWithoutExtension(payload?.DocumentName) ?? "";
        var directoryPath = Path.GetDirectoryName(payload?.Path)?.Replace('\\', '/') ?? "/";

        var template = new EmailTemplates.FinalDeliveryAdded
        {
            Client = notification.Client,
            Project = notification.Project,
            User = notification.User,
            Actor = notification.Actor!,
            DeliverableName = deliverableName,
            DirectoryPath = directoryPath,
        };
        await _emailService.SendEmail(template);
    }

    private async Task HandleReminders(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Checking for pending email reminders");
        if (cancellationToken.IsCancellationRequested)
            throw _cancelException;
        var scheduledTime = GetScheduledTime();
        var oneWeekAgo = DateTime.UtcNow.AddDays(-7).Date;
        var candidates = await _context
            .Users.Where(u => u.EmailFrequency == EmailFrequency.Weekly)
            .Where(u => u.EmailReminderSentAt == null || u.EmailReminderSentAt <= scheduledTime)
            .Where(u =>
                _context.Notifications.Any(n => n.UserId == u.Id && n.HappenedAt > oneWeekAgo)
            )
            .OrderBy(u => u.Id)
            .Take(BatchSize)
            .ToListAsync();

        if (cancellationToken.IsCancellationRequested)
            throw _cancelException;

        if (!candidates.Any())
        {
            _logger.LogInformation("No pending email reminders to process.");
            return;
        }

        // Preload notifications counts
        var candidateIds = candidates.Select(c => c.Id).ToList();
        var notificationCounts = await _context
            .Notifications.Where(n => candidateIds.Contains(n.UserId) && n.HappenedAt > oneWeekAgo)
            .GroupBy(n => n.UserId)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

        var startDate = oneWeekAgo.Date;
        var endDate = scheduledTime.Date;

        foreach (var candidate in candidates)
        {
            if (cancellationToken.IsCancellationRequested)
                throw _cancelException;

            if (!notificationCounts.TryGetValue(candidate.Id, out var count) || count == 0)
                continue;

            candidate.EmailReminderSentAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            var template = new EmailTemplates.NotificationReminder
            {
                User = candidate,
                NotificationCount = count.ToString(),
                StartDate = startDate,
                EndDate = endDate,
            };

            await _emailService.SendEmail(template);
            _logger.LogInformation("Sent reminder to user {UserId}", candidate.Id);
        }

        _logger.LogInformation("Finish sending email reminders");
    }

    private async Task HandleActionItemAssigned(Notification notification)
    {
        var nPayload = notification.GetPayload<NotificationPayloads.ActionItemAssigned>();
        //the notification payload holds the task information and it needs to be passed to the email template
        var template = new EmailTemplates.ActionItemAssigned
        {
            Client = notification.Client,
            Project = notification.Project,
            User = notification.User,
            Actor = notification.Actor!,
            TaskId = nPayload.TaskId,
            TaskName = nPayload.TaskName,
            TaskType = nPayload.TaskType,
            DueDate = nPayload.DueDate,
        };
        await _emailService.SendEmail(template);
    }

    private async Task HandleActionItemStatusChanged(Notification notification)
    {
        var nPayload = notification.GetPayload<NotificationPayloads.ActionItemStatusChanged>();
        //the notification payload holds the task information and it needs to be passed to the email template

        var template = new EmailTemplates.ActionItemStatusChanged
        {
            Client = notification.Client,
            Project = notification.Project,
            User = notification.User,
            Actor = notification.Actor!,
            TaskId = nPayload.TaskId,
            TaskName = nPayload.TaskName,
            TaskType = nPayload.TaskType,
            Status = nPayload.Status,
        };
        await _emailService.SendEmail(template);
    }

    private DateTime GetScheduledTime()
    {
        var scheduledDay = _config.WeeklyReminder.Day;
        var scheduledTime = _config.WeeklyReminder.Time;
        var utcNow = DateTime.UtcNow;

        // Compute this week's scheduled datetime
        int daysUntilScheduled = ((int)scheduledDay - (int)utcNow.DayOfWeek + 7) % 7;
        var thisWeekScheduled = utcNow.Date.AddDays(daysUntilScheduled).Add(scheduledTime);

        // If current time is before scheduled, go back a week
        if (utcNow < thisWeekScheduled)
            thisWeekScheduled = thisWeekScheduled.AddDays(-7);

        return thisWeekScheduled.Date;
    }
}
