using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.BGP.ControlAPI.Spec;
using BCP.Core.BGP.FirmAPI;
using BCP.Core.Client;
using BCP.Core.Common;
using BCP.Core.Email;
using BCP.Core.Events;
using BCP.Core.FirmAPI.Orchestration;
using BCP.Core.Modules.BGP.FirmAPI.Models.AddUsers;
using BCP.Core.Modules.BGP.FirmAPI.Models.RemoveUsers;
using BCP.Core.Project;
using BCP.Data;
using BCP.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EventPayloads = BCP.Core.Events.Payloads;
using UserModel = BCP.Data.Models.User;

namespace BCP.Core.User
{
    public partial class TeamMemberService : ITeamMemberService
    {
        private readonly IControlAPIService _bgpService;
        private readonly IUserService _userService;
        private readonly IUserContext _userContext;
        private readonly IClientService _clientService;
        private readonly IProjectService _projectService;
        private ILogger<ITeamMemberService> _logger;

        private IEventService _eventService;
        private IFirmAPIService _firmApiService;
        private IEmailService _emailService;
        private DataContext _dataContext;
        private WebSettings _settings;
        private readonly IFirmOrchestrationConfig _orchestrationConfig;
        private const int INVITE_USER_CHUNK_SIZE = 3;

        public TeamMemberService(
            IControlAPIService bgpService,
            IUserService userService,
            IUserContext userContext,
            IClientService clientService,
            IProjectService projectService,
            ILogger<ITeamMemberService> logger,
            IEventService eventService,
            IFirmAPIService firmApiService,
            IEmailService emailService,
            DataContext dataContext,
            WebSettings settings,
            IFirmOrchestrationConfig orchestrationConfig
        )
        {
            _clientService = clientService;
            _projectService = projectService;
            _bgpService = bgpService;
            _userService = userService;
            _userContext = userContext;
            _logger = logger;
            _eventService = eventService;
            _firmApiService = firmApiService;
            _emailService = emailService;
            _dataContext = dataContext;
            _settings = settings;
            _orchestrationConfig = orchestrationConfig;
        }

        public async Task<AddUsersResponse> AddUsers(BDOAddUsersRequest request)
        {
            if (request == null)
                throw new CoreException(CoreError.BadRequest);

            // TODO: Client and Project parameters should be refactored to receive BCP IDs
            // instead of BGP IDs.
            var client = await _clientService.GetByBgpId(request.ClientId);

            if (client == null)
                throw new CoreException("Invalid Client", CoreError.BadRequest);

            var project = await _projectService.GetByBgpId(request.ClientId, request.ProjectId);

            if (project == null)
                throw new CoreException("Invalid Project", CoreError.BadRequest);

            if (request.Users.Count == 0)
                throw new CoreException("Must add at least one user", CoreError.BadRequest);

            if (!client.BGPId.HasValue || !project.BGPId.HasValue)
                throw new CoreException("Invalid project or client", CoreError.BadRequest);

            // Check that the user at a minimum has access to this project or client
            await _userContext.CheckAccess(
                BcpRole.ClientUser,
                client.BGPId.Value,
                project.BGPId.Value
            );

            // This call always returns a role even if you do not have access therefore we need the call above
            var role = await _userContext.GetRole(
                new GetCurrentUserRolesRequest()
                {
                    ClientId = client.BGPId.Value,
                    MemberFirmId = _settings.MemberFirmId,
                    ProjectId = project.BGPId.Value,
                }
            );

            if (
                role == BcpRole.ClientUser
                && (
                    request.Users.Any(u => u.GroupName != BCPUserGroups.ClientUser)
                    || request.Users.Any(u => Validators.BDOEmailRegex().IsMatch(u.Email))
                )
            )
            {
                throw new CoreException("Attempting to add a user with a privileged role");
            }
            else if (
                role == BcpRole.BdoPractitioner
                && request.Users.Any(u =>
                    u.GroupName != BCPUserGroups.ClientUser && u.GroupName != BCPUserGroups.BdoUser
                )
            )
            {
                throw new CoreException("Attempting to add a user with a privileged role");
            }
            else if (
                role == BcpRole.BdoClientAdmin
                && request.Users.Any(u =>
                    u.GroupName != BCPUserGroups.ClientUser
                    && u.GroupName != BCPUserGroups.BdoUser
                    && u.GroupName != BCPUserGroups.BdoClientAdmin
                )
            )
            {
                throw new CoreException("Attempting to add a user with an unsupported role");
            }
            else if (
                role != BcpRole.BdoClientAdmin
                && role != BcpRole.BdoPractitioner
                && role != BcpRole.ClientUser
            )
            {
                throw new CoreException("Unrecognized role");
            }

            var users = new List<UserModel>(10);
            var errors = new List<AddUsersResponseResult>(10);

            foreach (var batch in request.Users.Chunk(INVITE_USER_CHUNK_SIZE))
            {
                var addUserTasks = batch.Select(async user =>
                {
                    var firmRequest = new AddProjectUserApiFirmsRequest
                    {
                        ClientId = client.BGPId.Value,
                        ProjectId = project.BGPId.Value,
                        FirmId = _settings.MemberFirmId,
                        Details = new AddProjectUserApiFirmsRequestDetails
                        {
                            Email = user.Email,
                            FirstName = null,   //First name cannot be provided for BDO group users when calling _firmApiService.AddUser(firmRequest)
                            LastName = null,
                            Role = user.GroupName,
                            OnboardingRedirectUrl = _settings.RedirectUri,
                        },
                    };
                    var userId = await _firmApiService.AddUser(firmRequest);

                    BDOUser bdoUser = new BDOUser
                    {
                        EmailAddress = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        UniqueUserId = userId.Replace("\"", ""),
                    };
                    if (String.IsNullOrWhiteSpace(userId))
                    {
                        return (false, user.Email, null);
                    }

                    var dbUser = await _userContext.GetOrCreateUser(bdoUser);
                    return (true, user.Email, dbUser);
                });

                var ct = new CancellationToken();
                var taskResults = await Task.WhenAll(addUserTasks).WaitAsync(ct);

                // Need to do this tuple logic as users and errors lists are not thread safe
                foreach (var (success, email, dbUser) in taskResults)
                {
                    if (!success)
                    {
                        errors.Add(
                            new AddUsersResponseResult(
                                new BDOAddUsersResponseResult { Email = email, ErrorCode = "500" }
                            )
                        );
                    }
                    else
                    {
                        users.Add(dbUser);
                    }
                }
            }

            // Log Project Event
            if (project != null)
            {
                await _eventService.LogEvent(
                    new EventPayloads.ProjectChanged
                    {
                        ClientId = client.Id,
                        ProjectId = project.Id,
                        AddedUsers = users.Select(x => x.Id),
                    }
                );
            }

            var succeedResults = users.Select(x => new AddUsersResponseResult(x)).ToList();
            var results = succeedResults.Concat(errors).Select(x => x!).ToList();
            return [.. results];
        }

        public async Task ResendEmailInvitationAsync(BDOReinviteUser request)
        {
            if (request.ProjectId is not > 0)
                throw new CoreException(CoreError.BadRequest, "Project is required");

            var project = await _projectService.GetByBgpId(
                request.ClientId,
                request.ProjectId.Value
            );

            if (project == null)
                throw new CoreException("Invalid Project", CoreError.BadRequest);

            var bgpProjectUsers = await _firmApiService.GetProjectUsers(
                (int)request.ClientId,
                (int)request.ProjectId
            ); // Already onboarded users will have an empty redemption URL

            var bgpProjectUser = bgpProjectUsers.FirstOrDefault(user =>
                user.UniqueUserId == request.UserId.ToString()
            );

            if (bgpProjectUser == null)
                throw new CoreException(
                    $"User {request.UserId} not found from Firm API for resend invite"
                );

            var client = _dataContext
                .Clients.Where(p => p.BGPId == request.ClientId)
                .FirstOrDefault();
            if (client == null)
                throw new CoreException("Invalid Client", CoreError.BadRequest);

            var user = _dataContext.Users.Where(p => p.BgpId == request.UserId).FirstOrDefault();

            if (user == null)
                throw new CoreException(
                    $"User {request.UserId} not found from Database for resend invite"
                );

            var targetUrl = !string.IsNullOrEmpty(bgpProjectUser.RedemptionUrl)
                ? bgpProjectUser.RedemptionUrl
                : _settings.RedirectUri;

            if (string.IsNullOrEmpty(targetUrl))
                throw new CoreException($"Invalid Redemption or Site URL for {request.UserId}");

            await _eventService.LogEvent(
                new EventPayloads.ProjectChanged
                {
                    ClientId = client.Id,
                    ProjectId = project.Id,
                    ReinvitedUsers = [request.UserId],
                }
            );

            await SetUserInvitedAt([user.Id]);

            _logger.LogInformation($"ResendEmailInvitationAsync completed");
        }

        public async Task<List<TeamMemberUser>> GetProjectTeamMembersAsync(
            int clientId,
            int projectId
        )
        {
            var members = await _bgpService.GetProjectUsers(clientId, projectId);
            var users = new List<UserModel>();
            var notifications = new List<Notification>();
            if (members == null || members.Length == 0)
            {
                _logger.LogInformation("No team members found.");
                return new List<TeamMemberUser>();
            }
            foreach (var member in members.Where(x => x != null))
            {
                var user = await _userContext.GetOrCreateUser(member);
                users.Add(user);
                var notification = await _dataContext.Notifications.FirstOrDefaultAsync(x =>
                    x.NotificationType == NotificationType.PortalInvite && x.UserId == user.Id
                );
                notifications.Add(notification);
            }

            var tbUsers = users
                .Zip(notifications)
                .Select(x => new TeamMemberUser(x.First, x.Second))
                .Select(x =>
                {
                    var tempType = members
                        ?.Where(y => y.UniqueUserId == x.BgpId.ToString())
                        ?.FirstOrDefault()
                        ?.UserType;
                    if (tempType != null)
                    {
                        switch (tempType)
                        {
                            case "Client User":
                                x.UserType = UserRole.ClientUser;
                                break;
                            case "BDO User":
                                x.UserType = UserRole.Practitioner;
                                break;
                            case "BDO Admin User":
                                x.UserType = UserRole.BDOClientAdmin;
                                break;
                            default:
                                Console.WriteLine("Unrecognized User Role");
                                x.UserType = UserRole.ClientUser;
                                break;
                        }
                    }

                    return x;
                })
                .ToArray();

            // Filter out the service account
            var filteredUsers = FilterServiceAccount(tbUsers);

            return filteredUsers.ToList();
        }

        public async Task UpdateProjectRoles(UpdateAccessRequest request)
        {
            var removeAccessRequest = new RemoveBgpAccessRequest
            {
                ClientId = request.ClientId,
                Email = request.Email,
                GroupName = request.GroupName[0],
                FirmId = request.MemberFirmId,
                ProjectId =
                    request.GroupName[0] == BCP.Data.Models.UserGroups.BdoAdmin
                        ? null
                        : request.ProjectId,
            };

            await _bgpService.RemoveUserRoles(removeAccessRequest);

            var addAccessRequest = new AddBgpAccessRequest
            {
                ClientId = request.ClientId,
                FirmId = request.MemberFirmId,
                ProjectId =
                    request.GroupName[1] == BCP.Data.Models.UserGroups.BdoAdmin
                        ? null
                        : request.ProjectId,
                Users =
                [
                    new AddAccessUser
                    {
                        DisplayName = request.DisplayName,
                        Email = request.Email,
                        GroupName = request.GroupName[1],
                        UniqueUserId = request.UniqueUserId,
                    },
                ],
            };

            await _bgpService.AddUserRoles(addAccessRequest);

            if (request.GroupName[1] == BCP.Data.Models.UserGroups.BdoAdmin)
            {
                var client = await _clientService.GetByBgpId(request.ClientId);

                if (client == null)
                    return;

                var payload = new EventPayloads.ClientChanged
                {
                    ClientId = client.Id,
                    UpdatedUsers = [request.UniqueUserId],
                };
                await _eventService.LogEvent(payload);
            }
        }

        public async Task ClientUserRemoveUser(RemoveBgpAccessRequest request)
        {
            if (request.ProjectId == null)
            {
                throw new ArgumentException("project id is required");
            }
            var removeAccessRequest = new RemoveProjectUserApiFirmsRequest
            {
                ClientId = request.ClientId,
                ProjectId = request.ProjectId.Value,
                Details = new RemoveProjectUsersApiFirmsRequestDetails { Email = request.Email },
            };

            await _firmApiService.RemoveUser(removeAccessRequest);
        }

        public async Task<bool> UpdateTeamMemberProfileAsync(Data.Models.User userInfo)
        {
            try
            {
                if (userInfo == null)
                {
                    throw new ArgumentNullException(nameof(userInfo));
                }

                /*
                 * in this case the UI passes BGPID and the Id. This should be fixed in the UI to ommit
                 * the BGPId from the request to reduce confusion.
                */
                var currentUser = await _dataContext
                    .Users.Where(u => u.BgpId == userInfo.Id)
                    .FirstOrDefaultAsync();

                if (currentUser == null)
                {
                    throw new CoreException("User does not exist");
                }
                currentUser.PhoneNumber = userInfo.PhoneNumber;
                currentUser.Department = userInfo.Department;
                currentUser.Title = userInfo.Title;
                await _dataContext.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in UpdateTeamMemberProfileAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<BDOProjectMember[]?> GetProjectMembersAsync(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            try
            {
                return await _bgpService.GetProjectMembersAsync(memberFirmId, clientId, projectId);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Error fetching project members. MemberFirmId: {memberFirmId}, ClientId: {clientId}, ProjectId: {projectId}",
                    ex
                );
            }
        }

        private async Task SetUserInvitedAt(Guid[] ids)
        {
            if (!ids.Any())
                return;
            await _dataContext
                .Users.Where(u => ids.Contains(u.Id))
                .ExecuteUpdateAsync(setters =>
                    setters.SetProperty(u => u.InvitedAt, DateTime.UtcNow)
                );
        }

        public async Task<BDOSearchUser[]> GetMemberFirmUsers()
        {
            var response = await _bgpService.GetMemberFirmUsers(_settings.MemberFirmId);
            var users = response?.Where(u => u != null).ToArray();
            if (users == null || users.Length == 0)
                return [];
            var emailSet = users
                .Select(u => u.EmailAddress)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);
            var bcpUsers = await _dataContext
                .Users.Where(x => emailSet.Contains(x.Email))
                .ToDictionaryAsync(x => x.Email, StringComparer.OrdinalIgnoreCase);

            foreach (var user in users)
            {
                // TODO: review move this logic elsewhere
                if (user.UserGroupName != BCPUserGroups.ClientUser)
                    user.UserGroupName = BCPUserGroups.BdoUser;

                if (bcpUsers.TryGetValue(user.EmailAddress, out var bcpUser))
                {
                    user.DisplayName = bcpUser.DisplayName;
                }
            }

            // Filter out the service account
            users = FilterServiceAccount(users);

            return users;
        }

        public async Task<BDOPortalMember[]> GetPortalUsers(int clientId)
        {
            var users = await this._bgpService.GetPortalUsers(_settings.MemberFirmId, clientId);
            if (users == null)
                return [];
            var bcpUsers = _dataContext.Users.Where(x =>
                users.Select(u => u.EmailAddress).Contains(x.Email)
            );
            foreach (var user in users.Where(x => x != null))
            {
                var bcpUser = await bcpUsers
                    .Where(u => u.Email == user.EmailAddress)
                    .FirstOrDefaultAsync();
                if (bcpUser != null)
                {
                    user.DisplayName = bcpUser.DisplayName;
                }
            }

            // Filter out the service account
            users = FilterServiceAccount(users);

            return users;
        }

        /// <summary>
        /// Filters out service accounts from user collections.
        /// Uses email-based filtering for robust identification.
        /// Excludes configured service account and any email containing "svc".
        /// </summary>
        private T[] FilterServiceAccount<T>(T[] users)
            where T : class
        {
            // If no service account configured, return as-is
            if (string.IsNullOrEmpty(_orchestrationConfig.ServiceAccountName))
            {
                return users;
            }

            return users.Where(user => !IsServiceAccount(user)).ToArray();
        }

        /// <summary>
        /// Determines if a user is a service account by comparing email addresses.
        /// ServiceAccountName contains the service account email address.
        /// Also filters accounts containing "svc" in their email address.
        /// </summary>
        private bool IsServiceAccount<T>(T user)
            where T : class
        {
            var emailProperty =
                user.GetType().GetProperty("EmailAddress") ?? user.GetType().GetProperty("Email");

            if (emailProperty != null)
            {
                var userEmail = emailProperty.GetValue(user)?.ToString();

                if (string.IsNullOrEmpty(userEmail))
                {
                    return false;
                }

                // Check if email matches the configured service account
                if (
                    !string.IsNullOrEmpty(_orchestrationConfig.ServiceAccountName)
                    && string.Equals(
                        userEmail,
                        _orchestrationConfig.ServiceAccountName,
                        StringComparison.OrdinalIgnoreCase
                    )
                )
                {
                    return true;
                }

                // Check if email contains "svc" (case-insensitive)
                if (userEmail.Contains("svc", StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
