{"action-item": "Action Item", "cancel": "Cancel", "cancel-add-new": "Cancel Add new flow", "confirm-file-deletion": "Confirm File Deletion", "confirm-file-deletion-sub": "Are you sure you want to delete this file? It will be stored in the recycle bin for 94 days before permanent deletion.", "confirm-folder-deletion": "Confirm folder deletion", "confirm-folder-deletion-sub": "Are you sure you want to delete this folder and its contents?  Items will be stored in the recycle bin for 94 days before permanent deletion.", "copy-link": "Copy link", "create-new-folder": "Create new folder", "create-new-subfolder": "Create new subfolder", "created-by": "Created by", "current": "Current", "current-folder": "Current folder", "days-left_one": "{{count}} day", "days-left_other": "{{count}} days", "delete-info-toast": "\"{{fileName, string}}\" is being moved to the Recycle Bin. This may take a few moments", "delete-modal-cta": "Delete permanently", "delete-modal-subtitle": "Are you sure you want to permanently delete these files? This action cannot be undone.", "delete-modal-title": "Confirm Permanent deletion - This Action Cannot Be Undone", "delete-modal-warning": "Files will be lost permanently and cannot be recovered.", "delete-success_one": "1 item permanently deleted. This action cannot be undone.", "delete-success_other": "{{count}} items permanently deleted. This action cannot be undone.", "document-details": "Document details", "document-name": "Document name", "document-name-is-required": "Document name is required", "documents": "Documents", "documents-title": "Manage your project documents", "download-file": "Download {{name}}", "download-selected": "Download selected", "download-selected-count": "Download {{count}} selected files", "export": "Export", "send-to-apt": "Send to APT", "downloaded": "1 file downloaded", "downloaded-pl": "{{count}} files downloaded", "downloading": "Downloading 1 item", "downloading-pl": "Downloading {{count}} items", "drag-files-here": "Drag files here to upload", "drag-files-here-sub-message": "or click ‘Upload’ button", "drag-and-drop-disabled": "You need edit access to upload files into a read-only folder.", "draft-deliverables": "Draft Deliverables", "dropdownmenu-file": "Document {{name}} actions dropdown", "empty-folder": "This folder is empty", "empty-folder-search": "We didn't find any results.", "empty-folder-search-sub-message": "Try changing your search or filter settings", "empty-recycle-bin": "This folder is empty.", "error-invalid-file-name": "\n File name cannot contain any of the following special characters:  “ . * : < > () ? /  ~ | \\ \n File name cannot be longer than 178 characters", "error-invalid-file-name-input": "Invalid input: \n File name cannot contain any of the following special characters:  “. * : < > () ? /  ~ | \\ \n File name cannot be longer than 178 characters", "error-invalid-file-type": "Invalid file type", "error-invalid-folder-name-input": "Invalid input: \n Folder name cannot contain any of the following special characters:  “ * : < > ? /  ~ | \\ \n Folder name cannot be longer than 100 characters \n Folder name cannot end in ‘.’", "error-restricting-folder-access": "Folder {{folderName, string}} Could not restrict folder access", "error-too-large": "The file is too large. Please upload a file smaller than 2 GB.", "error-updating-permissions": "Error updated permissions for folder {{folderName, string}}", "failed-delete-permanently": "Failed to delete. Please try again.", "failed-to-bulk-delete": "Unable to delete one or more items", "failed-to-download-item": "Failed to download item with path {{path, string}}", "failed-to-move-bulk_one": "Failed to move {{count}} item. Please try again later.", "failed-to-move-bulk_other": "Failed to move {{count}} items. Please try again later.", "failed-to-move": "Failed to move {{name}}.", "failed-to-move-bulk": "Unable to move items.", "failed-to-move-exists-message": "A file with the same name already exists in the selected folder. Please choose a different destination or rename the file to move.", "failed-to-move-restricted": "This folder can’t be moved.", "failed-to-move-restricted-message": "Some items inside need higher access. Please contact a BDO team member or move the items one at a time.", "failed-to-move-to-recycle-bin": "Failed to move {{fileName}} to recycle bin. Please try again later.", "failed-to-rename": "Failed to rename document", "failed-to-restore": "Failed to restore {{fileName}}. Please try again later.", "failed-to-restore-multiple": "Failed to restore {{count}} items. Please try again later.", "failed-to-upload": "Failed to upload {{fileName, string}}", "failed-to-upload-unknown-error": "An error occurred. Please try again later.", "file-already-exists": "A document with this name already exists. Please choose a different name.", "file-details": "File details", "file-type": "File type", "file-type-filter-title": "Filter by file type", "file-upload-failed-multiple": "{{count}} file(s) failed to upload", "file-uploaded": "File(s) uploaded successfully", "file-uploaded-multiple": "{{count}} file(s) uploaded successfully", "file-uploading": "Uploading...", "file": "file", "files": "files", "folder": "folder", "final-deliverables": "Final Deliverables", "folder-details": "Folder details", "folder-name": "Folder name", "folder-name-exists": "A folder with this name already exists", "folder-name-is-invalid": "Folder name is invalid", "folder-name-is-required": "Folder name is required", "folders": "Folders", "items-selected_one": "{{count}} item selected", "items-selected_other": "{{count}} items selected", "go-back-to": "Go back to {{name}}", "last-modified": "Last Modified", "link-copied": "Link copied successfully", "location": "Location", "manage-access": "Manage access", "modified": "Modified", "move-bulk-to-recycle-bin-message": "Are you sure you want to move {{count}} items to the recycle bin? These items will stay in the recycle bin for 94 days before they’re permanently deleted from the portal.", "move-bulk-to-recycle-bin-title": "Move {{count}} items to the recycle bin?", "move-cta": "Move", "move-fail-message-exists": "{{skipped}} was already there and wasn’t moved.", "move-fail-message-exists-multiple": "{{skipped}} items were already there and weren’t moved.", "move-file": "Move file", "move-folder-is-restricted-message": "Any items you move here will follow the same access settings.", "move-folder-is-restricted-title": "This folder has limited access", "move-multiple": "Move {{count}} items", "move-partial-success-message": "{{skipped}} was already there and wasn’t moved.", "move-partial-success-message-multiple": "{{skipped}} items were already there and weren’t moved.", "move-partial-success-title_one": "{{count}} item moved to  \"{{destinationFolder}}\".", "move-partial-success-title_other": "{{count}} items moved to  \"{{destinationFolder}}\".", "move-read-only-message": "The new folder’s settings will apply to the moved items.", "move-read-only-title": "You’re moving read-only items for client users.", "move-restricted-items-message": "Some files or folders have permission limits. Their access may change depending on the destination folder’s permission settings.", "move-restricted-items-title": "Are you sure you want to move restricted items?", "move-restricted-recycle-bin-message": "These files will retain their current access restrictions while in the recycle bin.", "move-restricted-recycle-bin-title": "There are restricted items in your selection.", "move-success-multiple": "{{count}} items moved to \"{{destinationFolder}}\" successfully", "move-success-multiple-file-already-there": "{{count}} item was already there and wasn’t moved.", "move-success-single-title": "1 item moved to \"{{destinationFolder}}\" successfully.", "move-success-toast": "successfully moved to", "move-to-read-only-message": "Items in this folder can’t be edited or moved by client users.", "move-to-read-only-title": "Client users have view-only access", "move-to-recycle-bin": "Move to recycle bin", "move-to-recycle-bin-progress_one": "{{count}} item is being moved to the recycle bin. This may take a few moments.", "move-to-recycle-bin-progress_other": "{{count}} items are being moved to the recycle bin. This may take a few moments.", "move-to-recycle-bin-read-only-message": "They’ll retain their current access restrictions in the recycle bin.", "move-to-recycle-bin-read-only-title": "There are items in your selection that are read only for client users", "move-to-recycle-bin-success": "{{count}} items moved to the recycle bin successfully.", "nav-alt-text": "Breadcrumb navigation", "new-file": "New", "new-folder": "New folder", "new-folder-name": "New folder name", "open": "Open", "original-location": "Original location", "permanently-delete": "Permanently delete", "preparing-files": "Preparing files", "read-only": "Read-only", "read-only-tooltip": "You need edit access to move items into read-only folders", "recycle-bin": "Recycle bin", "recycle-bin-info": "Items in the recycle bin will be permanently deleted after 94 days", "rename": "<PERSON><PERSON>", "rename-document": "Rename document", "rename-folder": "Rename folder", "rename-success-toast": "'{{prev}}' was renamed to '{{curr}}' successfully", "restore": "Rest<PERSON>", "restore-modal-sub": "To view the contents of this folder, restore it from the recycle bin.", "restore-modal-title": "This folder is in recycle bin", "restore-item-success": "Restored {{fileName}} successfully", "restore-item-success-multiple": "Restored {{count}} items successfully", "restrict-access": "Restrict access to specific users", "restricted": "Restricted", "restricted-to-select-users": "Restricted to select users", "save": "Save", "search-by-name": "Search by name", "search-files": "Search files", "search-in-recycle": "Search in Recycle Bin", "search-label": "Search for {{searchTerm}} globally", "select-all": "Select all {{count}} documents", "select-a-new-folder": "Select a new folder for", "select-single-for-download": "Select {{name}} for download", "select-single-for-removal": "Select {{name}} for removal", "select-users-placeholder": "Select users to access this folder", "set-folder-permissions": "Set folder permissions", "shared-with": "shared with", "shared-with-everyone": "Shared with the entire project & client team members", "shared-with-everyone-radio-btn-title": "Entire Project Team", "success-creating-folder-error-updating-permissions": "Folder {{folderName, string}} created but failed to set permissions", "success-creating-new-folder": "'{{folderName, string}}' folder successfully created", "success-updating-permissions": "Access settings for '{{itemName, string}}' updated successfully", "time-left": "Time left", "title": "Title", "type": "Type", "unselect-all": "Unselect all {{count}} files", "update-permissions-modal-title": "Manage access for {{itemName, string}}", "upload-by-filter-title": "Filter by uploaded", "upload-file": "Upload file", "upload-files": "Upload files", "uploaded-by": "Uploaded by", "upload": "Upload", "upload-folder": "Upload folder", "users-have-access-modal-title": "This {{type}} is shared with {{count}} user(s)", "view-details": "View details for {{title}}", "view-folder": "View folder", "folder_contractual_documents": "Contractual Documents", "folder_client_documentation": "Client Documentation", "folder_status_reports": "Status Reports", "folder_deliverables": "Deliverables", "folder_draft_deliverables": "Draft Deliverables", "folder_ready_to_be_signed": "Ready to be Signed", "folder_final_deliverables": "Final Deliverables", "folder_archive": "Archive", "folder_to_be_signed_by_client": "To be signed by client", "view-all-users": "View all users", "apt-export-modal-title": "Send to APT Workspace", "apt-export-button": "Send to APT", "apt-export-description": "You're about to export these files and folders to 'Portal documents' in your APT workspace:", "apt-export-folder-structure-info": "Folder structure will stay the same as it is in this portal", "apt-export-subfolder-info": "Subfolders and all files inside will be included", "apt-export-remove-tooltip": "Remove from export", "apt-export-empty-state": "No files or folders selected for export.", "apt-export-folder": "FOLDER", "apt-export-unknown-file-type": "UNKNOWN", "file-size-kb": "KB", "file-size-mb": "MB", "file-size-gb": "GB", "apt-export-success-title": "Items sent to APT successfully", "apt-export-success-message": "Please allow a few minutes for the process to run", "apt-export-error-title": "Export to APT failed", "apt-export-error-message": "An error occurred while exporting files to APT", "apt-export-invalid-file-type": "Unsupported file type: (.html)", "apt-export-file-exceeds-size": "This file exceeds the file size limitation (50MB)", "apt-export-contains-invalid-file-type": "This folder contains an unsupported file type (.html).", "apt-export-contains-oversized-file": "This folder contains a file larger than 50MB"}