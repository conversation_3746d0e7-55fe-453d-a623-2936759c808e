namespace BCP.Core.Email.Translations;

public class EnglishTranslation : IEmailTranslation
{
    // Notifications
    public string Notification_Footer_Cta => "please click here";
    public string Notification_Footer_Short_Cta => "please click here";
    public string Notification_Footer_Note =>
        "You are receiving this notification email because there is a BDO client account associated with this email address. If you wish to change your notification preferences, ";

    // PortalInvite
    public string PortalInvite_Title => "Welcome to your BDO Client Portal Experience";
    public string PortalInvite_Body =>
        "You're invited to access your personalized workspace for business projects and collaboration with the BDO team. Click below to set up your account.";
    public string PortalInvite_Cta => "Begin setup";

    // ProjectInvite
    public string ProjectInvite_Title =>
        "[BDO Client Portal] {{name}} has invited you to your project.";

    public string ProjectInvite_Title_NULL =>
        "[BDO Client Portal] You have been invited to a project.";

    public string ProjectInvite_Body =>
        "{{name}} invited you to collaborate on a project. The BDO Client Portal gives you a secure and streamlined way to work with your BDO team. Through the portal, you’ll be able to:";

    public string ProjectInvite_Body_NULL =>
        "You have been invited to collaborate on a project. The BDO Client Portal gives you a secure and streamlined way to work with your BDO team. Through the portal, you’ll be able to:";

    public string[] ProjectInvite_Bullets =>
        [
            "View project timelines and key milestones",
            "Stay informed of any risks or issues",
            "Track progress and receive timely updates",
            "Access documents and communications in one place",
            "See action items assigned to you and manage your tasks",
        ];

    public string ProjectInvite_Cta => "Access your Project";

    public string ClientAdminInvite_Subject =>
        "Welcome to your BDO Client Portal Experience: Start Setting Up Your Project";
    public string ClientAdminInvite_Greeting => "Hi {{name}},";
    public string ClientAdminInvite_Body =>
        "You now have access to your new Client Portal as a BDO Client Admin. Please login and begin setting up the portal for your client.";
    public string ClientAdminInvite_Cta => "Set Up your BDO Client Portal";
    public string ClientAdminInvite_StepTitle => "Get started with your project setup:";
    public (string, string, string)[] ClientAdminInvite_Steps =>
        [
            ("Add your team members", "Invite the right people to collaborate.", "team"),
            ("Set up your project timeline", "Plan key dates and milestones.", "timeline"),
            ("Add and assign action items", "Keep work organized and on track.", "action"),
            ("Identify project risks", "Flag any known concerns early.", "risk"),
        ];
    public string ClientAdminInvite_HelpTitle => "Need help getting started?";
    public string ClientAdminInvite_HelpBody => "Here are a few helpful resources:";
    public string ClientAdminInvite_ResourceName => "Centre of Excellence";

    // NotificationReminder
    public string NotificationReminder_Subject => "Your Weekly BDO Client Portal Summary";
    public string NotificationReminder_Title =>
        "Hi {{name}}, you have {{notifications}} new notification(s) this week!";
    public string NotificationReminder_DateRange => "{{startDate}} – {{endDate}}";
    public string NotificationReminder_Cta => "View all notifications";
    public string NotificationReminder_Unsubscribe =>
        "To opt out of promotional emails or unsubscribe from promotional emails,";

    //OnboardingCompleted
    public string OnboardingCompleted_Title => "Explore your BDO Client Portal";
    public string OnboardingCompleted_Heading => "You're all set";
    public string OnboardingCompleted_Body =>
        "Start exploring your personalized workspace to manage business projects and collaborate with your BDO team.";

    public string OnboardingCompleted_Cta => "Access my dashboard";

    public string OnboardingCompleted_Footer_Note =>
        "You are receiving this transactional email because there is a BDO client account associated with this email address.";

    //ActionItem
    public string ActionItem_TaskType(string type) =>
        type.ToLowerInvariant() switch
        {
            "signature" => "Signature request",
            "requestitem" => "Document request",
            "action" => "Project task",
            _ => "",
        };

    public string ActionItem_TaskTypeIcon(string type) =>
        type.ToLowerInvariant() switch
        {
            "signature" => "task-type-signature-request",
            "requestitem" => "task-type-document-request",
            "action" => "task-type-project-task",
            _ => "",
        };

    public string ActionItem_TaskStatus(string status) =>
        status.ToLowerInvariant() switch
        {
            "todo" => "To do",
            "inreview" => "In review",
            "declined" => "Declined",
            "complete" => "Complete",
            _ => "",
        };

    public string ActionItem_TaskStatusIcon(string status) =>
        status.ToLowerInvariant() switch
        {
            "todo" => "task-status-to-do",
            "inreview" => "task-status-in-review",
            "declined" => "task-status-declined",
            "complete" => "task-status-complete",
            _ => "",
        };

    public string ActionItem_TaskStatusBadgeWidth(string status) =>
        status.ToLowerInvariant() switch
        {
            "todo" => "60px",
            "inreview" => "100px",
            "declined" => "100px",
            "complete" => "100px",
            _ => "",
        };

    //ActionItemAssigned
    public string ActionItemAssigned_Subject =>
        "[BDO Client Portal - {{clientName}}] {{taskName}} was assigned to you";
    public string ActionItemAssigned_PreviewText =>
        """{{name}} has assigned <span style="font-weight: 600;">{{taskName}}</span> to you""";
    public string ActionItemAssigned_Title =>
        """{{name}} has assigned <span style="font-weight: 600;">{{taskName}}</span> to you""";
    public string ActionItemAssigned_Cta => "View in BDO Client Portal";
    public string ActionItemAssigned_DueDate => "Due by {{dueDate}}";

    //ActionItemStatusChanged
    public string ActionItemStatusChanged_Subject =>
        "[BDO Client Portal - {{clientName}}] The status of {{taskName}} was updated to {{status}}";
    public string ActionItemStatusChanged_PreviewText =>
        """{{name}} updated the status to {{status}}""";
    public string ActionItemStatusChanged_Title =>
        """{{name}} <span style="font-weight: 600;">updated the status to</span>""";
    public string ActionItemStatusChanged_Cta => "View in BDO Client Portal";

    //FinalDeliveryAdded
    public string FinalDeliveryAdded_Subject =>
        "[BDO Client Portal - {{clientName}}] {{actorName}} uploaded a new deliverable";
    public string FinalDeliveryAdded_Title =>
        """{{actorName}} <span style="font-weight: 600;">has uploaded a new deliverable to your project</span>""";
    public string FinalDeliveryAdded_Cta => "View in BDO Client Portal";
}
