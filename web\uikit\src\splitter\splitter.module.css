.splitter {
  display: flex;
  width: 100%;
  height: 100%;
}

.panel {
  overflow: auto;
  padding: 0;
  height: 100%;
}

.resizeTrigger {
  background-color: #e5e5e5;
  cursor: col-resize;
  padding: 0;
  position: relative;
  transition: background-color 0.2s ease;
  width: 2px;
  border: 0;
  border-radius: 0;
}

.resizeTrigger:hover {
  background-color: var(--color-primary-charcoal);
}

.resizeTrigger:active {
  background-color: var(--color-primary-charcoal);
}

/* Visual indicator when resizing */
.resizeTrigger::after {
  content: "";
  position: absolute;
  top: 0;
  left: -2px;
  right: -2px;
  bottom: 0;
  background: transparent;
}

.resizeTrigger:hover::after {
  background-color: rgba(var(--color-primary-charcoal-rgb), 0.1);
}
