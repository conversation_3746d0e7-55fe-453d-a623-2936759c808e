@keyframes rotation-clockwise {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}

@keyframes rotation-counter-clockwise {
  0% {
    transform: rotate(90deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.treeViewRoot ul {
  width: 100%;
  list-style-type: none;
  padding-inline-start: 0.75rem;
  margin: 0;
}

.treeViewRoot ul li {
  margin-bottom: 5px;
}

.branch {
  padding: 0;
  width: 100%;
  text-align: left;
}

.focusableBranch:focus-visible {
  outline: none;

  .indicator {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: -3px;
    border-radius: 3px;
  }
}

.indicator {
  margin-bottom: 0;
  line-height: 20px;
  font-size: 14px;
  height: 44px;
  align-content: center;
  padding-right: 8px;
  padding-left: 8px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.disabledLink {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.readOnly {
  padding-left: 2rem;
}

.indicator svg {
  vertical-align: middle;
}

.indicator:hover {
  cursor: pointer;
  background-color: #f2f2f2;
  max-width: 210px;
  width: fit-content;
  min-width: -webkit-fill-available;
}

.treeViewSelected {
  background-color: #f2f2f2;
}

.indicator:focus-visible {
  background-color: #f2f2f2;
}

.icons,
.icons .dropdown {
  display: flex;
  align-items: center;
}

.icons .dropdown {
  color: #5b6e7f;
  animation: rotation-counter-clockwise 0.25s linear;
}

.icons .dropdownRotated {
  transform: rotate(90deg);
  display: inline-block;
  color: #5b6e7f;
  animation: rotation-clockwise 0.25s linear;
}

.icons .folder {
  margin-right: 8px;
  margin-left: 8px;
  display: inline-block;
}

.icons .dropdown:focus-visible {
  transform: rotate(90deg);
}

.highlighted {
  background-color: #f2f2f2;
  font-weight: 600;
  width: fit-content;
  min-width: -webkit-fill-available;
}

.labelParent {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  align-content: center;
  color: var(--color-primary-light-charcoal);

  span {
    display: flex;
    align-items: center;
    white-space: nowrap;
    height: 100%;
  }

  span:hover {
    background-color: #f2f2f2;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.restrictedAndReadonly {
  display: flex;
  gap: 8px;
  padding-right: 4px;
}
