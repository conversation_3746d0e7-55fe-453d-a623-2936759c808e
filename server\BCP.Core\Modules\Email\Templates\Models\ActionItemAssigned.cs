using BCP.Core.Email.Translations;

namespace BCP.Core.Email.Templates;

public class ActionItemAssigned : TemplateModel, ITemplateModel
{
    public static string TemplateName => "ActionItemAssigned";
    public override EmailLayout Layout => EmailLayout.Narrow;
    public Guid TaskId { get; set; }
    public string? TaskName { get; set; } = null;
    public string? TaskType { get; set; } = null;
    public DateTime? DueDate { get; set; } = null;
    public string DisplayedTaskType => Translation.ActionItem_TaskType(TaskType);
    public string TaskTypeIcon => Translation.ActionItem_TaskTypeIcon(TaskType);

    private string LongDateFormat =>
        Translation is FrenchTranslation ? "d MMMM yyyy" : "MMM d, yyyy";

    public override string Subject =>
        Translation
            .ActionItemAssigned_Subject.Replace("{{clientName}}", Client?.Name)
            .Replace("{{taskName}}", TaskName);

    public override string PreviewText =>
        Translation
            .ActionItemAssigned_PreviewText.Replace("{{name}}", Actor?.Name)
            .Replace("{{taskName}}", TaskName);

    public string Title =>
        Translation
            .ActionItemAssigned_Title.Replace("{{name}}", Actor?.Name)
            .Replace("{{taskName}}", TaskName);

    public string DisplayedDueDate =>
        DueDate.HasValue
            ? Translation.ActionItemAssigned_DueDate.Replace(
                "{{dueDate}}",
                DueDate?.ToString(LongDateFormat, FormatProvider)
            )
            : "";
}
