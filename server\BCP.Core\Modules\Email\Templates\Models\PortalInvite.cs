namespace BCP.Core.Email.Templates;

public class PortalInvite : TemplateModel, ITemplateModel
{
    public static string TemplateName => "PortalInvite";
    public override EmailLayout Layout => EmailLayout.Wide;
    public override string Subject =>
        EnglishTranslation.PortalInvite_Title + " | " + FrenchTranslation.PortalInvite_Title;
    public override string PreviewText =>
        EnglishTranslation.PortalInvite_Title + " | " + FrenchTranslation.PortalInvite_Title;

    public string RedemptionUrl = string.Empty;

    // PortalInvite is multilanguage, for all other templates use the inherit Translation Property.
    public IEmailTranslation EnglishTranslation => new Translations.EnglishTranslation();
    public IEmailTranslation FrenchTranslation => new Translations.FrenchTranslation();
}
