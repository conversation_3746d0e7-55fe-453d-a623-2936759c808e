.container {
  display: flex;
  height: calc(80vh - 2rem);
  border-radius: 100px;
}

.spinnerContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.tableSection {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem 1rem 0 1.5rem;
  flex: 1;
  width: -webkit-fill-available;
  position: relative;
}

.tableTitle {
  font-weight: 400;
  color: var(--color-primary-inactive-charcoal);
  font-size: 18px;
  line-height: 24px;
  display: flex;
  align-items: center;
  margin: 0;
  list-style: none;
  padding: 0;
}

.lastFolderName {
  margin: 0;
  word-break: break-word;
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-h6);
}

.nameInPath {
  color: var(--color-primary-inactive-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: 24px;
  cursor: pointer;
}

.tableTitle li:not(:first-child) .nameInPath {
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.nameInPath:last-child {
  cursor: default;
}

.nameInPath:hover:not(:last-child) {
  color: var(--color-primary-charcoal);
}

.breadcrumbSeparator {
  align-content: center;
  margin: 0 0.85rem;
  font-size: var(--fontsize-body-small);
}

.currentFolderTitle {
  margin: 0;
  margin-top: 1rem;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  min-height: 2.5rem;
}

.documentDetailsButton {
  display: flex;
  align-items: center;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 50%;
}

.buttonDropdownContainer {
  display: flex;
  gap: 0.75rem;
}

.defaultTrigger {
  padding: 0.5rem 1rem;
  color: var(--color-primary-charcoal);
  line-height: var(--lineheight-body);
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 2px;
  border: 1px solid var(--color-primary-charcoal);
  cursor: pointer;
  font-weight: 600;
  background-color: #fafafa;

  svg {
    margin-left: 0.5rem;
    path {
      fill: var(--color-primary-charcoal);
    }
  }
}

.projectControls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.documentsTop {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 1rem;
  gap: 1rem;
}

.manageAccess {
  font-weight: 600;
  color: var(--color-primary-charcoal);
  font-family: var(--primary-font-family);
  font-size: 16px;
  line-height: 24px;
}

.inputFolderName {
  margin-top: 1rem;
  margin-bottom: 1.3125rem;
}

.cardSelector {
  margin-top: 1.25rem;
  margin-bottom: 0;
}

.toastWrapper {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.restrictedSelector {
  margin-top: 1.5rem;
}

.breadcrumbItem {
  display: flex;
  position: relative;
}
