import React from "react";
import { FileUpload, useFileUpload } from "@ark-ui/react";
import { DocumentUploadProps, DocumentUploadFile } from "./spec";
import styles from "./documentUpload.module.css";
import { Icon } from "~/icon";
import { getDateTimeLabel } from "~/utils/dateUtils";
import { useTranslation } from "react-i18next";
import {
  DEFAULT_MAX_FILES,
  validateFileForUpload,
} from "~/utils/validateFileForUpload";
import classNames from "classnames";
import { Spinner } from "~/spinner";
import { formatISO } from "date-fns";
import { useToast } from "~/toaster/Toaster";

const fileNameExists = (name: string, files: DocumentUploadFile[]): boolean => {
  return files.some(item => item.file.name === name);
};

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  existingFiles = [],
  id,
  title,
  subtitle,
  subtext,
  username,
  maxFiles = 20,
  onFileAccept,
  onFileReject,
  onFileDelete,
  showDropZone = true,
  showNoDocumentsBanner = false,
  dataTestId = "uikit-documentUpload",
  role,
  ariaLabel = "Document Upload",
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
  noDocumentsBannerText,
  required = false,
  children,
  showFormRequiredFields = true,
}) => {
  const { t, i18n } = useTranslation("actionItems");
  const { t: tGlobal } = useTranslation("global");
  const { t: tDocuments } = useTranslation("documents");
  const toast = useToast();

  const onFileChangeHandler = (details: FileUpload.FileChangeDetails) => {
    const isReject = details.rejectedFiles.length > 0;
    const isUpload =
      details.acceptedFiles.length > existingFiles.length && !isReject;
    const isRemove = details.acceptedFiles.length < existingFiles.length;

    if (isReject) {
      if (onFileReject) {
        onFileReject();
      }

      //the file uplaoded was rejected, return
      return;
    }

    if (isUpload && !isRemove) {
      // TODO: as use cases are defined, possibly updates to logic and error checking here might be needed

      const acceptedFiles = details.acceptedFiles;

      //if there are duplicates based on name, lastModified and size, keep only one duplicate
      //if there are 3 duplicates, keep 2 and remove 1 (prevents duplicate error messages from showing)
      //This is known issue from the file upload component where it doesn't reset state after each upload
      const groupedFiles = acceptedFiles.reduce((acc, file) => {
        const key = `${file.name}-${file.lastModified}-${file.size}`;
        if (!acc[key]) {
          acc[key] = [];
        }

        if (acc[key].length < 2) {
          acc[key].push(file);
        } else {
          fileUpload.deleteFile(file);
        }
        return acc;
      }, {} as Record<string, File[]>);

      const deduplicatedFiles = Object.values(groupedFiles).flat();

      const numberOfNewFiles = deduplicatedFiles.length - existingFiles.length;
      const newFiles = deduplicatedFiles.slice(
        deduplicatedFiles.length - numberOfNewFiles
      );
      const uploadedFiles: DocumentUploadFile[] = [];

      let hasError = false;
      newFiles.forEach(newFile => {
        if (fileNameExists(newFile.name, existingFiles)) {
          toast.create({
            type: "error",
            title: tDocuments("failed-to-upload", {
              fileName: newFile.name,
            }),
            description: tDocuments("file-already-exists"),
            errorMessageWithLineBreaks: true,
            replaceLineBreaksWithBulletPoints: true,
          });
          fileUpload.deleteFile(newFile);
          hasError = true;
          return;
        }

        const fileBody: DocumentUploadFile = {
          time: getDateTimeLabel(
            formatISO(new Date()),
            i18n.language,
            false,
            true
          ),
          file: newFile,
          id: newFile.name,
        };

        uploadedFiles.push(fileBody);
      });

      if (hasError || newFiles.length === 0) {
        return;
      }

      const updatedFiles = [...existingFiles, ...uploadedFiles];

      if (onFileAccept) {
        onFileAccept(updatedFiles);
      }
    }
  };

  const onFileDeleteHandler = (file: File) => {
    //remove file from files array
    fileUpload.deleteFile(file);
    const updatedFiles = existingFiles.filter(f => f.id !== file.name);

    if (onFileDelete) {
      onFileDelete(updatedFiles);
    }
  };

  const handleValidateUpload = (file: File) => {
    const { errors } = validateFileForUpload(file);

    for (const error of errors) {
      toast.create({
        title: tDocuments("failed-to-upload", { fileName: file.name }),
        type: "error",
        description: error,
        errorMessageWithLineBreaks: true,
        replaceLineBreaksWithBulletPoints: true,
      });
    }

    return errors;
  };

  const fileUpload = useFileUpload({
    id: id || "file-upload",
    validate: handleValidateUpload,
    onFileChange: onFileChangeHandler,
    maxFiles: maxFiles || DEFAULT_MAX_FILES,
  });
  return (
    <>
      <FileUpload.RootProvider
        value={fileUpload}
        data-testid={dataTestId}
        role={role}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        aria-labelledby={ariaLabelledBy}
        tabIndex={tabIndex}
      >
        <div className={styles.textContainer}>
          <div className={styles.title}>
            <FileUpload.Label onClick={e => e.preventDefault()}>
              {title}
            </FileUpload.Label>
            {required && <span className={styles.titleAsterisk}>*</span>}
          </div>
          {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
          {subtext && <p className={styles.subtitle}>{subtext}</p>}
          {required && showFormRequiredFields && existingFiles.length === 0 && (
            <div className={styles.errorContainer}>
              <div className={styles.errorTextContainer}>
                <Icon iconName="error-circle" altText="Error Icon" size={20} />
                <p className={styles.errorText}>{tGlobal("required-field")}</p>
              </div>
            </div>
          )}
          {children}
        </div>
        {showDropZone && existingFiles.length < maxFiles && (
          <div
            className={classNames(styles.dropzoneContainer, {
              [styles.dropzoneContainerError]:
                required &&
                showFormRequiredFields &&
                existingFiles.length === 0,
            })}
          >
            <FileUpload.Dropzone>
              <Icon iconName="document-add" altText="DocumentAddIcon" />
              <div className={styles.dropdownText}>
                {t("drop-files-here")}{" "}
                <span className={styles.dropdownTextBlue}>
                  {t("browse-files")}
                </span>
              </div>
            </FileUpload.Dropzone>
            <FileUpload.HiddenInput />
          </div>
        )}
        {existingFiles.length > 0 ? (
          <FileUpload.ItemGroup>
            {existingFiles.map((item, index) => (
              <FileUpload.Item
                key={`${item.file.name}-${index}`}
                file={item.file}
              >
                {item.isPending ? (
                  <div className={styles.pendingIcon}>
                    <Spinner isLoading />
                  </div>
                ) : (
                  <div className={styles.documentIcon}>
                    <Icon iconName="documents-icon" altText="DocumentPreview" />
                  </div>
                )}
                <FileUpload.ItemName />
                <div className={styles.nameContainer}>
                  {item.isPending ? (
                    <span>{tGlobal("uploading")}...</span>
                  ) : (
                    <span>
                      {username} • {item.time}
                    </span>
                  )}
                </div>
                <FileUpload.ItemDeleteTrigger
                  onClick={() => onFileDeleteHandler(item.file)}
                >
                  <Icon iconName="dismiss-icon" altText="dismiss-icon"></Icon>
                </FileUpload.ItemDeleteTrigger>
              </FileUpload.Item>
            ))}
          </FileUpload.ItemGroup>
        ) : showNoDocumentsBanner ? (
          <div className={styles.noDocumentsContainer}>
            <div className={styles.noDocumentsInfo}>
              {noDocumentsBannerText
                ? noDocumentsBannerText
                : t("upload-empty")}
            </div>
          </div>
        ) : null}
      </FileUpload.RootProvider>
    </>
  );
};
