using System.Globalization;
using BCP.Core.Email.Translations;
using BCP.Data.Models;

namespace BCP.Core.Email;

public abstract class TemplateModel : ITemplateModelBase
{
    private IEmailTranslation? _translation;
    public IEmailTranslation Translation => _translation ??= GetTranslation();
    private IFormatProvider? _formatProvider;
    public IFormatProvider FormatProvider => _formatProvider ??= GetFormatProvider();
    public required TemplateUser User { get; set; }
    public abstract string Subject { get; }
    public abstract string PreviewText { get; }
    public abstract EmailLayout Layout { get; }
    public string PortalUrl { get; set; } = "#";
    public string AssetUrl => Path.Join(PortalUrl, "/api/email/assets");
    public TemplateEntity? Client { get; set; }
    public TemplateEntity? Project { get; set; }
    public TemplateUser? Actor { get; set; }

    private IEmailTranslation GetTranslation()
    {
        return User.Language switch
        {
            Language.English => new EnglishTranslation(),
            Language.French => new FrenchTranslation(),
            _ => new EnglishTranslation(),
        };
    }

    private IFormatProvider GetFormatProvider()
    {
        return User.Language switch
        {
            Language.French => new CultureInfo("fr-CA"),
            _ => new CultureInfo("en-CA"),
        };
    }
}
