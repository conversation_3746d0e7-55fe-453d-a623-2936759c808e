using BCP.Core.Email.Translations;

namespace BCP.Core.Email.Templates;

public class NotificationReminder : TemplateModel, ITemplateModel
{
    public static string TemplateName => "NotificationReminder";
    public override EmailLayout Layout => EmailLayout.Narrow;

    public string? NotificationCount { get; set; } = null;

    public DateTime? StartDate { get; set; } = null;
    public DateTime? EndDate { get; set; } = null;

    private string ShortDateFormat => Translation is FrenchTranslation ? "d MMMM" : "MMM d";
    private string LongDateFormat =>
        Translation is FrenchTranslation ? "d MMMM yyyy" : "MMM d, yyyy";

    private string StartDateFormat =>
        StartDate?.Year == EndDate?.Year ? ShortDateFormat : LongDateFormat;

    public string DateRange =>
        Translation
            .NotificationReminder_DateRange.Replace(
                "{{startDate}}",
                StartDate?.ToString(StartDateFormat, FormatProvider)
            )
            .Replace("{{endDate}}", EndDate?.ToString(LongDateFormat, FormatProvider));

    public override string Subject => Translation.NotificationReminder_Subject;
    public string Title =>
        Translation
            .NotificationReminder_Title.Replace("{{name}}", User?.Name)
            .Replace("{{notifications}}", NotificationCount);

    public override string PreviewText =>
        Translation
            .NotificationReminder_Title.Replace("{{name}}", User?.Name)
            .Replace("{{notifications}}", NotificationCount);
}
