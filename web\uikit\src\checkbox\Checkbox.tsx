import React, { useEffect, useState } from "react";
import styles from "./checkbox.module.css";
import { Checkbox as ArkCheckbox } from "@ark-ui/react";
import { CheckboxProps, CheckboxSizeEnum } from "./spec";
import { Icon } from "~/icon";
import { useFocusedBy } from "~/utils";
import classNames from "classnames";

export const Checkbox: React.FC<CheckboxProps> = ({
  value = false,
  className = "",
  onChange = () => {},
  disabled = false,
  id,
  label = "",
  customLabel,
  size = CheckboxSizeEnum.large,
  dataTestId = "uikit-checkbox",
  role,
  ariaLabel = "",
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
}) => {
  const [checkboxValue, setCheckboxValue] = useState(value);
  const { focusClass } = useFocusedBy();

  useEffect(() => {
    setCheckboxValue(value);
  }, [value]);

  const handleValueChange = () => {
    const newValue = !checkboxValue;
    setCheckboxValue(newValue);
    onChange(newValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle Enter key for VoiceOver and other screen readers
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (!disabled) {
        handleValueChange();
      }
    }
  };

  return (
    <ArkCheckbox.Root
      id={id}
      className={classNames(styles.checkboxWrapper, className, focusClass)}
      checked={checkboxValue}
      disabled={disabled}
      onCheckedChange={disabled ? () => {} : handleValueChange}
      onKeyDown={handleKeyDown}
      data-testid={dataTestId}
      role={role}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-labelledby={ariaLabelledBy}
      tabIndex={tabIndex}
    >
      <ArkCheckbox.Control
        className={`${styles.checkbox} ${styles[size]} ${
          disabled ? styles.disabled : ""
        }`}
      >
        <ArkCheckbox.Indicator
          className={`${styles.checkmark} ${disabled ? styles.disabled : ""}`}
        >
          <Icon iconName="checkbox-checkmark-icon" altText="checkmark" />
        </ArkCheckbox.Indicator>
      </ArkCheckbox.Control>
      <ArkCheckbox.HiddenInput />
      {customLabel && <ArkCheckbox.Label>{customLabel}</ArkCheckbox.Label>}
      {label.length > 0 && (
        <ArkCheckbox.Label
          className={`${styles.checkboxLabel} ${
            disabled ? styles.disabled : ""
          }`}
          data-checked={checkboxValue}
        >
          {label}
        </ArkCheckbox.Label>
      )}
    </ArkCheckbox.Root>
  );
};
