.buttonDropdownWrapper {
  position: relative;
  z-index: var(--z-index-btn-dropdown);

  [data-scope="menu"][data-part="trigger"] {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 2px;
    padding: 0.5rem 1rem;
    color: var(--color-primary-charcoal);
    line-height: var(--lineheight-body);
    border: 1px solid var(--color-primary-charcoal);
    font-weight: 600;
    background-color: var(--color-primary-soft-charcoal);
    transition: 0.2s background-color;
    outline: none;

    svg {
      path {
        fill: var(--color-primary-charcoal);
      }
    }
  }

  [data-scope="menu"][data-part="trigger"]:hover {
    color: var(--color-bg-white);
    background-color: var(--color-primary-charcoal);
    cursor: pointer;

    svg {
      path {
        fill: var(--color-bg-white);
      }
    }
  }

  [data-scope="menu"][data-part="trigger"]:focus-visible {
    margin: 0;
    border: 1px solid var(--color-primary-charcoal);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }

  [data-scope="menu"][data-part="trigger"].tertiaryTrigger {
    color: var(--color-primary-charcoal);
    border: 1px solid var(--color-primary-charcoal);
    background-color: var(--color-primary-soft-charcoal);

    svg path {
      fill: var(--color-primary-charcoal);
    }
  }

  [data-scope="menu"][data-part="trigger"].tertiaryTrigger:hover {
    color: var(--color-bg-white);
    background-color: var(--color-primary-charcoal);

    svg path {
      fill: var(--color-bg-white);
    }
  }

  [data-scope="menu"][data-part="trigger"].tertiaryTrigger:focus-visible {
    margin: 0;
    border: 1px solid var(--color-primary-charcoal);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }

  [data-scope="menu"][data-part="trigger"].primaryTrigger {
    color: var(--color-bg-white);
    border: 1px solid var(--color-primary-red);
    background-color: var(--color-primary-red);

    svg path {
      fill: var(--color-bg-white);
    }
  }

  [data-scope="menu"][data-part="trigger"].primaryTrigger:hover {
    color: var(--color-bg-white);
    background-color: var(--color-secondary-burgundy);
    border: 1px solid var(--color-secondary-burgundy);
    svg path {
      fill: var(--color-bg-white);
    }
  }

  [data-scope="menu"][data-part="trigger"].primaryTrigger:focus-visible {
    margin: 0;
    border: 1px solid var(--color-secondary-burgundy);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }

  [data-scope="menu"][data-part="content"] {
    top: calc(100% + 0.25rem);
    right: 0;
    border: 0;
    width: 15rem;
    padding: 0;
  }

  [data-scope="menu"][data-part="item"] {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    padding: 1rem;
    gap: 1rem;
    color: var(--color-primary-charcoal);
    cursor: pointer;
    transition: background-color 300ms linear;
    text-wrap-mode: nowrap;
    height: auto;
    width: auto;

    svg {
      path {
        fill: var(--color-bg-white);
      }
    }
  }

  .charcoalIconItem {
    svg {
      path {
        fill: var(--color-primary-charcoal) !important;
      }
    }
  }

  [data-scope="menu"][data-part="item"]:hover {
    background-color: var(--color-primary-pale-charcoal);
  }
}
