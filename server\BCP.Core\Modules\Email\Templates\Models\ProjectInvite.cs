namespace BCP.Core.Email.Templates;

public class ProjectInvite : TemplateModel, ITemplateModel
{
    public static string TemplateName => "ProjectInvite";
    public override EmailLayout Layout => EmailLayout.Narrow;

    public override string Subject =>
        Actor != null
            ? Translation.ProjectInvite_Title.Replace("{{name}}", Actor?.Name)
            : Translation.ProjectInvite_Title_NULL;
    public override string PreviewText =>
        Actor != null
            ? Translation.ProjectInvite_Title.Replace("{{name}}", Actor?.Name)
            : Translation.ProjectInvite_Title_NULL;
    public string Body =>
        Actor != null
            ? Translation.ProjectInvite_Body.Replace("{{name}}", Actor?.Name)
            : Translation.ProjectInvite_Body_NULL;
}
