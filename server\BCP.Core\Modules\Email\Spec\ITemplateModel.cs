using BCP.Data.Models;
using ClientModel = BCP.Data.Models.Client;
using ProjectModel = BCP.Data.Models.Project;
using UserModel = BCP.Data.Models.User;

namespace BCP.Core.Email;

public enum EmailLayout
{
    Narrow,
    Wide,
}

public class TemplateEntity
{
    public required string Id { get; set; } = string.Empty;
    public required string Name { get; set; } = string.Empty;

    public static implicit operator TemplateEntity?(ClientModel? client)
    {
        if (client == null)
            return null;
        return new TemplateEntity { Id = client.Id.ToString(), Name = client.Name };
    }

    public static implicit operator TemplateEntity?(ProjectModel? project)
    {
        if (project == null)
            return null;
        return new TemplateEntity { Id = project.Id.ToString(), Name = project.Name };
    }
}

public class TemplateUser : TemplateEntity
{
    public required string Email { get; set; } = string.Empty;
    public required Language Language { get; set; }
    public required string FirstName { get; set; }

    public static implicit operator TemplateUser(UserModel? user)
    {
        if (user == null)
        {
            return null;
        }

        return new TemplateUser
        {
            Id = user.Id.ToString(),
            Name = user.DisplayName,
            Email = user.Email,
            Language = user.Language,
            FirstName = user.FirstName ?? user.DisplayName,
        };
    }
}

public interface ITemplateModelBase
{
    IEmailTranslation Translation { get; }
    TemplateUser User { get; set; }
    string Subject { get; }
    string PreviewText { get; }
    EmailLayout Layout { get; }
    string PortalUrl { get; set; }
    string AssetUrl { get; }
    TemplateEntity? Client { get; set; }
    TemplateEntity? Project { get; set; }
}

public interface ITemplateModel : ITemplateModelBase
{
    static abstract string TemplateName { get; }
}
