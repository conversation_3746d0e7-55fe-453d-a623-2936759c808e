import React, { useEffect, useRef, useState } from "react";
import { Link, useLocation, useSearchParams } from "react-router-dom";
import { CustomTreeView, getFolderUrl } from "~/treeView";
import { Input } from "~/input";
import classNames from "classnames";
import styles from "./documentsNav.module.css";
import { DocumentsNavProps } from "./spec";
import { useTranslation } from "react-i18next";
import { Spinner } from "~/spinner";
import { Icon } from "~/icon";
import { LiveAnnouncer } from "~/liveAnnouncer";

export const DocumentsNav: React.FC<DocumentsNavProps> = ({
  id,
  items,
  projectId,
  clientId,
  dataTestId = "uikit-toast",
  role,
  ariaLabel,
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
  isLoading = false,
  updateIsRestricted,
  updateFolderItemId,
  searchResultsCount,
}) => {
  const { t } = useTranslation("documents");
  const [params, setParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setSearchTerm(params.get("search") ?? "");
  }, [params]);

  const location = useLocation();

  const lastSegment = location.pathname
    .split("/")
    .filter(Boolean)
    .pop();

  const path = params.get("path");
  const isOnDocumentsRoot =
    lastSegment === "documents" && (!path || path === "/");
  const isOnRecycleBin = lastSegment === "trash";

  const baseUrl = `/client/${clientId}/project/${projectId}/documents`;

  return (
    <div className={styles.parentDiv}>
      <div
        className={classNames(styles.inputParent, {
          [styles.parentHideHiglights]: searchTerm != "",
        })}
        data-testid={dataTestId}
        role={role}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        aria-labelledby={ariaLabelledBy}
        tabIndex={tabIndex}
      >
        <Input
          inputId={"searchBar"}
          placeholder={
            isOnRecycleBin ? t("search-in-recycle") : t("search-files")
          }
          className={styles.input}
          floatingLabelEnabled={false}
          withLeftIcon
          leftIconName={"search-icon"}
          onValueChange={value => {
            setSearchTerm(value);

            if (!value) {
              setParams(prev => {
                prev.delete("search");
                return prev;
              });
              return;
            }

            if (debounceTimeoutRef.current) {
              clearTimeout(debounceTimeoutRef.current);
            }

            debounceTimeoutRef.current = setTimeout(() => {
              setParams({ search: value });
            }, 300);
          }}
          value={searchTerm}
          ariaLabel={t("search-label", { searchTerm })}
          role="textbox"
          dataTestId="uikit-input-documentSearch"
          showClearSearchTerm={searchTerm.length !== 0}
          enableSearch
        ></Input>
        <LiveAnnouncer
          searchTerm={searchTerm}
          count={searchResultsCount ?? 0}
          isLoading={isLoading}
        />
      </div>
      <div className={styles.docNavButton}>
        <Link
          className={classNames(styles.documentsTitleLink, {
            [styles.documentsHighlight]: isOnDocumentsRoot,
          })}
          id={"document"}
          to={getFolderUrl(baseUrl, "/")}
        >
          <Icon iconName={"document-folder"} altText={"documents"} />
          {t("documents")}
        </Link>
      </div>
      <div className={styles.labelParent}>
        <span className={styles.label}>{t("folders")}</span>
      </div>
      <div className={styles.folders}>
        {!items?.length && isLoading ? (
          <div className={styles.spinnerContainer}>
            <Spinner isLoading={true} />
          </div>
        ) : (
          <CustomTreeView
            id={id}
            baseUrl={baseUrl}
            isLoading={isLoading}
            updateIsRestricted={updateIsRestricted}
            updateFolderItemId={updateFolderItemId}
            items={items}
          />
        )}
      </div>
      <div className={styles.docNavButton} id={"recycleButton"}>
        <Link
          className={classNames(styles.documentsTitleLink, {
            [styles.documentsHighlight]: isOnRecycleBin,
          })}
          id={"recycleButton"}
          to={`${baseUrl}/trash`}
        >
          <Icon iconName={"recycle-bin"} altText={"recycle bin"} />
          {t("recycle-bin")}
        </Link>
      </div>
    </div>
  );
};
