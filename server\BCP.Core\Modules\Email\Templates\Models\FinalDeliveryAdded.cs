using BCP.Core.Email.Translations;

namespace BCP.Core.Email.Templates;

public class FinalDeliveryAdded : TemplateModel, ITemplateModel
{
    public static string TemplateName => "FinalDeliveryAdded";
    public override EmailLayout Layout => EmailLayout.Narrow;
    public string? DeliverableName { get; set; } = null;
    public string? DirectoryPath { get; set; } = null;

    public override string Subject =>
        Translation
            .FinalDeliveryAdded_Subject.Replace("{{clientName}}", Client?.Name)
            .Replace("{{actorName}}", Actor?.Name);

    public string Title =>
        Translation.FinalDeliveryAdded_Title.Replace("{{actorName}}", Actor?.Name);

    public override string PreviewText =>
        Translation.FinalDeliveryAdded_Title.Replace("{{actorName}}", Actor?.Name);
}
