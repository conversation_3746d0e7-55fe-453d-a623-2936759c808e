using BCP.Core.BGP.ControlAPI.Models;
using System.Net.Http.Json;
using System.Web;
using BCP.Core.Common;
using BCP.Core.Events.Payloads;
using BCP.Core.Modules.Email.Models;
using BCP.Data.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using EmailTemplates = BCP.Core.Email.Templates;

namespace BCP.Core.Email;

public class EmailService(
    ITemplateService _templateService,
    EmailConfig _emailConfig,
    ILogger<EmailService> _logger,
    ISendGridClient sendGridClient
) : IEmailService
{
    public async Task<string?> SendEmail<T>(
        T model,
        string[]? ccRecipients = null,
        long? delayTimeInSeconds = null
    )
        where T : class, ITemplateModel
    {
        var emailHtml = await _templateService.Build(model);
        return await SendEmailAsync(
            [model.User.Email],
            model.Subject,
            emailHtml,
            ccRecipients,
            delayTimeInSeconds
        );
    }

    private async Task<string?> SendEmailAsync(
        string[] to,
        string subject,
        string htmlBody,
        string[]? ccRecipients,
        long? delayTimeInSeconds
    )
    {
        try
        {
            _logger.LogInformation($"Sending email to {string.Join(", ", to)}");

            var message = MapToSendGridMessage(
                to,
                subject,
                htmlBody,
                ccRecipients,
                delayTimeInSeconds
            );
            var response = await sendGridClient.SendEmailAsync(message);

            if (!response.IsSuccessStatusCode)
            {
                throw new InvalidOperationException(await response.Body.ReadAsStringAsync());
            }
            response.Headers.TryGetValues("X-Message-Id", out var messageIds);
            return messageIds?.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unexpected error occurred while sending email.");
            throw new InvalidOperationException("An error occurred while sending the email.", ex);
        }
    }

    private SendGridMessage MapToSendGridMessage(
        string[] targets,
        string subject,
        string htmlBody,
        string[]? ccRecipients,
        long? delayTimeInSeconds,
        IList<Attachment>? attachments = null
    )
    {
        var senderEmail = _emailConfig.Sender?.Trim();
        var senderName = _emailConfig.SenderName?.Trim();
        if (string.IsNullOrEmpty(senderEmail))
            throw new CoreException("Missing required configuration: Email.Sender.");

        var message = new SendGridMessage
        {
            HtmlContent = htmlBody,
            Subject = subject,
            From = new SendGrid.Helpers.Mail.EmailAddress(senderEmail, senderName),
            Attachments = attachments?.ToList(),
        };

        message.MailSettings = new MailSettings
        {
            SandboxMode = new SandboxMode { Enable = _emailConfig.SandBox },
        };

        message.AddTos(targets.Select(x => new SendGrid.Helpers.Mail.EmailAddress(x)).ToList());

        if (ccRecipients is not null && ccRecipients.Length is not 0)
        {
            message.AddCcs(
                ccRecipients.Select(x => new SendGrid.Helpers.Mail.EmailAddress(x)).ToList()
            );
        }

        if (delayTimeInSeconds > 0)
        {
            message.SendAt = new DateTimeOffset(
                DateTime.UtcNow.AddSeconds(delayTimeInSeconds.Value)
            ).ToUnixTimeSeconds();
        }

        return message;
    }

    public Task<FileStreamResult> GetAssetStream(string path)
    {
        Dictionary<string, string> AllowedMimeTypes = new(StringComparer.OrdinalIgnoreCase)
        {
            [".png"] = "image/png",
            [".jpg"] = "image/jpeg",
            [".gif"] = "image/gif",
        };

        if (string.IsNullOrWhiteSpace(path))
            throw new ArgumentException("Asset path is required.", nameof(path));

        var ext = Path.GetExtension(path);
        if (!AllowedMimeTypes.TryGetValue(ext, out var contentType))
            throw new InvalidOperationException("Unsupported asset type.");

        string outputDir = AppDomain.CurrentDomain.BaseDirectory;
        var fullPath = Path.Combine(outputDir, "Modules/Email/Templates/Assets", path);
        if (!File.Exists(fullPath))
            throw new CoreException(CoreError.NotFound, "Asset not found.");

        var stream = File.OpenRead(fullPath);
        return Task.FromResult(new FileStreamResult(stream, contentType));
    }

    public async Task<string> GetPreview(string name, Language language = Language.English)
    {
        var client = new TemplateEntity { Id = "cc", Name = "Acme Corp" };
        var project = new TemplateEntity { Id = "pp", Name = "Tax Planning 2024" };
        var user = new TemplateUser
        {
            Id = "js",
            Name = "John Smith",
            Email = "<EMAIL>",
            Language = language,
            FirstName = "John",
        };
        var actor = new TemplateUser
        {
            Id = "jb",
            Name = "James Brown",
            Email = "<EMAIL>",
            Language = Language.English,
            FirstName = "James",
        };

        var lastScheduledTime = DateTime.Now;
        var referenceDate = lastScheduledTime.AddDays(-7).Date;
        var startOfWeek = referenceDate;
        var endOfWeek = startOfWeek.AddDays(7);

        switch (name.ToLower())
        {
            case "portal_invite":
            {
                var model = new EmailTemplates.PortalInvite
                {
                    User = user,
                    Client = client,
                    Project = project,
                    RedemptionUrl = "",
                };
                return await _templateService.Build(model);
            }

            case "project_invite":
            {
                var model = new EmailTemplates.ProjectInvite
                {
                    User = user,
                    Client = client,
                    Project = project,
                    Actor = actor,
                };
                return await _templateService.Build(model);
            }

            case "client_admin_invite":
            {
                var model = new EmailTemplates.ClientAdminInvite { User = user, Client = client };
                return await _templateService.Build(model);
            }

            case "notification_reminder":
            {
                var model = new EmailTemplates.NotificationReminder
                {
                    User = user,
                    NotificationCount = "15",
                    StartDate = startOfWeek,
                    EndDate = endOfWeek,
                };
                return await _templateService.Build(model);
            }

            case "onboarding_completed":
            {
                var model = new EmailTemplates.OnboardingCompleted
                {
                    User = user,
                    Client = client,
                    Project = project,
                    Actor = actor,
                };
                return await _templateService.Build(model);
            }

            case "action_item_assigned":
            {
                var model = new EmailTemplates.ActionItemAssigned
                {
                    User = user,
                    Client = client,
                    Project = project,
                    Actor = actor,
                    TaskId = Guid.NewGuid(),
                    TaskName = "Sign Management Representation Letter",
                    TaskType = ActionItemType.Signature.ToString(),
                    DueDate = DateTime.Now,
                };
                return await _templateService.Build(model);
            }

            case "final_delivery_added":
            {
                var model = new EmailTemplates.FinalDeliveryAdded
                {
                    User = user,
                    Client = client,
                    Project = project,
                    Actor = actor,
                    DeliverableName = "Test Document",
                    DirectoryPath = "/",
                };
                return await _templateService.Build(model);
            }

            case "action_item_status_changed":
            {
                var model = new EmailTemplates.ActionItemStatusChanged
                {
                    User = user,
                    Client = client,
                    Project = project,
                    Actor = actor,
                    TaskId = Guid.NewGuid(),
                    TaskName = "Complete and upload T2 form",
                    TaskType = ActionItemType.RequestItem.ToString(),
                    Status = ActionItemStatuses.InReview,
                };
                return await _templateService.Build(model);
            }

            default:
                throw new CoreException(CoreError.NotFound);
        }
    }

    public async Task<MessageStatus> GetDeliveryStatus(string messageId)
    {
        var filter = HttpUtility.UrlEncode($"limit=1&msg_id LIKE '{messageId}'");
        var response = await sendGridClient.RequestAsync(
            BaseClient.Method.GET,
            null,
            null,
            $"messages?{filter}"
        );
        if (!response.IsSuccessStatusCode)
        {
            return MessageStatus.Error;
        }
        var messageResponse = await response.Body.ReadFromJsonAsync<MessageStatusResponse>();
        if (messageResponse?.Messages?.Count == 0)
        {
            return MessageStatus.NotFound;
        }
        return messageResponse.Messages[0].Status switch
        {
            "processed" => MessageStatus.Processed,
            "delivered" => MessageStatus.Delivered,
            "not_delivered" => MessageStatus.NotDelivered,
            _ => MessageStatus.Error,
        };
    }

    public async Task SendEmailAsync(
        string[] to,
        string subject,
        string htmlBody,
        string[]? ccRecipients,
        long? delayTimeInSeconds,
        IList<Attachment>? attachments = null
    )
    {
        try
        {
            _logger.LogInformation($"Sending email to {string.Join(", ", to)}");

            var message = MapToSendGridMessage(
                to,
                subject,
                htmlBody,
                ccRecipients,
                delayTimeInSeconds,
                attachments
            );
            var response = await sendGridClient.SendEmailAsync(message);

            if (!response.IsSuccessStatusCode)
            {
                throw new InvalidOperationException(await response.Body.ReadAsStringAsync());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unexpected error occurred while sending email.");
            throw new InvalidOperationException("An error occurred while sending the email.", ex);
        }
        await Task.CompletedTask;
    }
}
