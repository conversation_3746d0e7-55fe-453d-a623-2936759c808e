using BCP.Data.Models;

public enum UserRole
{
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>er,
    BDOClientAdmin,
    BDOSuperAdmin,
};

public class TeamMemberUser : User
{
    public UserRole UserType { get; set; }

    public Notification Notification { get; set; }

    public TeamMemberUser(User user, Notification notification)
    {
        foreach (var prop in user.GetType().GetProperties())
        {
            var childProp = this.GetType().GetProperty(prop.Name);
            if (childProp != null && childProp.CanWrite)
            {
                childProp.SetValue(this, prop.GetValue(user));
            }
        }

        Notification = notification;
    }
}
