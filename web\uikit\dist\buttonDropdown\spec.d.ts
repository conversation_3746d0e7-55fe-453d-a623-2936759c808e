import { ReactNode } from "react";
import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
import { IconColor } from "~/selectionCard";
export interface ButtonDropdownProps extends defaultProps {
    id: string;
    triggerText?: string;
    triggerIcon?: IconName;
    items: ButtonDropdonItem[];
    defaultOpen?: boolean;
    closeOnSelect?: boolean;
    withTaskType?: boolean;
    iconColor?: "charcoal" | "white";
    buttonType?: "primary" | "tertiary";
}
export interface ButtonDropdonItem extends defaultProps {
    id: string;
    onClick: () => void;
    iconName: IconName;
    label: string;
    value: string;
    withIcon?: boolean;
    children?: ReactNode;
    backgroundColor?: IconColor;
    width?: number | string;
    height?: number | string;
}
