import { defaultProps } from "~/default.spec";

export interface UserSelectProps extends defaultProps {
  id?: string;
  selected: DropdownUser | null;
  onChange: (user: DropdownUser | null) => void;
  users: DropdownUser[];
  label: string;
  placeholder?: string;
  hideLabel?: boolean;
  ariaDescribedBy?: string;
  ariaLabel?: string;
}

export interface DropdownUser {
  id: string;
  bgpId: string;
  displayName: string;
  email: string;
  avatarColor?: string;
}
