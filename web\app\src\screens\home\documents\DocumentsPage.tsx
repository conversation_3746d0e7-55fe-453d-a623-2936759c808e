import { DocumentsNav, Splitter } from "@bcp/uikit";
import { Outlet, useSearchParams } from "react-router-dom";
import { transformDocumentData } from "./utils";
import { useDocumentService } from "~/services/document";
import { useEffect, useState } from "react";
import styles from "./document.module.css";
import { useProjectService } from "~/services/project";
import { useClientService } from "~/services/client";
import { useSettingsService } from "~/services/settings";
import { DocumentItem } from "./spec";

export const DocumentsPage: React.FC = () => {
  const {
    nodes,
    fetchFolderContent,
    getFolderContentByPath,
    setIsLoading,
  } = useDocumentService();
  const { memberFirmId } = useSettingsService();
  const [params] = useSearchParams();
  const { activeProject } = useProjectService();
  const { activeClient } = useClientService();
  const [hasInitialData, setHasInitialData] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [navData, setNavData] = useState<DocumentItem[]>([]);
  const [reloadTrigger, setReloadTrigger] = useState(0);
  const { updateIsRestrictedFolder, updateFolderItemId } = useDocumentService();

  const folderPath = params.get("path") ?? "/";
  const searchTerm = params.get("search") ?? undefined;

  const addDescendantsToTree = (
    tree: DocumentItem[],
    parentPath: string,
    newData: DocumentItem[],
    currentPath = ""
  ): DocumentItem[] => {
    if (!tree.length) return newData;

    parentPath = parentPath.replace(/\/+\$/, "");

    if ((parentPath === "" || parentPath === "/") && currentPath === "") {
      const existingIds = new Set(tree.map(item => item.id));
      return [
        ...tree.map(item => {
          const updated = newData.find(n => n.id === item.id);
          return updated ?? item;
        }),
        ...newData.filter(n => !existingIds.has(n.id)),
      ];
    }

    return tree.map(item => {
      const thisPath = `${currentPath}/${item.name}`.replace(/\/+/g, "/");
      const updated = newData.find(n => n.id === item.id);

      if (thisPath === parentPath) {
        // this is being used like a hook for the current folder changed - these checks should move elsewhere
        updateIsRestrictedFolder(item.restricted);
        updateFolderItemId(item.listItemId ?? 0);

        return {
          ...(updated ?? item),
          descendants: newData,
        };
      } else if (item.descendants?.length) {
        return {
          ...(updated ?? item),
          descendants: addDescendantsToTree(
            item.descendants,
            parentPath,
            newData,
            thisPath
          ),
        };
      }

      return updated ?? item;
    });
  };

  useEffect(() => {
    const fetchNavData = async () => {
      if (hasInitialData || !activeClient?.bgpId || !activeProject?.bgpId) {
        return;
      }

      const pathSegments = folderPath.split("/").filter(Boolean);

      const pathsToFetch: string[] = ["/"];
      pathSegments.forEach((_, index) => {
        const path = "/" + pathSegments.slice(0, index + 1).join("/");
        if (path !== "/") {
          pathsToFetch.push(path);
        }
      });

      try {
        setIsFetching(true);
        let items: DocumentItem[] = [];

        for (const path of pathsToFetch) {
          const response = await getFolderContentByPath(
            memberFirmId,
            activeClient.bgpId,
            path,
            activeProject.bgpId
          );

          items = addDescendantsToTree(
            items,
            path,
            transformDocumentData(response)
          );
        }

        setNavData(items);
        setHasInitialData(true);
      } catch (error) {
        console.error(error);
      } finally {
        setIsFetching(false);
      }
    };

    fetchNavData();
  }, [params, activeProject, activeClient]);

  useEffect(() => {
    if (activeClient?.bgpId && activeProject) {
      const controller = new AbortController();
      const signal = controller.signal;

      const fetchDocuments = async () => {
        if (!activeClient.bgpId) return;

        try {
          await fetchFolderContent(
            memberFirmId,
            activeClient.bgpId,
            folderPath,
            activeProject.bgpId,
            searchTerm,
            signal
          );

          if (!signal.aborted) {
            setIsLoading(true);
          }
        } catch (error) {
          if (error instanceof DOMException && error.name === "AbortError") {
            console.log("Fetch sequence aborted.");
          } else {
            console.error(error);
          }
        }
      };

      fetchDocuments();

      return () => {
        controller.abort();
        setIsLoading(false);
      };
    }
  }, [params, activeProject, activeClient, reloadTrigger]);

  useEffect(() => {
    if (
      hasInitialData &&
      !nodes?.isBusy &&
      nodes?.data &&
      activeProject?.id &&
      activeClient?.id
    ) {
      const transformedData = transformDocumentData(nodes.data);

      setNavData(prevData =>
        addDescendantsToTree(prevData, folderPath, transformedData)
      );
    }
  }, [nodes, activeProject, activeClient]);

  const reloadDocuments = async () => {
    setReloadTrigger(prev => prev + 1);
  };

  return (
    <div
      className={styles.container}
      id="documents-panel"
      role="tabpanel"
      aria-labelledby="nav-tab-documents"
    >
      {/* Splitter for navigation and document view */}
      {/* Adjust values here for splitter defaults */}
      <Splitter
        leftPanel={
          activeProject?.id &&
          activeClient?.id && (
            <DocumentsNav
              id={`${activeProject?.name}-documents`}
              items={navData}
              projectId={activeProject.id}
              clientId={activeClient.id}
              isLoading={isFetching}
              updateIsRestricted={updateIsRestrictedFolder}
              updateFolderItemId={updateFolderItemId}
              searchResultsCount={
                !!searchTerm && !isFetching ? nodes?.data?.length : 0
              }
            />
          )
        }
        rightPanel={
          <Outlet context={{ reloadDocuments, isSearchActive: !!searchTerm }} />
        }
        defaultSizes={[20, 80]}
        maxLimits={[40, 90]}
        dataTestId="document-page-splitter"
      />
    </div>
  );
};
