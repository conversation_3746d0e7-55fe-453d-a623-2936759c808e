import React from "react";
import { ButtonDropdownProps } from "./spec";
import { Icon } from "~/icon";
import { Menu } from "@ark-ui/react";
import styles from "./buttonDropdown.module.css";
import { TaskType } from "~/taskType";
import classNames from "classnames";

const DEFAULT_TRIGGER_TEXT = "New";

export const ButtonDropdown: React.FC<ButtonDropdownProps> = ({
  id,
  triggerText,
  triggerIcon,
  items,
  iconColor = "charcoal",
  defaultOpen = false,
  closeOnSelect = true,
  dataTestId = "uikit-buttonDropdown",
  ariaLabel = "Button Dropdown",
  ariaDescribedBy,
  ariaLabelledBy,
  withTaskType = false,
  buttonType = "tertiary",
}) => {
  return (
    <div className={styles.buttonDropdownWrapper}>
      <Menu.Root
        id={id}
        defaultOpen={defaultOpen}
        closeOnSelect={closeOnSelect}
        aria-label={ariaLabel}
        data-testid={dataTestId}
        aria-describedby={ariaDescribedBy}
        aria-labelledby={ariaLabelledBy}
      >
        <Menu.Trigger
          className={classNames(styles.defaultTrigger, {
            [styles.primaryTrigger]: buttonType === "primary",
            [styles.tertiaryTrigger]: buttonType === "tertiary",
          })}
        >
          {triggerText || DEFAULT_TRIGGER_TEXT}
          {triggerIcon && (
            <Icon iconName={triggerIcon} altText={triggerIcon} size={12} />
          )}
        </Menu.Trigger>
        <Menu.Content hidden={false}>
          {items.map((item, i) => {
            const { withIcon = true } = item;
            return (
              <Menu.Item
                value={item.value}
                className={classNames(styles.buttonDropdownItem, {
                  [styles.charcoalIconItem]: iconColor === "charcoal",
                })}
                key={i}
                aria-label={item.label}
                onClick={() => {
                  item.onClick();
                }}
              >
                {withIcon && !withTaskType && (
                  <Icon iconName={item.iconName} altText={item.iconName} />
                )}
                {withTaskType && (
                  <TaskType
                    iconName={item.iconName}
                    backgroundColor={item.backgroundColor!}
                    width={item.width}
                    height={item.height}
                  />
                )}
                {item.children && item.children}
                {item.label}
              </Menu.Item>
            );
          })}
        </Menu.Content>
      </Menu.Root>
    </div>
  );
};
