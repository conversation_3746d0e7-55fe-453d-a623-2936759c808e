using BCP.Core.BGP.FirmAPI;
using BCP.Core.Common;
using BCP.Core.Events;
using BCP.Core.Project;
using BCP.Data;
using BCP.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EventModel = BCP.Data.Models.Event;
using EventPayloads = BCP.Core.Events.Payloads;
using NotificationPayloads = BCP.Core.Notifications.Payloads;

namespace BCP.Core.Notifications;

public class NotificationEventHandler : IEventHandler
{
    public string Name => "notification";
    public DateTime ProcessFrom => new DateTime(2025, 6, 27, 14, 50, 00);
    private readonly ILogger<NotificationEventHandler> _logger;
    private readonly IFirmAPIService _firmApiService;
    private readonly DataContext _context;

    public NotificationEventHandler(
        ILogger<NotificationEventHandler> logger,
        DataContext context,
        IFirmAPIService firmApiService
    )
    {
        this._logger = logger;
        this._context = context;
        this._firmApiService = firmApiService;
    }

    public async Task Process(EventModel ev)
    {
        _logger.LogInformation($"Processing event: {ev.Id} - {ev.Name}");
        switch (ev.Name)
        {
            case EventName.OnboardingCompleted:
                await HandleOnboardingCompleted(ev);
                break;
            case EventName.ProjectChanged:
                await HandleProjectChanged(ev);
                break;
            case EventName.ClientChanged:
                await HandleClientChanged(ev);
                break;
            case EventName.ActionItemChanged:
                await HandleActionItemChanged(ev);
                break;
            case EventName.DocumentAdded:
                await HandleDocumentAdded(ev);
                break;
            default:
                _logger.LogInformation(
                    $"Acknowledging event: {ev.Id} - {ev.Name}, nothing to process"
                );
                break;
        }
    }

    private async Task HandleOnboardingCompleted(Event ev)
    {
        if (ev.UserId == null)
            throw new CoreException("Onboarding Completed requires a event user");
        var ePayload = ev.GetPayload<EventPayloads.OnboardingCompleted>();
        if (ePayload == null)
            throw new CoreException("OnboardingCompleted event data not found");
        var payload = new NotificationPayloads.OnboardingCompleted
        {
            UserId = ePayload.UserId,
            ActorId = ev.UserId,
        };
        await this.AddNotification(payload, ev);
    }

    private async Task HandleProjectChanged(Event ev)
    {
        var ePayload = ev.GetPayload<EventPayloads.ProjectChanged>();
        if (ePayload == null)
            throw new CoreException("ProjectChanged event data not found");

        var client = await _context
            .Clients.Where(x => x.Id == ePayload.ClientId)
            .FirstOrDefaultAsync();
        var project = await _context
            .Projects.Where(x => x.Id == ePayload.ProjectId)
            .FirstOrDefaultAsync();

        if (client?.BGPId == null || project?.BGPId == null)
            throw new CoreException("Invalid Client or Project");

        if (ePayload.AddedUsers != null)
        {
            var newProjectUsers = await _context
                .Users.Where(x => ePayload.AddedUsers.Contains(x.Id))
                .ToListAsync();
            //Fetch the redemption URL here - CTRL we use /GetPortalUsers or /GetProjectUsers

            var bgpProjectUsers = await _firmApiService.GetProjectUsers(
                (int)client.BGPId,
                (int)project.BGPId
            );

            // Already onboarded users will have an empty redemption URL
            var redemptionUrls = bgpProjectUsers
                .Where(u => !string.IsNullOrWhiteSpace(u.RedemptionUrl))
                .ToDictionary(u => u.UniqueUserId.ToString(), u => u.RedemptionUrl);

            // New portal users, haven't been onboarded to AD, hence they have a redemption URL.
            var newPortalUsers = newProjectUsers
                .Where(x => x.CompletedOnboardingAt == null)
                .Where(u => redemptionUrls.ContainsKey(u.BgpId.ToString()))
                .ToList();

            // Portal Invite Notification.
            var portalPayloads = newPortalUsers
                .Select(x => new NotificationPayloads.PortalInvite
                {
                    ClientId = ePayload.ClientId,
                    ProjectId = ePayload.ProjectId,
                    ActorId = ev.UserId,
                    UserId = x.Id,
                    RedemptionUrl = redemptionUrls[x.BgpId.ToString()],
                })
                .ToList();
            await AddNotification(portalPayloads, ev);

            // Project Invite Notification.
            var projectPayloads = newProjectUsers
                .Where(x => !newPortalUsers.Any(y => y.Id == x.Id)) // Don't invite those who received a Portal Invite
                .Where(u =>
                    // Don't invite Client Admins
                    bgpProjectUsers
                        .Find(bu => bu.UniqueUserId == u.BgpId.ToString())
                        ?.UserGroupName != "BDO Admin User"
                )
                .Select(x => new NotificationPayloads.ProjectInvite
                {
                    ClientId = ePayload.ClientId,
                    ProjectId = ePayload.ProjectId,
                    ActorId = ev.UserId,
                    UserId = x.Id, //redemption url not needed in this invite
                })
                .ToList();
            await AddNotification(projectPayloads, ev);
        }
        if (ePayload.ReinvitedUsers != null)
        {
            // Reinvite
            foreach (var userId in ePayload.ReinvitedUsers)
            {
                var user = await _context.Users.FirstOrDefaultAsync(x => x.BgpId == userId);
                if (user == null)
                {
                    continue;
                }
                await _context
                    .Notifications.Where(x => x.UserId == user.Id)
                    .Where(x => x.NotificationType == NotificationType.PortalInvite)
                    .ExecuteUpdateAsync(x =>
                        x.SetProperty(y => y.EmailStatus, NotificationEmailStatus.Queued)
                            .SetProperty(y => y.EmailMessageId, (string?)null)
                            .SetProperty(y => y.EmailProcessedAt, (DateTime?)null)
                    );
            }
        }

        // Project Health Status Change.
        if (ePayload.HealthStatus != null)
        {
            var projectFirmUsers =
                await _firmApiService.GetProjectUsersAsBcpUsers(
                    (int)client.BGPId,
                    (int)project.BGPId
                ) ?? [];

            var bgpIds = projectFirmUsers?.Select(u => u.BgpId).ToList() ?? [];

            List<Data.Models.User> projectUsers = await _context
                .Users.Where(u => bgpIds.Contains(u.BgpId))
                .ToListAsync();

            var payloads = projectUsers
                .Select(x => new NotificationPayloads.ProjectHealthStatusChanged
                {
                    ClientId = ePayload.ClientId,
                    ProjectId = ePayload.ProjectId,
                    Status = (ProjectStatus)ePayload.HealthStatus,
                    ActorId = ev.UserId,
                    UserId = x.Id,
                })
                .ToList();
            await AddNotification(payloads, ev);
        }
    }

    private async Task HandleClientChanged(Event ev)
    {
        var ePayload = ev.GetPayload<EventPayloads.ClientChanged>();

        if (ePayload == null)
            throw new CoreException("Missing ClientChanged event data");

        var client = await _context
            .Clients.Where(x => x.Id == ePayload.ClientId)
            .FirstOrDefaultAsync();

        if (client?.BGPId == null)
            throw new CoreException("Invalid Client");

        if (ePayload.UpdatedUsers?.Count() > 0)
        {
            var updatedUsers = await _context
                .Users.Where(u => ePayload.UpdatedUsers.Contains(u.BgpId))
                .ToListAsync();

            // Check that the user is a client admin in BGP
            var clientAdminUsers = await _firmApiService.GetClientAdminUsersAsBcpUsers(
                (int)client.BGPId
            );
            var updatedClientAdminUsers = updatedUsers.Where(
                (x) => clientAdminUsers.Any((y) => x.BgpId == y.BgpId)
            );

            var payloads = updatedClientAdminUsers
                .Select(u => new NotificationPayloads.ClientAdminInvite
                {
                    UserId = u.Id,
                    ClientId = ePayload.ClientId,
                })
                .ToList();
            await AddNotification(payloads, ev);
        }
    }

    private async Task HandleActionItemChanged(Event ev)
    {
        var ePayload = ev.GetPayload<EventPayloads.ActionItemChanged>();

        if (ePayload?.ClientId == null || ePayload?.ProjectId == null || ePayload?.TaskId == null)
        {
            throw new CoreException(
                $"Missing required parameters, (ClientId, ProjectId, TaskId) from payload. Event: ${ev.Id}"
            );
        }

        var client = await _context
            .Clients.Where(x => x.Id == ePayload.ClientId)
            .FirstOrDefaultAsync();
        var project = await _context
            .Projects.Where(x => x.Id == ePayload.ProjectId)
            .FirstOrDefaultAsync();

        if (client?.BGPId == null || project?.BGPId == null)
            throw new CoreException("Invalid Client or Project");

        var task = await _firmApiService.GetTask(
            (int)client.BGPId,
            (int)project.BGPId,
            ePayload.TaskId
        );
        if (task == null)
            throw new CoreException($"Invalid Task {ePayload.TaskId}");

        var taskUsersEmails = task.Assignees.Select(x => x.User.EmailAddress);
        // Include owner in taskusers besides asignees.
        if (task.Owner != null)
            taskUsersEmails = [task.Owner.EmailAddress, .. taskUsersEmails];

        var taskUsers = await _context
            .Users.Where(x => taskUsersEmails.Contains(x.Email))
            .ToListAsync();

        // Action Item Assigned notification
        if (ePayload?.Users?.After != null)
        {
            var before = ePayload?.Users?.Before ?? [];
            var after = ePayload?.Users?.After ?? [];
            var newUserIds = after.Where(x => !before.Contains(x)).ToArray();

            var NewUsers = await _context.Users.Where(x => newUserIds.Contains(x.Id)).ToListAsync();

            var payloads = NewUsers
                .Select(x => new Payloads.ActionItemAssigned
                {
                    ClientId = ePayload!.ClientId,
                    ProjectId = ePayload.ProjectId,
                    ActorId = ev.UserId,
                    UserId = x.Id,
                    TaskId = task.Id,
                    TaskName = task.Name,
                    TaskType = task.Type.ToString(),
                    DueDate = task.DueDate,
                })
                .ToList();
            await AddNotification(payloads, ev);
        }

        // Action Item status changed notification.
        if (ePayload?.Status?.After != null && ePayload.Status?.Before != ePayload.Status?.After)
        {
            var payloads = taskUsers
                .Select(x => new Payloads.ActionItemStatusChanged
                {
                    ClientId = ePayload.ClientId,
                    ProjectId = ePayload.ProjectId,
                    ActorId = ev.UserId,
                    UserId = x.Id,
                    TaskId = task.Id,
                    TaskName = task.Name,
                    TaskType = task.Type.ToString(),
                    Status = ePayload.Status!.After,
                })
                .ToList();
            await AddNotification(payloads, ev);
        }

        // Comment Added
        if (ePayload?.Comments?.After?.Length > 0)
        {
            var payloads = taskUsers
                .Select(x => new Payloads.ActionItemCommentAdded
                {
                    ClientId = ePayload.ClientId,
                    ProjectId = ePayload.ProjectId,
                    ActorId = ev.UserId,
                    UserId = x.Id,
                    TaskId = task.Id,
                    TaskName = task.Name,
                })
                .ToList();
            await AddNotification(payloads, ev);
        }
    }

    private async Task HandleDocumentAdded(Event ev)
    {
        var ePayload =
            ev.GetPayload<EventPayloads.DocumentAdded>()
            ?? throw new CoreException("DocumentAdded event data not found");

        var client = await _context
            .Clients.Where(x => x.Id == ePayload.ClientId)
            .FirstOrDefaultAsync();
        var project = await _context
            .Projects.Where(x => x.Id == ePayload.ProjectId)
            .FirstOrDefaultAsync();

        if (client?.BGPId == null || project?.BGPId == null)
            throw new CoreException("Invalid Client or Project");

        var projectUsers =
            (await _firmApiService.GetProjectUsersAsBcpUsers((int)client.BGPId, (int)project.BGPId)) //TODO: Map BGP to BCP in _firmProjectService
            ?? [];

        List<Guid> userBGPIds = projectUsers?.Select(u => u.BgpId).ToList() ?? [];

        List<Data.Models.User> bcpUsers = await _context
            .Users.Where(u => userBGPIds.Contains(u.BgpId))
            .ToListAsync();

        var bcpUserIdsMap = bcpUsers.ToDictionary(b => b.BgpId, b => b.Id);

        if (projectUsers == null || projectUsers.Count == 0)
        {
            _logger.LogInformation(
                $"No project users found for projectId {project.Id}, while processing event {ev.Id}"
            );
            return;
        }

        var notifyOnlyUsers = ePayload.NotificationRecipientsOnly ?? [];

        var payloads = projectUsers
            .Where(x => bcpUserIdsMap.ContainsKey(x.BgpId))
            .Select(x => new NotificationPayloads.FinalDeliveryAdded
            {
                ClientId = ePayload.ClientId,
                ProjectId = ePayload.ProjectId,
                DocumentName = ePayload.DocumentName,
                DocumentId = ePayload.DocumentId,
                Path = ePayload.Path,
                ActorId = ev.UserId,
                UserId = bcpUserIdsMap[x.BgpId],
            })
            .ToList();

        if (notifyOnlyUsers.Count != 0)
        {
            // If notifyOnlyUsers contains any users, filter payloads so that only those users will receive the notification
            payloads = [.. payloads.Where(p => notifyOnlyUsers.Contains(p.UserId))];
        }

        await AddNotification(payloads, ev);
    }

    public async Task AddNotification<T>(T payload, EventModel ev)
        where T : class, INotificationPayload
    {
        var payloads = new T[] { payload };
        await AddNotification(payloads, ev);
    }

    private async Task AddNotification<T>(IEnumerable<T> payloads, EventModel ev)
        where T : class, INotificationPayload
    {
        if (payloads.Count() == 0)
            return;
        var notifications = payloads
            .Where(x => x != null)
            .Select(x => new Notification
            {
                NotificationType = T.NotificationType,
                Payload = JsonUtils.Serialize(x), // the redemptionUrl will be in this payload
                UserId = x.UserId,
                ClientId = x.ClientId,
                ProjectId = x.ProjectId,
                ActorId = x.ActorId,
                EmailStatus = GetEmailStatus(T.NotificationType),
                HappenedAt = ev.CreatedAt,
            });
        await _context.AddRangeAsync(notifications);
        await _context.SaveChangesAsync();
    }

    private static NotificationEmailStatus GetEmailStatus(NotificationType notificationType)
    {
        var supportedTypes = new NotificationType[]
        {
            NotificationType.PortalInvite,
            NotificationType.ProjectInvite,
            NotificationType.ClientAdminInvite,
            NotificationType.OnboardingCompleted,
            NotificationType.ActionItemAssigned,
            NotificationType.ActionItemStatusChanged,
            NotificationType.FinalDeliveryAdded,
        };
        if (supportedTypes.Contains(notificationType))
            return NotificationEmailStatus.Queued;
        return NotificationEmailStatus.NotApplicable;
    }
}
