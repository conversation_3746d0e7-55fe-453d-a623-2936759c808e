import React, { useEffect, useState } from "react";
import {
  Chip,
  ChipType,
  Spinner,
  Icon,
  Modal,
  ModalSize,
  ButtonTypeEnum,
  ButtonSizeEnum,
  FooterButtonAlignment,
  CustomTooltip,
} from "@bcp/uikit";
import classNames from "classnames";
import {
  getLastFolderName,
  extractPathAfterDocuments,
  fileType,
  normalizeActionItemFileType,
  isReadonlyFolder,
  canCreateSubfolder,
  getPathFromNode,
  hasReadonly,
  getLocalizedFolderKeyName,
} from "../utils";
import { useTranslation } from "react-i18next";
import { useRoleService } from "~/services/role";
import { DocumentRow, iconNameMapping } from "../spec";
import { INode, useDocumentService } from "~/services/document";
import EmptyState from "../emptyState/EmptyState";
import InfoBannerWrapper from "../info-banner/InfoBannerWrapper";
import CreateNewDocument from "../createNewDocument/CreateNewDocument";
import documentTreeStyles from "../../../practitioner/shared/documentTreeModal.module.css";

type BulkMoveModalProps = {
  selectedFoldersToMoveDocument: INode[];
  showBulkMoveDocumentToFolderModal: boolean;
  selectedFilesAndFolders: DocumentRow[];
  selectedFolderToMoveDocument: INode | null;
  isAddingNewFolder: boolean;
  shouldShowAddFolderButton: boolean;
  setSelectedFolderToMoveDocument: (node: INode | null) => void;
  setSelectedFoldersToMoveDocument: React.Dispatch<
    React.SetStateAction<INode[]>
  >;
  setModalActionLoading: React.Dispatch<React.SetStateAction<boolean>>;
  setIsAddingNewFolder: (adding: boolean) => void;
  fetchSubFolders: (node: INode) => void;
  fetchMoveDocumentFolders: (path: string) => void;
  resetModalState: () => void;
  handleBulkMoveDocuments: () => void;
  modalActionLoading: boolean;
};

export const BulkMoveModal: React.FC<BulkMoveModalProps> = ({
  selectedFoldersToMoveDocument,
  showBulkMoveDocumentToFolderModal,
  selectedFilesAndFolders,
  selectedFolderToMoveDocument,
  isAddingNewFolder,
  shouldShowAddFolderButton,
  setSelectedFolderToMoveDocument,
  setSelectedFoldersToMoveDocument,
  setModalActionLoading,
  setIsAddingNewFolder,
  fetchSubFolders,
  fetchMoveDocumentFolders,
  resetModalState,
  handleBulkMoveDocuments,
  modalActionLoading,
}) => {
  const { t } = useTranslation("documents");
  const { t: tGlobal } = useTranslation("global");
  const { isClient, isAdmin } = useRoleService();
  const { moveNodes } = useDocumentService();

  // Indicates whether the user has reached the maximum allowed folder nesting level for creating subfolders.
  // This prevents further subfolder creation if the depth limit is reached.
  const [isAtMaxAllowedFolderDepth, setIsAtMaxAllowedFolderDepth] = useState<
    boolean
  >(false);

  useEffect(() => {
    if (!selectedFolderToMoveDocument) {
      return;
    }
    setIsAtMaxAllowedFolderDepth(
      !canCreateSubfolder(getPathFromNode(selectedFolderToMoveDocument))
    );
  }, [selectedFoldersToMoveDocument]);

  const uniqueMoveLocationDocuments = [
    ...new Map(
      selectedFilesAndFolders.map(doc => [
        getLastFolderName(doc.originalLocation || ""),
        doc,
      ])
    ).values(),
  ];

  const hasFolderInUniqueMoveLocation = selectedFilesAndFolders.some(
    item => item.folderOrFile === "Folder"
  );

  const shouldShowReadOnlyBanner =
    !!selectedFolderToMoveDocument &&
    isReadonlyFolder(selectedFolderToMoveDocument);

  const shouldShowMovingFromReadOnlyBanner =
    !!selectedFolderToMoveDocument &&
    hasReadonly(selectedFilesAndFolders) &&
    !selectedFolderToMoveDocument.restricted &&
    isAdmin;

  const shouldShowNotRestrictedMovingFromReadOnlyBanner =
    !selectedFolderToMoveDocument &&
    hasReadonly(selectedFilesAndFolders) &&
    isAdmin;

  const shouldShowEmptyState =
    !isAddingNewFolder &&
    !moveNodes?.isBusy &&
    !(moveNodes?.data && moveNodes.data.length > 0);

  const readOnlyFolderInfoBanner = (
    <InfoBannerWrapper
      text={t("move-to-read-only-title")}
      message={t("move-to-read-only-message")}
    />
  );

  const movingFromReadOnlyFolderInfoBanner = (
    <InfoBannerWrapper
      text={t("move-read-only-title")}
      message={t("move-read-only-message")}
    />
  );

  const restrictedFolderInfoBanner = (
    <InfoBannerWrapper
      text={t("move-folder-is-restricted-title")}
      message={t("move-folder-is-restricted-message")}
    />
  );

  const renderMoveFolders = (nodesToRender: INode[] = []) => {
    return nodesToRender.map((node, i) => {
      if (node.fileKind === "Folder") {
        const showTooltip = isReadonlyFolder(node);
        const lastFolderName = getLastFolderName(node.url || "");
        const isCurrent = selectedFilesAndFolders.some(doc => {
          const parentFolderName = getLastFolderName(
            doc.originalLocation || ""
          );
          return parentFolderName === lastFolderName;
        });

        const folderRow = (
          <button
            key={`${i}-move-folder`}
            className={documentTreeStyles.documentListItem}
            onClick={() => {
              const path = extractPathAfterDocuments(node.parentUrl);
              setSelectedFolderToMoveDocument(node);
              setSelectedFoldersToMoveDocument(prev =>
                selectedFolderToMoveDocument ? [...prev, node] : [node]
              );
              fetchMoveDocumentFolders(`${path}/${node.name}`);
            }}
            disabled={isReadonlyFolder(node)}
          >
            <div className={documentTreeStyles.folderTitleContainer}>
              <Icon iconName="folder-icon" />
              <span
                className={
                  isReadonlyFolder(node)
                    ? documentTreeStyles.documentListItemDisabled
                    : ""
                }
              >
                {node.displayName}
              </span>
            </div>
            <div className={documentTreeStyles.folderInformationContainer}>
              {node.restricted && (
                <Chip
                  text={t("restricted")}
                  iconName="prohibited-icon"
                  type={ChipType.READONLY}
                  iconHeight={12}
                  iconWidth={12}
                />
              )}
              {showTooltip && (
                <Chip
                  text={t("read-only")}
                  iconName="read-only"
                  type={ChipType.READONLY}
                  iconHeight={8}
                  iconWidth={11.99}
                />
              )}
              {isCurrent && (
                <span className={documentTreeStyles.currentFolderBadge}>
                  {t("current")}
                </span>
              )}
              <Icon iconName="chevron-right" />
            </div>
          </button>
        );
        return showTooltip ? (
          <CustomTooltip
            key={`tooltip-${node.listItemId}`}
            message={t("read-only-tooltip")}
            inputId={`read-only-${node.listItemId}`}
          >
            {folderRow}
          </CustomTooltip>
        ) : (
          folderRow
        );
      }
      const normalizedFileType = normalizeActionItemFileType(fileType(node));
      const iconName = iconNameMapping[normalizedFileType];
      return (
        <div
          className={classNames(
            documentTreeStyles.documentListItem,
            documentTreeStyles.fileLabelContainer
          )}
        >
          <Icon iconName={iconName} />
          {node.name}
        </div>
      );
    });
  };

  return (
    <Modal
      id="bulk-move-modal"
      title={t("move-multiple", { count: selectedFilesAndFolders.length })}
      isVisible={showBulkMoveDocumentToFolderModal}
      hide={resetModalState}
      isScrollable
      allowOverflow={false}
      size={ModalSize.MEDIUM}
      primaryBtnConfig={{
        label: t("move-cta"),
        type: ButtonTypeEnum.primary,
        onClick: async () => {
          setModalActionLoading(true);
          handleBulkMoveDocuments();
        },
        withRightIcon: false,
        size: ButtonSizeEnum.large,
        withLeftIcon: false,
        disabled:
          moveNodes?.isBusy ||
          (isClient &&
            selectedFolderToMoveDocument &&
            isReadonlyFolder(selectedFolderToMoveDocument)) ||
          isAddingNewFolder ||
          (isAtMaxAllowedFolderDepth && hasFolderInUniqueMoveLocation) ||
          modalActionLoading,
        loading: modalActionLoading,
      }}
      secondaryBtnConfig={{
        id: "secondary-btn",
        label: tGlobal("cancel"),
        type: ButtonTypeEnum.tertiary,
        onClick: resetModalState,
        size: ButtonSizeEnum.large,
        withRightIcon: false,
        withLeftIcon: false,
      }}
      includeHeaderBorder={true}
      footerBtnAlignment={FooterButtonAlignment.END}
      addFooterButton={shouldShowAddFolderButton}
      addFooterButtonMethod={() => setIsAddingNewFolder(true)}
      customHeaderContent={
        !selectedFolderToMoveDocument ? (
          <div className={documentTreeStyles.headerContainer}>
            <div className={documentTreeStyles.selectedFolderChipContainer}>
              {t("current-folder")}: {selectedFolderToMoveDocument}
              {uniqueMoveLocationDocuments.map((document, i) => {
                const lastFolderName = document.originalLocation
                  ? getLastFolderName(document.originalLocation) || ""
                  : "";
                const selectedMoveDocumentTranslationKey = lastFolderName
                  ? getLocalizedFolderKeyName(lastFolderName)
                  : "";

                return (
                  <Chip
                    iconName="folder-icon"
                    text={
                      t(selectedMoveDocumentTranslationKey, lastFolderName) ||
                      ""
                    }
                    type={ChipType.LOCATION}
                    key={`original-folder-Location-${i}`}
                  />
                );
              })}
            </div>
          </div>
        ) : (
          <div className={documentTreeStyles.headerContainer}>
            <div className={documentTreeStyles.selectedFolderChipContainer}>
              {t("current-folder")}
              <div className={documentTreeStyles.currentFolderLocation}>
                {uniqueMoveLocationDocuments.map((document, i) => {
                  const lastFolderName = document.originalLocation
                    ? getLastFolderName(document.originalLocation) || ""
                    : "";
                  const selectedMoveDocumentTranslationKey = lastFolderName
                    ? getLocalizedFolderKeyName(lastFolderName)
                    : "";

                  return (
                    <Chip
                      iconName="folder-icon"
                      text={
                        t(selectedMoveDocumentTranslationKey, lastFolderName) ||
                        ""
                      }
                      type={ChipType.LOCATION}
                      key={`original-folder-Location-${i}`}
                    />
                  );
                })}
              </div>
            </div>
            <button
              className={documentTreeStyles.backButton}
              onClick={() => {
                setSelectedFoldersToMoveDocument(prevState => {
                  if (prevState.length === 0) return prevState;

                  const current = prevState[prevState.length - 1];
                  let newLength = prevState.length - 1;
                  let secondLastItem = prevState[newLength - 1] || null;

                  if (
                    secondLastItem &&
                    (secondLastItem.name === current?.name ||
                      secondLastItem.listItemId === current?.listItemId)
                  ) {
                    newLength--;
                    secondLastItem = prevState[newLength - 1] || null;
                  }

                  setSelectedFolderToMoveDocument(secondLastItem);

                  const path = secondLastItem
                    ? `${extractPathAfterDocuments(
                        secondLastItem.parentUrl
                      )}/${secondLastItem?.name || ""}`
                    : "/";

                  fetchMoveDocumentFolders(path);

                  return prevState.slice(0, newLength);
                });
              }}
              aria-label={t("go-back-to", {
                name: selectedFolderToMoveDocument?.name,
              })}
            >
              <Icon iconName="arrow-left" size={20} />
              <div>{selectedFolderToMoveDocument?.displayName}</div>
              {selectedFolderToMoveDocument.restricted ? (
                <Chip
                  text={t("restricted")}
                  iconName={"prohibited-icon"}
                  type={ChipType.READONLY}
                  iconHeight={12}
                  iconWidth={12}
                />
              ) : null}
              {isReadonlyFolder(selectedFolderToMoveDocument) && (
                <Chip
                  text={t("read-only")}
                  iconName={"read-only"}
                  type={ChipType.READONLY}
                  iconHeight={8}
                  iconWidth={11.99}
                />
              )}
            </button>
            {shouldShowNotRestrictedMovingFromReadOnlyBanner &&
              movingFromReadOnlyFolderInfoBanner}
            {shouldShowReadOnlyBanner && readOnlyFolderInfoBanner}
            {shouldShowMovingFromReadOnlyBanner &&
              movingFromReadOnlyFolderInfoBanner}
            {selectedFolderToMoveDocument?.restricted &&
              restrictedFolderInfoBanner}
          </div>
        )
      }
    >
      {isAddingNewFolder && selectedFolderToMoveDocument && (
        <CreateNewDocument
          folderPath={selectedFolderToMoveDocument.name}
          setShowAddNewFolder={() => setIsAddingNewFolder(false)}
          getMoveFolderContent={() => {
            fetchSubFolders(selectedFolderToMoveDocument);
          }}
        />
      )}

      {!moveNodes?.isBusy && moveNodes?.data ? (
        renderMoveFolders(moveNodes.data)
      ) : (
        <div className={documentTreeStyles.spinnerContainer}>
          <Spinner isLoading size={"large"} />
        </div>
      )}

      {shouldShowEmptyState && (
        <EmptyState emptyMessage={t("empty-folder")} fullHeight />
      )}
    </Modal>
  );
};
