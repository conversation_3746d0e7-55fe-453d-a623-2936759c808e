using BCP.Data.Models;

namespace BCP.Core.Notifications.Payloads;

public record ActionItemStatusChanged : NotificationPayload, INotificationPayload
{
    public static NotificationType NotificationType => NotificationType.ActionItemStatusChanged;
    public Guid TaskId { get; set; }
    public required string TaskName { get; set; }
    public required string TaskType { get; set; }
    public required string Status { get; set; }
}
