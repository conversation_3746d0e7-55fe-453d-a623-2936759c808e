namespace BCP.Core.Email.Templates;

public class ClientAdminInvite : TemplateModel, ITemplateModel
{
    public static string TemplateName => "ClientAdminInvite";
    public override EmailLayout Layout => EmailLayout.Narrow;

    public override string Subject => Translation.ClientAdminInvite_Subject;
    public override string PreviewText =>
        Translation.ClientAdminInvite_Greeting.Replace("{{name}}", User?.Name);
    public string Greeting =>
        Translation.ClientAdminInvite_Greeting.Replace("{{name}}", User?.Name);
}
