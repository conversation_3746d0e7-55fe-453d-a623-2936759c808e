using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCP.Data.Models;

public enum NotificationEmailStatus
{
    NotApplicable,
    Queued,
    EmailQueued,
    Sent,
    Failed,
    TimedOut,
}

public class Notification
{
    public Guid Id { get; set; }
    public NotificationType NotificationType { get; set; }
    public string? Payload { get; set; }
    public Guid UserId { get; set; }
    public Guid? ClientId { get; set; }
    public Guid? ProjectId { get; set; }
    public Guid? ActorId { get; set; }
    public DateTime? ReadAt { get; set; }
    public NotificationEmailStatus EmailStatus { get; set; }
    public DateTime? EmailProcessedAt { get; set; }
    public DateTime HappenedAt { get; set; } = DateTime.UtcNow;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public bool IsImportant { get; set; } = false;
    public bool IsArchived { get; set; } = false;
    public DateTime? ArchivedAt { get; set; }
    public string? EmailMessageId { get; set; }

    #region Joins
    public User User { get; set; } = null!;
    public Client? Client { get; set; }
    public Project? Project { get; set; }
    public User? Actor { get; set; }
    #endregion


    internal static void ConfigureEntity(EntityTypeBuilder<Notification> builder)
    {
        builder.Property(x => x.Id).HasDefaultValueSql("NEWSEQUENTIALID()");
    }
}
