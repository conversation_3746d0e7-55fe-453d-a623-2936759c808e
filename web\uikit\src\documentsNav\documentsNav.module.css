.parentDiv {
  background-color: white;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.inputParent {
  margin: 0 1rem;
  padding-top: 16px;
}

.inputParent [data-forspacing="forSearchIcon"] {
  top: 12px;
}

.input {
  height: 40px;
  padding: 0 1.75rem 0 1rem;
  border-radius: 100px;
  text-overflow: ellipsis;
  font-size: 0.875rem;
  letter-spacing: -0.1px;
}

.labelParent {
  margin: 0 1rem;
}

.label {
  font-family: var(--primary-font-family);
  font-size: var(--fontsize-body-label);
  line-height: var(--lineheight-body-label);
  color: #666666;
  font-weight: 600;
  text-transform: uppercase;
}

.docNavButton {
  height: 44px;
  flex: 0 1 auto;
  margin: 1rem;
  cursor: pointer;
}

.documentsTitleLink {
  display: flex;
  align-items: center;
  font-size: var(--fontsize-body-small);
  font-weight: 400;
  gap: 0.5rem;
  padding: 0.85rem 0.5rem;
  border-radius: 2px;
  width: 100%;
  color: var(--color-primary-light-charcoal);

  &:hover {
    color: initial;
  }

  &:focus-visible {
    outline-color: var(--color-primary-pale-charcoal);
    outline-width: 0.25rem;
  }
}

.documentsHighlight,
.documentsTitleLink:hover {
  background-color: #f2f2f2;
}

.folders {
  margin-right: 16px;
  flex: 1 1 auto;
  overflow: auto;
  min-height: 0;
}

.folders li {
  display: inline;
}

.spinnerContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
