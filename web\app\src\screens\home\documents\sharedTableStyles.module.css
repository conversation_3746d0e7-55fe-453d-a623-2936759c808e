.selectTrashCell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  justify-content: space-between;
}

.container {
  margin-top: 1rem;
}

.selectCell,
.titleCell,
.filesCell {
  font-family: var(--primary-font-family);
}

.selectCell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  justify-content: space-between;
}

.titleCell {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-grow: 1;
  color: var(--color-primary-light-charcoal);
  font-weight: 600;
}

.titleCellName {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: fit-content;
  cursor: pointer;
  color: var(--color-primary-light-charcoal);
  font-weight: 600;

  .titleOpenIcon {
    visibility: hidden;
  }
}

.titleCellName:hover {
  border-bottom: 1px solid var(--color-primary-light-charcoal);
  margin-bottom: -1px;
  color: var(--color-primary-light-charcoal);

  .titleOpenIcon {
    visibility: visible;
  }
}

.trashCell:hover {
  cursor: default;
  border-color: transparent;
}

.filesCell {
  color: var(--color-bg-inactive-charcoal);
  font-weight: 400;
}

.selectTrashCellFolderIcon,
.selectCellIcon {
  padding: 0.5rem;
  background-color: var(--color-bg-light-grey);
  border: 2px;
}

.originalLocations,
.trashActionItemCellContent {
  width: fit-content;
}

.selectTrashCellIcon,
.selectDocumentCellIcon {
  padding: 0.5rem;
}

.readOnlyChip {
  text-align: -webkit-right;
}

.modifiedCell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
  white-space: nowrap;
  color: var(--color-primary-light-charcoal);
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.initials {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #888;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
}

.actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
  width: 144px;
  gap: 0.25rem;

  svg {
    path {
      fill: var(--color-primary-charcoal);
    }
  }
}

.recycleActions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
  svg {
    path {
      fill: var(--color-primary-charcoal);
    }
  }
}

.sortTableIcon {
  cursor: pointer;
}

.infoButton:hover,
.downloadButton:hover {
  background-color: var(--color-primary-pale-charcoal);
}

.menuButton {
  width: 35px;
  text-align: -webkit-center;
}

.downloadButton,
.infoButton {
  display: flex;
  align-items: center;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  justify-content: center;
  padding: 0.25rem;

  svg {
    flex-shrink: 0;
    margin-top: -3px;
  }

  :focus-visible {
    outline-offset: -5px;
  }
}

.infoButton svg {
  margin-top: -1px;
}

.fileDetails {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: var(--fontsize-body-small);
  font-weight: 400;
  svg {
    height: 4px;
  }
}

.sortableHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  padding: 0;
}

.inputFileType {
  font-family: var(--primary-font-family);
  font-weight: 600;
  text-transform: lowercase;
}

.slottedContainer {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-bottom: 0.5rem;
}

.toastWrapper {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.filtersContainer {
  margin: 0 0 1rem 0;
}

.toastWrapper {
  position: absolute;
  bottom: 10px;
  right: 1.5rem;
  z-index: var(--z-index-modal);
}

.documentItem {
  width: 100%;
}

.folderLink {
  padding: 0;
  &:focus-visible {
    text-decoration: underline;
  }
}

.historyItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid var(--color-primary-pale-charcoal);
  padding: 1rem;
  justify-content: space-between;
  max-width: 100%;

  .leftHandInfo {
    display: flex;
    gap: 0.75rem;
    margin-right: 0.75rem;
  }

  .leftIconWrapper {
    padding: 1rem;
    background-color: var(--color-bg-light-grey);
    border-radius: 2px;
  }

  .documentName {
    display: flex;
    align-items: center;
    word-break: break-word;
  }

  .documentFileType {
    text-transform: lowercase;
  }
}
.trashBannerWrapper {
  margin-top: 1rem;
}

.trashInfoContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--color-bg-light-cobalt);
  font-size: var(--fontsize-body-small);
  padding: 0.75rem 1rem;
  border-radius: 2px;

  svg {
    path {
      fill: var(--color-bg-secondary-cobalt);
    }
  }
}

@media (max-width: 768px) {
  .toastWrapper {
    left: 50%;
    transform: translate(-50%, 0);
    bottom: 1rem;
  }
}

.bulkDownloadModalWrapper {
  position: fixed;
  right: 1.5rem;
  bottom: 1.5rem;
  min-width: 358px;
  z-index: var(--z-index-modal);
}

@media (max-width: 768px) {
  .bulkDownloadModalWrapper {
    left: 50%;
    transform: translate(-50%, 0);
    bottom: 1rem;
  }
}

.truncate {
  /* Line-clamp trick to keep table-layout:auto and truncate text */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
