namespace BCP.Core.Email;

public interface IEmailTranslation
{
    // Notifications
    string Notification_Footer_Note { get; }
    string Notification_Footer_Cta { get; }
    string Notification_Footer_Short_Cta { get; }

    // PortalInvite
    string PortalInvite_Title { get; }
    string PortalInvite_Body { get; }
    string PortalInvite_Cta { get; }

    // ProjectInvite
    string ProjectInvite_Title { get; }
    string ProjectInvite_Title_NULL { get; }
    string ProjectInvite_Body { get; }
    string ProjectInvite_Body_NULL { get; }
    string ProjectInvite_Cta { get; }
    string[] ProjectInvite_Bullets { get; }

    // ClientAdminInvite
    string ClientAdminInvite_Subject { get; }
    string ClientAdminInvite_Greeting { get; }
    string ClientAdminInvite_Body { get; }
    string ClientAdminInvite_Cta { get; }
    string ClientAdminInvite_StepTitle { get; }
    (string, string, string)[] ClientAdminInvite_Steps { get; }
    public string ClientAdminInvite_HelpTitle { get; }
    public string ClientAdminInvite_HelpBody { get; }
    public string ClientAdminInvite_ResourceName { get; }

    // NotificationReminder
    string NotificationReminder_Subject { get; }
    string NotificationReminder_Title { get; }
    string NotificationReminder_DateRange { get; }
    string NotificationReminder_Cta { get; }
    string NotificationReminder_Unsubscribe { get; }

    // OnboardingCompleted
    string OnboardingCompleted_Title { get; }
    string OnboardingCompleted_Heading { get; }
    string OnboardingCompleted_Body { get; }
    string OnboardingCompleted_Cta { get; }
    string OnboardingCompleted_Footer_Note { get; }

    //ActionItem
    string ActionItem_TaskType(string TaskType);
    string ActionItem_TaskTypeIcon(string TaskType); // not translation related
    string ActionItem_TaskStatus(string TaskStatus);
    string ActionItem_TaskStatusIcon(string TaskStatus); // not translation related
    string ActionItem_TaskStatusBadgeWidth(string TaskStatus); // not translation related

    //ActionItemAssigned
    string ActionItemAssigned_Subject { get; }
    string ActionItemAssigned_PreviewText { get; }
    string ActionItemAssigned_Title { get; }
    string ActionItemAssigned_Cta { get; }
    string ActionItemAssigned_DueDate { get; }

    //ActionItemStatusChanged
    string ActionItemStatusChanged_Subject { get; }
    string ActionItemStatusChanged_PreviewText { get; }
    string ActionItemStatusChanged_Title { get; }
    string ActionItemStatusChanged_Cta { get; }

    //FinalDeliveryAdded
    string FinalDeliveryAdded_Subject { get; }
    string FinalDeliveryAdded_Title { get; }
    string FinalDeliveryAdded_Cta { get; }
}
