import React, { useEffect, useState } from "react";
import { TreeRootProps, TreeBranchProps, ChildType } from "./spec";
import { TreeView } from "@ark-ui/react";
import { Icon } from "~/icon";
import styles from "./treeView.module.css";
import classNames from "classnames";
import { Spinner } from "~/spinner";
import { useNavigate, useSearchParams, useLocation } from "react-router-dom";
import { CustomTooltip } from "~/tooltip";
import { useTranslation } from "react-i18next";
import { isReadonlyPath } from "~/utils";

export const getFolderUrl = (baseUrl: string, path?: string) => {
  if (!path) {
    return baseUrl;
  }

  const queryString = new URLSearchParams({ path }).toString();
  return `${baseUrl}?${queryString}`;
};

export const CustomTreeView: React.FC<TreeRootProps> = ({
  baseUrl,
  dataTestId = "uikit-customTreeView",
  ariaLabel,
  ariaDescribedBy,
  ariaLabelledBy,
  isLoading = false,
  updateIsRestricted,
  updateFolderItemId,
  id,
  items,
}) => {
  const [expandedValues, setExpandedValues] = useState<string[]>([]);
  const location = useLocation();

  useEffect(() => {
    const foldersWithDescendants = items
      .filter(
        item =>
          item.type === "folder" &&
          item.descendants &&
          item.descendants.length > 0 &&
          item.descendants.some(
            desc => desc.type === "folder" && !desc.deletionInProgress
          )
      )
      .map(item => item.id);

    setExpandedValues(prevExpanded => {
      const newExpanded = [...prevExpanded];

      foldersWithDescendants.forEach(folderId => {
        if (!newExpanded.includes(folderId)) {
          newExpanded.push(folderId);
        }
      });

      return newExpanded;
    });
  }, [items]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const currentPath = params.get("path") || "";

    const pathSegments = currentPath.split("/").filter(Boolean);
    const ancestorIds: string[] = [];

    let currentItems = items;
    pathSegments.forEach(segment => {
      const found = currentItems.find(item => item.name === segment);
      if (found) {
        ancestorIds.push(found.id);
        currentItems = found.descendants || [];
      }
    });

    setExpandedValues(prevExpanded => {
      const newExpanded = [...prevExpanded];
      ancestorIds.forEach(id => {
        if (!newExpanded.includes(id)) {
          newExpanded.push(id);
        }
      });
      return newExpanded;
    });
  }, [location.search, items]);

  const changeExpandedValues = (id: string) => {
    if (expandedValues.includes(id)) {
      setExpandedValues(expandedValues.filter(value => value != id));
    } else {
      setExpandedValues([...expandedValues, id]);
    }
  };

  return (
    <div data-testid={dataTestId}>
      <TreeView.Root
        className={styles.treeViewRoot}
        aria-label={ariaLabel}
        aria-labelledby={ariaLabelledBy}
        aria-describedby={ariaDescribedBy}
        id={id}
        expandOnClick={false}
        expandedValue={expandedValues}
      >
        <TreeView.Tree>
          {items
            .filter(data => data.type === "folder")
            .map((item, i) => (
              <CustomTreeBranch
                key={`${item.id}-${i}`}
                id={item.id}
                name={item.name}
                baseUrl={baseUrl}
                path={`/${item.name}`}
                isLoading={isLoading}
                updateIsRestricted={updateIsRestricted}
                restricted={item.restricted}
                updateFolderItemId={updateFolderItemId}
                listItemId={item.listItemId}
                type={item.type!}
                descendants={item.descendants}
                displayName={item.displayName}
                readonly={item.readonly || isReadonlyPath(`/${item.name}`)}
                deletionInProgress={item.deletionInProgress}
                changeExpandedValues={changeExpandedValues}
                openTab={expandedValues.indexOf(item.id) >= 0}
              />
            ))}
        </TreeView.Tree>
      </TreeView.Root>
    </div>
  );
};

export const CustomTreeBranch: React.FC<TreeBranchProps> = ({
  id,
  name,
  baseUrl,
  path,
  isLoading,
  updateIsRestricted,
  restricted,
  updateFolderItemId,
  listItemId = 0,
  type,
  descendants,
  displayName,
  readonly,
  changeExpandedValues,
  openTab,
  deletionInProgress,
}) => {
  const [params] = useSearchParams();
  const { t } = useTranslation("documents");
  const navigate = useNavigate();
  const [folderDescendants, setFolderDescendants] = useState<boolean>(false);

  useEffect(() => {
    if (descendants) {
      const foldersNotBeingDeleted = descendants.filter(
        folder => folder.type == ChildType.FOLDER && !folder.deletionInProgress
      );
      if (foldersNotBeingDeleted.length > 0) {
        setFolderDescendants(true);
      } else {
        setFolderDescendants(false);
      }
    }
  }, [descendants]);

  const values = Array.from(params.values());
  const lastSegment = values[values.length - 1]?.split("/").pop() ?? "";

  const shouldHighlight = lastSegment === name;

  const onFolderClick = (event: React.MouseEvent | React.KeyboardEvent) => {
    if (isLoading) {
      event.preventDefault();
      return;
    }
    navigate(getFolderUrl(baseUrl, path));
    updateIsRestricted(restricted);
    updateFolderItemId(listItemId);
  };

  if (deletionInProgress) return null;

  return (
    <TreeView.Branch value={id}>
      <TreeView.BranchControl asChild>
        <button
          onClick={event => onFolderClick(event)}
          onKeyDown={e => {
            if (e.key === "Enter") {
              onFolderClick(e);
            }
            if (e.key === "ArrowRight" && !openTab && folderDescendants) {
              changeExpandedValues(id);
            } else if (e.key === "ArrowLeft" && openTab) {
              changeExpandedValues(id);
            }
          }}
          className={classNames(styles.branch, {
            [styles.focusableBranch]: !isLoading,
            [styles.disabledLink]: isLoading && !shouldHighlight,
          })}
          aria-label={name}
        >
          <div
            className={classNames(styles.indicator, {
              [styles.treeViewSelected]: shouldHighlight,
            })}
          >
            {type == ChildType.FOLDER && (
              <div className={styles.icons}>
                {folderDescendants && (
                  <div
                    className={
                      !openTab ? styles.dropdown : styles.dropdownRotated
                    }
                    onClick={event => {
                      event.stopPropagation();
                      changeExpandedValues(id);
                    }}
                  >
                    <Icon iconName="chevron-right" height={12} width={12} />
                  </div>
                )}
                <div
                  className={styles.folder}
                  onClick={event => {
                    event.stopPropagation();
                    onFolderClick(event);
                  }}
                >
                  <Icon iconName="folder-icon-filled" />
                </div>
              </div>
            )}
            <div className={styles.labelParent}>
              <TreeView.BranchText
                className={classNames({
                  [styles.label]: !descendants,
                })}
              >
                {displayName}
              </TreeView.BranchText>
              <div
                className={
                  restricted && readonly ? styles.restrictedAndReadonly : ""
                }
              >
                {restricted && (
                  <CustomTooltip
                    message={"Restricted"}
                    inputId={`${id}-restricted`}
                    direction="down"
                    openDelay={100}
                    closeDelay={100}
                  >
                    <Icon iconName={"lock-icon"} size={12} />
                  </CustomTooltip>
                )}
                {readonly && (
                  <CustomTooltip
                    message={t("read-only")}
                    inputId={`${id}-readonly`}
                    direction="down"
                    openDelay={100}
                    closeDelay={100}
                  >
                    <Icon iconName="read-only" size={12}></Icon>
                  </CustomTooltip>
                )}
              </div>
            </div>
          </div>
        </button>
      </TreeView.BranchControl>
      <TreeView.BranchContent>
        {isLoading && shouldHighlight ? (
          <div className={styles.loading}>
            <Spinner isLoading={true} />
          </div>
        ) : descendants && descendants.length > 0 ? (
          descendants
            .filter(data => data.type === "folder")
            .map((child: TreeBranchProps) => {
              const childPath = `${path}/${child.name}`;

              return (
                <CustomTreeBranch
                  key={child.id}
                  name={child.name}
                  displayName={child.displayName}
                  id={child.id}
                  descendants={child.descendants}
                  type={child.type}
                  baseUrl={baseUrl}
                  path={childPath}
                  dataTestId="uikit-customTreeViewChild"
                  readonly={child.readonly || isReadonlyPath(childPath)}
                  restricted={child.restricted}
                  isLoading={isLoading}
                  deletionInProgress={child.deletionInProgress}
                  updateIsRestricted={updateIsRestricted}
                  updateFolderItemId={updateFolderItemId}
                  listItemId={listItemId}
                  changeExpandedValues={changeExpandedValues}
                  openTab={openTab}
                />
              );
            })
        ) : (
          shouldHighlight &&
          isLoading && (
            <div className={styles.loading}>
              <Spinner isLoading={true} />
            </div>
          )
        )}
      </TreeView.BranchContent>
    </TreeView.Branch>
  );
};
