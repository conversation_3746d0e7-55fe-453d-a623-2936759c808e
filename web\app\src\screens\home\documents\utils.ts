import { ChildType, isReadonlyPath } from "@bcp/uikit";
import { DocumentItem, DocumentRow, DocumentTypeEnum } from "./spec";
import { FileKind, INode } from "~/services/document";
import { snakeCase } from "lodash";
import { SignatureRequestDocumentStatus } from "~/screens/practitioner/shared/spec";
import { UrlUtils } from "~/utils/generic/urlUtils";

const MAX_FOLDER_NAME_SIZE = 100;
const MAX_FOLDER_DEPTH = 5;

export const SPECIAL_FOLDER_NAMES = [
  "Deliverables",
  "Final Deliverables",
  "Draft Deliverables",
];

export const BOTTOM_PRIORITY_FOLDERS = ["archive"];

export enum ConflictTypes {
  FileAlreadyExists = "FileAlreadyExists",
  FolderContainsRestrictedFiles = "FolderContainsRestrictedFiles",
}

export const normalizeActionItemFileType = (
  fileType: string | undefined
): DocumentTypeEnum => {
  if (!fileType) {
    console.warn("File type is undefined");
    return DocumentTypeEnum.PDF;
  }
  const normalizedFileType = fileType.toUpperCase();
  switch (normalizedFileType) {
    case "PDF":
      return DocumentTypeEnum.PDF;
    case "DOC":
    case "DOCX":
    case "DOCM":
    case "RTF":
      return DocumentTypeEnum.DOCX;
    case "PNG":
    case "JPG":
    case "JPEG":
    case "GIF":
      return DocumentTypeEnum.PNG;
    case "XLS":
    case "XLSX":
    case "XLSM":
    case "XLM":
    case "CSV":
      return DocumentTypeEnum.CSV;
    case "PPT":
    case "PPTX":
    case "PPTM":
      return DocumentTypeEnum.PPT;
    case "HTML":
    case "HTM":
    case "XSL":
    case "XSLT":
    case "XML":
      return DocumentTypeEnum.CODE;
    case "ZIP":
    case "ZIPX":
    case "RAR":
    case "7Z":
      return DocumentTypeEnum.ARCHIVE;
    case "MSG":
    case "EML":
      return DocumentTypeEnum.EMAIL;
    case "LOG":
    case "TXT":
      return DocumentTypeEnum.UNKNOWN;
    case "VSD":
    case "VSDX":
      return DocumentTypeEnum.VISIO;
    case "MP4":
      return DocumentTypeEnum.VIDEO;
    case "XPS":
    case "GME":
    case "PREVIEW":
    case "QBW":
      return DocumentTypeEnum.UNKNOWN;
    default:
      return DocumentTypeEnum.UNKNOWN;
  }
};

export const transformDocumentData = (data: INode[]): DocumentItem[] => {
  const transformedItems = data
    .map((item): DocumentItem | undefined => {
      if (!item.displayName) {
        console.warn("Skipping invalid item:", item);
        return;
      }

      const fileType =
        item.fileKind?.toLowerCase() === "folder"
          ? ChildType.FOLDER
          : ChildType.FILE;

      return {
        id: item.driveItemId,
        name: item.name,
        displayName: item.displayName,
        type: fileType,
        createdAt: item.createdAt,
        createdBy: item.createdByName,
        modifiedAt: item.modifiedAt,
        modifiedBy: item.modifiedByName,
        restricted: item.restricted,
        readonly: item.readonly,
        size: fileType === ChildType.FILE ? item.size : undefined,
        descendants: [],
        listItemId: item.listItemId,
      };
    })
    .filter(item => !!item);

  const bottomPriorityFolders = transformedItems.filter(
    item =>
      item.type === ChildType.FOLDER &&
      BOTTOM_PRIORITY_FOLDERS.includes(item.name.toLowerCase())
  );

  const standardPriorityItems = transformedItems.filter(
    item =>
      !(
        item.type === ChildType.FOLDER &&
        BOTTOM_PRIORITY_FOLDERS.includes(item.name.toLowerCase())
      )
  );

  return [...standardPriorityItems, ...bottomPriorityFolders];
};

export const mapApiToDocumentRows = (apiData: INode[]): DocumentRow[] => {
  return apiData.map(
    (item): DocumentRow => ({
      id: item.listItemId.toString(),
      name: item.name,
      title: item.displayName,
      files: item.fileCount, // For backward compatibility
      fileType:
        item.name
          .split(".")
          .pop()
          ?.toUpperCase() || "UNKNOWN",
      size: item.size,
      modifiedAt: item.modifiedAt,
      modifiedByName: item.modifiedByName,
      createdAt: item.createdAt,
      createdByName: item.createdByName,
      originalLocation: item.parentUrl,
      actionItemName: item.actionItem?.name,
      url: item.url,
      folderOrFile: item.fileKind,
      driveItemId: item.driveItemId,
      listItemId: item.listItemId,
      readonly: item.readonly,
      restricted: item.restricted,
      deletionInProgress: item.deletionInProgress,
      isTemplated: item.isTemplated,
      requestSignature: item.requestSignature,
      fileCount: item.fileCount,
      subfolderCount: item.subfolderCount,
    })
  );
};

/**
 * Generates the count text to display below the folder name
 * @param fileCount - Number of direct files in the folder
 * @param subfolderCount - Number of direct subfolders in the folder
 * @param t - Translation function from react-i18next
 * @returns String with the appropriate format (e.g., "3 files", "1 folder, 5 files")
 */
export const generateFolderCountText = (
  fileCount?: number,
  subfolderCount?: number,
  t?: (key: string) => string
): string => {
  if (fileCount === undefined && subfolderCount === undefined) {
    return "";
  }

  const hasFiles = fileCount !== undefined && fileCount > 0;
  const hasSubfolders = subfolderCount !== undefined && subfolderCount > 0;

  if (!hasFiles && !hasSubfolders) {
    // For empty folders, show "Empty folder" or "0 files"
    const emptyText = t ? t("empty-folder") : "Empty folder";
    return emptyText;
  }

  const parts: string[] = [];

  if (hasSubfolders) {
    const folderText = t
      ? subfolderCount === 1
        ? t("folder")
        : t("folders")
      : subfolderCount === 1
      ? "folder"
      : "folders";
    parts.push(`${subfolderCount} ${folderText}`);
  }

  if (hasFiles) {
    const fileText = t
      ? fileCount === 1
        ? t("file")
        : t("files")
      : fileCount === 1
      ? "file"
      : "files";
    parts.push(`${fileCount} ${fileText}`);
  }

  return parts.join(", ");
};

export const getPathFromUrl = (): string => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("path") ?? "/";
};

export const getPathFromNode = ({ url }: { url?: string }): string => {
  if (!url) {
    return "/";
  }

  const documentsIndex = url.indexOf("/Documents");

  if (documentsIndex === -1) {
    return "/";
  }

  return url.slice(documentsIndex + "/Documents".length) ?? "/";
};

export const getFileExtension = (filename: string): string | null => {
  const parts = filename.split(".");
  return parts.length > 1 ? parts.pop() ?? null : null;
};

export const getFileNameWithoutExtension = (filename: string): string => {
  return filename.substring(0, filename.lastIndexOf(".")) || filename;
};

export function nonEmpty<T>(value: T): value is NonNullable<T> {
  return !!value;
}

export const isFolderNameValid = (inputString: string): boolean => {
  const pattern = /^[^\\*:<>?/~|"]*[^\\*:<>?/~|."]$/; // cannot contain restricted characters, or end with "."
  return pattern.test(inputString) && inputString.length < MAX_FOLDER_NAME_SIZE;
};

export function getSelectedFilesOrFolders(
  selectedDocument: string[],
  documents: DocumentRow[]
): DocumentRow[] {
  return documents.filter(doc => selectedDocument.includes(doc.id));
}

const isDocumentRow = (node: INode | DocumentRow): node is DocumentRow =>
  "folderOrFile" in node;

const isFolder = (node: INode | DocumentRow) =>
  isDocumentRow(node)
    ? node.folderOrFile === "Folder"
    : node.fileKind === FileKind.Folder;

export const isReadonlyFolder = (node: INode | DocumentRow): boolean => {
  return isFolder(node) && isReadonly(node);
};

export const isReadonly = (node: INode | DocumentRow): boolean =>
  node.readonly || isReadonlyPath(getPathFromNode(node));

export const isTemplatedFolder = (node: INode | DocumentRow): boolean => {
  return isFolder(node) && node.isTemplated;
};

export const hasTemplatedFolder = (documents: DocumentRow[]) =>
  documents.some(doc => isTemplatedFolder(doc));

export const hasReadonly = (documents: DocumentRow[]) =>
  documents.some(doc => isReadonlyPath(getPathFromNode(doc)));

export const hasRestricted = (documents: DocumentRow[]): boolean =>
  documents.some(doc => doc.restricted);

export const fileType = (node: INode) => {
  return node.name
    .split(".")
    .pop()
    ?.toUpperCase();
};

export const getLastFolderName = (path: string): string | null => {
  if (!path) return null;

  const cleanedPath = path.replace(/\/$/, "");

  const parts = cleanedPath.split("/");
  return parts.length > 0 ? parts[parts.length - 1] : null;
};

export const extractPathAfterDocuments = (fullPath: string): string => {
  const match = fullPath.match(/\/Documents(\/.*)/);
  return match ? match[1] : "";
};

export const getRelativePath = (
  { title }: DocumentRow,
  params: URLSearchParams
): string => {
  let path = params.get("path");

  if (!path) {
    return `/${title}`;
  } else {
    if (path.endsWith("/")) {
      path = path.slice(0, path.length - 1);
    }

    return `${path}/${title}`;
  }
};

export const canCreateSubfolder = (path: string): boolean => {
  const levels = path.split("/").filter(Boolean).length;
  return levels < MAX_FOLDER_DEPTH;
};

export const isCurrent = (
  selectedDocument: DocumentRow,
  selectedFolderForMove: INode
) =>
  getLastFolderName(selectedDocument.originalLocation || "") ===
  selectedFolderForMove?.name;

// APT Export validation
const APT_SUPPORTED_EXTENSIONS = [
  'doc', 'docm', 'docx', 'dot', 'dotm', 'dotx', 'htm', 'html', 'msg', 'pdf',
  'rtf', 'txt', 'wpd', 'xps', 'bmp', 'gif', 'jpg', 'jpeg', 'png', 'tif',
  'tiff', 'pot', 'potx', 'pps', 'ppt', 'pptm', 'pptx', 'csv', 'xls', 'xlsm', 'xlsx'
];

export interface AptExportValidationResult {
  isValid: boolean;
  errorMessage?: string;
  invalidFiles?: string[];
}

export interface DocumentValidationError {
  type: 'invalid_file_type' | 'file_size_exceeded' | 'invalid_file_name';
  message: string;
}

export interface ValidatedDocument extends DocumentRow {
  errors: DocumentValidationError[];
  isValid: boolean;
}

export const validateDocumentForAptExport = (document: DocumentRow): ValidatedDocument => {
  const errors: DocumentValidationError[] = [];

  // Skip validation for folders - they are always allowed
  if (document.folderOrFile === 'Folder') {
    return {
      ...document,
      errors: [],
      isValid: true,
    };
  }

  // Validate file extension
  const extension = document.fileType?.toLowerCase();
  if (!extension || !APT_SUPPORTED_EXTENSIONS.includes(extension)) {
    errors.push({
      type: 'invalid_file_type',
      message: 'Invalid file type',
    });
  }

  // Validate file size (50MB limit = 52,428,800 bytes)
  const maxSizeBytes = 50 * 1024 * 1024; // 50MB
  if (document.size && document.size > maxSizeBytes) {
    errors.push({
      type: 'file_size_exceeded',
      message: 'File exceeds maximum file size limitation (50MB)',
    });
  }

  return {
    ...document,
    errors,
    isValid: errors.length === 0,
  };
};

export const validateFilesForAptExport = (documents: DocumentRow[]): AptExportValidationResult => {
  if (documents.length === 0) {
    return {
      isValid: false,
      errorMessage: "No files or folders selected for export.",
    };
  }

  // Check file extensions only for files (folders are always allowed)
  const invalidFiles: string[] = [];

  documents.forEach(doc => {
    // Skip validation for folders - they are always allowed
    if (doc.folderOrFile.toLowerCase() === 'folder') {
      return;
    }

    // Validate file extensions for files only
    const extension = doc.fileType?.toLowerCase();
    if (!extension || !APT_SUPPORTED_EXTENSIONS.includes(extension)) {
      invalidFiles.push(doc.title);
    }
  });

  if (invalidFiles.length > 0) {
    return {
      isValid: false,
      errorMessage: `The following files have unsupported file types for APT export: ${invalidFiles.join(', ')}. Supported types: ${APT_SUPPORTED_EXTENSIONS.join(', ')}.`,
      invalidFiles,
    };
  }

  return {
    isValid: true,
  };
};

export const getSecondLastItem = (node: INode[], newLength: number) => {
  const current = node[node.length - 1];
  let secondLastItem = node[newLength - 1] || null;

  if (
    secondLastItem &&
    (secondLastItem.name === current?.name ||
      secondLastItem.listItemId === current?.listItemId)
  ) {
    newLength--;
    secondLastItem = node[newLength - 1] || null;
  }
  return secondLastItem;
};

export const isBulkSelectionValid = (
  documents: DocumentRow[],
  isClient: boolean
): boolean =>
  // clients can't move if any item is readonly
  // non-clients can't move if any one of the templated folders is part of the selection
  documents.every(doc => {
    return isClient ? !isReadonly(doc) : !isTemplatedFolder(doc);
  });

export const getLocalizedFolderKeyName = (name: string) => {
  return `folder_${snakeCase(name)}`;
};

export const hasActiveSignatureRequest = (document: DocumentRow) => {
  return document.requestSignature == SignatureRequestDocumentStatus.InProgress;
};

export const generateFileDownloadUrl = (
  spoUrl: string,
  documentUrl: string
) => {
  const _downloadPath = `/_layouts/15/download.aspx?SourceUrl=${encodeURIComponent(
    documentUrl ?? ""
  )}`;
  return UrlUtils.join(spoUrl, _downloadPath);
};
