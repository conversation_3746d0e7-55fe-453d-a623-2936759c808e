import React from "react";
import { Splitter as ArkSplitter } from "@ark-ui/react";
import styles from "./splitter.module.css";
import classNames from "classnames";
import { SizeChangeDetails } from "@ark-ui/react/dist/components/splitter/splitter";

export interface SplitterProps {
  leftPanel: React.ReactNode;
  rightPanel: React.ReactNode;
  defaultSizes?: [number, number];
  maxLimits?: [number, number];
  className?: string;
  dataTestId?: string;
  panelOverflow?: "auto" | "visible";
}

export const Splitter: React.FC<SplitterProps> = ({
  leftPanel,
  rightPanel,
  defaultSizes = [30, 70],
  maxLimits = [80, 100],
  className,
  dataTestId = "uikit-splitter",
  panelOverflow = "auto",
}) => {
  const [sizes, setSizes] = React.useState(defaultSizes);

  const handleSizeChange = (details: SizeChangeDetails) => {
    const newSizes = details.size
      .map(s => s.size)
      .filter((size): size is number => size !== undefined);

    setSizes(newSizes as [number, number]);
  };

  const handleSizeChangeEnd = (details: SizeChangeDetails) => {
    const newSizes = details.size
      .map(s => s.size)
      .filter((size): size is number => size !== undefined);

    let [leftSize, rightSize] = newSizes;

    if (leftSize > maxLimits[0]) {
      leftSize = maxLimits[0];
      rightSize = 100 - leftSize;
    }

    if (rightSize > maxLimits[1]) {
      rightSize = maxLimits[1];
      leftSize = 100 - rightSize;
    }

    setSizes([leftSize, rightSize] as [number, number]);
  };

  return (
    <ArkSplitter.Root
      className={classNames(styles.splitter, className)}
      data-testid={dataTestId}
      size={[
        { id: "left-panel", size: sizes[0] },
        { id: "right-panel", size: sizes[1] },
      ]}
      onSizeChange={handleSizeChange}
      onSizeChangeEnd={handleSizeChangeEnd}
    >
      <ArkSplitter.Panel
        className={classNames(
          styles.panel,
          panelOverflow === "visible" && styles.noOverflow
        )}
        id="left-panel"
      >
        {leftPanel}
      </ArkSplitter.Panel>

      <ArkSplitter.ResizeTrigger
        className={styles.resizeTrigger}
        id="left-panel:right-panel"
      />

      <ArkSplitter.Panel className={styles.panel} id="right-panel">
        {rightPanel}
      </ArkSplitter.Panel>
    </ArkSplitter.Root>
  );
};
