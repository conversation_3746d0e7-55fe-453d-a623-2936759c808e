.container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  margin-bottom: 0.5rem;
}

.row {
  display: grid;
  grid-template-columns: 1.5rem 1fr 1.5rem;
  align-items: center;
  gap: 0.5rem;
}

.order {
  text-align: center;
}

.deleteButton {
  color: var(--color-secondary-burgundy);
  padding: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deleteButton:focus-visible {
  outline: 2px solid var(--color-secondary-cobalt);
  outline-offset: 2px;
  border-radius: 2px;
}

.addButton {
  padding: 0;
  align-self: flex-start;
}

.error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
}

.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
