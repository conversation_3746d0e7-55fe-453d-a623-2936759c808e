@using BCP.Core.Email.Translations
@model BCP.Core.Email.Templates.ActionItemAssigned
@using Microsoft.AspNetCore.Html

@{
    Layout = "Common/Layout.cshtml";
    var T = Model.Translation;
}

<table role="presentation" cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td style="padding: 0 0 24px 0;">
            <h1 style="margin:0; color:#333; font-size:16px; font-style:normal; font-weight:400; line-height:24px;">
                @((HtmlString)new HtmlString(Model.Title))
            </h1>
        </td>
    </tr>
    <tr>
        <td style="padding: 32px; border-width: 1px; border-radius: 2px; border-color: #e7e7e7; border-style: solid;">
            <p style="margin:0; color:#666; font-size:14px; font-style:normal; font-weight:400; line-height:20px;">
                @Model.Project?.Name
            </p>
            <p
                style="margin:8px 0 0; color:#333; font-size:18px; font-style:normal; font-weight:600; line-height:24px;">
                @Model.TaskName
            </p>
            <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                <tr>
                    <td style="padding: 16px 0 0;">
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0"
                            style="background-color:#f2f2f2; border-radius: 2px;">
                            <tr>
                                <td style="padding:4px 8px 4px 4px;">
                                    <table role="presentation" cellpadding="0" cellspacing="0" border="0">
                                        <tr>
                                            <td style="vertical-align: middle;">
                                                <img src="@($"{Model.AssetUrl}/{Model.TaskTypeIcon}.png")"
                                                    alt="@Model.TaskTypeIcon" width="16" height="16"
                                                    style="width:16px; height:16px; border:0; display:block;" />
                                            </td>
                                            <td style="padding-left:8px; vertical-align: middle;">
                                                <p
                                                    style="padding: 0; margin: 0; color:#333; font-size:14px; font-weight:400; line-height:14px; mso-line-height-rule:exactly; font-family:Arial, sans-serif;">
                                                    @Model.DisplayedTaskType
                                                </p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            @if (@Model.DisplayedDueDate != "")
            {
                <table>
                    <td style="padding: 16px 0 0;">
                        <p
                            style="color:#333; font-size:16px; font-style:normal; font-weight:400; line-height:24px; padding: 0; margin: 0;">
                            @Model.DisplayedDueDate
                        </p>
                    </td>
                </table>
            }
            <!--[if mso]>
                    <table cellpadding="0" cellspacing="0" role="presentation" border="0" style="border-collapse:collapse;">
                            <tr>
                                <td style="padding: 24px 0 0;">
                                <v:roundrect
                                    xmlns:v="urn:schemas-microsoft-com:vml"
                                    href="@Model.PortalUrl/client/@Model.Client?.Id/project/@Model.Project?.Id/action-items/@Model.TaskId?source=email"
                                    style="height:56px; v-text-anchor:middle; width:200px; padding:0 24px;"
                                    arcsize="2%"
                                    strokecolor="#E81A3B"
                                    fillcolor="#E81A3B"
                                >
                                    <w:anchorlock/>
                                    <center style="color:#ffffff; font-family:Arial, sans-serif; font-size:16px; font-weight:400; mso-line-height-rule: exactly;">
                                    @T.ActionItemAssigned_Cta &rarr;
                                    </center>
                                </v:roundrect>
                                </td>
                            </tr>
                    </table>
            <![endif]-->
            <!--[if !mso]><!-- -->
            <table role="presentation" cellpadding="0" cellspacing="0" style="margin: 24px 0 0;">
                <tr>
                    <td style="background:#e81a3b; padding:8px 16px; border-radius:2px;">
                        <a href="@Model.PortalUrl/client/@Model.Client?.Id/project/@Model.Project?.Id/action-items/@Model.TaskId?source=email"
                            target="_blank"
                            style="color:#fff; text-decoration:none; font-size:16px; line-height:24px; font-weight:300;">
                            @T.ActionItemAssigned_Cta &rarr;
                        </a>
                    </td>
                </tr>
            </table>
            <!--<![endif]-->
        </td>
    </tr>
</table>

@section Footer {
    <p style="font-size:12px; line-height:18px; color:#666; margin: 0;">
        @T.Notification_Footer_Note <a href="@Model.PortalUrl/settings/notifications">@T.Notification_Footer_Cta</a>.
    </p>
}
